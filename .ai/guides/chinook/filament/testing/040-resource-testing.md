# Resource Testing Guide

This guide covers comprehensive testing strategies for Filament resources in the Chinook admin panel, including CRUD operations, authorization, validation, and advanced features.

## Table of Contents

- [Overview](#overview)
- [Test Structure](#test-structure)
- [CRUD Operation Testing](#crud-operation-testing)
- [Authorization Testing](#authorization-testing)
- [Validation Testing](#validation-testing)
- [Relationship Testing](#relationship-testing)
- [Advanced Feature Testing](#advanced-feature-testing)
- [Performance Testing](#performance-testing)

## Overview

Resource testing ensures that all Filament resources function correctly, maintain proper security, and provide the expected user experience. This includes testing all CRUD operations, form validation, table functionality, and access control.

### Testing Objectives

- **Functionality**: Verify all CRUD operations work correctly
- **Security**: Ensure proper authorization and access control
- **Validation**: Test form validation rules and error handling
- **Performance**: Validate resource performance under load
- **User Experience**: Test filtering, searching, and sorting

## Test Structure

### Base Resource Test Class

Create a base class for common resource testing functionality:

```php
<?php

namespace Tests\Feature\ChinookAdmin\Resources;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

abstract class ResourceTestCase extends TestCase
{
    use RefreshDatabase;

    protected User $adminUser;
    protected User $editorUser;
    protected User $guestUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->adminUser = User::factory()->create();
        $this->adminUser->assignRole('Admin');
        
        $this->editorUser = User::factory()->create();
        $this->editorUser->assignRole('Editor');
        
        $this->guestUser = User::factory()->create();
        $this->guestUser->assignRole('Guest');
    }

    /**
     * Get the resource URL path.
     */
    abstract protected function getResourcePath(): string;

    /**
     * Get valid data for creating a record.
     */
    abstract protected function getValidCreateData(): array;

    /**
     * Get valid data for updating a record.
     */
    abstract protected function getValidUpdateData(): array;

    /**
     * Get invalid data for testing validation.
     */
    abstract protected function getInvalidData(): array;

    /**
     * Test that admin can access the resource index.
     */
    public function test_admin_can_access_index(): void
    {
        $this->actingAs($this->adminUser)
            ->get("/chinook-admin/{$this->getResourcePath()}")
            ->assertStatus(200);
    }

    /**
     * Test that unauthorized users cannot access the resource.
     */
    public function test_unauthorized_user_cannot_access_resource(): void
    {
        $this->actingAs($this->guestUser)
            ->get("/chinook-admin/{$this->getResourcePath()}")
            ->assertStatus(403);
    }
}
```

### Artist Resource Test Example

```php
<?php

namespace Tests\Feature\ChinookAdmin\Resources;

use App\Models\Artist;
use App\Models\Album;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class ArtistResourceTest extends ResourceTestCase
{
    protected function getResourcePath(): string
    {
        return 'artists';
    }

    protected function getValidCreateData(): array
    {
        return [
            'name' => 'Test Artist',
            'country' => 'US',
            'biography' => 'Test biography',
            'website' => 'https://example.com',
            'formed_year' => 2020,
            'is_active' => true,
            'social_links' => [
                ['platform' => 'facebook', 'url' => 'https://facebook.com/artist'],
                ['platform' => 'twitter', 'url' => 'https://twitter.com/artist'],
            ],
        ];
    }

    protected function getValidUpdateData(): array
    {
        return [
            'name' => 'Updated Artist Name',
            'country' => 'CA',
            'biography' => 'Updated biography',
        ];
    }

    protected function getInvalidData(): array
    {
        return [
            'name' => '', // Required field
            'website' => 'invalid-url',
            'formed_year' => 1800, // Too early
            'social_links' => [
                ['platform' => '', 'url' => 'https://example.com'], // Missing platform
            ],
        ];
    }
}
```

## CRUD Operation Testing

### Create Operation Testing

```php
class ArtistResourceTest extends ResourceTestCase
{
    public function test_admin_can_create_artist(): void
    {
        $artistData = $this->getValidCreateData();

        $this->actingAs($this->adminUser)
            ->post("/chinook-admin/{$this->getResourcePath()}", $artistData)
            ->assertRedirect();

        $this->assertDatabaseHas('artists', [
            'name' => 'Test Artist',
            'country' => 'US',
        ]);

        $artist = Artist::where('name', 'Test Artist')->first();
        $this->assertNotNull($artist->public_id);
        $this->assertNotNull($artist->slug);
        $this->assertEquals($this->adminUser->id, $artist->created_by);
    }

    public function test_editor_can_create_artist(): void
    {
        $artistData = $this->getValidCreateData();

        $this->actingAs($this->editorUser)
            ->post("/chinook-admin/{$this->getResourcePath()}", $artistData)
            ->assertRedirect();

        $this->assertDatabaseHas('artists', [
            'name' => 'Test Artist',
        ]);
    }

    public function test_guest_cannot_create_artist(): void
    {
        $artistData = $this->getValidCreateData();

        $this->actingAs($this->guestUser)
            ->post("/chinook-admin/{$this->getResourcePath()}", $artistData)
            ->assertStatus(403);

        $this->assertDatabaseMissing('artists', [
            'name' => 'Test Artist',
        ]);
    }

    public function test_create_artist_with_media(): void
    {
        Storage::fake('public');
        $file = UploadedFile::fake()->image('artist.jpg');

        $artistData = array_merge($this->getValidCreateData(), [
            'profile_image' => $file,
        ]);

        $this->actingAs($this->adminUser)
            ->post("/chinook-admin/{$this->getResourcePath()}", $artistData)
            ->assertRedirect();

        $artist = Artist::where('name', 'Test Artist')->first();
        $this->assertTrue($artist->hasMedia('profile_images'));
    }
}
```

### Read Operation Testing

```php
public function test_admin_can_view_artist_details(): void
{
    $artist = Artist::factory()->create();

    $this->actingAs($this->adminUser)
        ->get("/chinook-admin/{$this->getResourcePath()}/{$artist->id}")
        ->assertStatus(200)
        ->assertSee($artist->name)
        ->assertSee($artist->country);
}

public function test_artist_index_displays_correct_data(): void
{
    $artists = Artist::factory()->count(3)->create();

    $response = $this->actingAs($this->adminUser)
        ->get("/chinook-admin/{$this->getResourcePath()}");

    $response->assertStatus(200);
    
    foreach ($artists as $artist) {
        $response->assertSee($artist->name);
    }
}

public function test_artist_index_pagination(): void
{
    Artist::factory()->count(30)->create();

    $this->actingAs($this->adminUser)
        ->get("/chinook-admin/{$this->getResourcePath()}")
        ->assertStatus(200)
        ->assertSee('Next')
        ->assertSee('Previous');
}
```

### Update Operation Testing

```php
public function test_admin_can_update_artist(): void
{
    $artist = Artist::factory()->create();
    $updateData = $this->getValidUpdateData();

    $this->actingAs($this->adminUser)
        ->put("/chinook-admin/{$this->getResourcePath()}/{$artist->id}", $updateData)
        ->assertRedirect();

    $this->assertDatabaseHas('artists', [
        'id' => $artist->id,
        'name' => 'Updated Artist Name',
        'country' => 'CA',
    ]);

    $artist->refresh();
    $this->assertEquals($this->adminUser->id, $artist->updated_by);
}

public function test_editor_can_update_artist(): void
{
    $artist = Artist::factory()->create();
    $updateData = $this->getValidUpdateData();

    $this->actingAs($this->editorUser)
        ->put("/chinook-admin/{$this->getResourcePath()}/{$artist->id}", $updateData)
        ->assertRedirect();

    $this->assertDatabaseHas('artists', [
        'id' => $artist->id,
        'name' => 'Updated Artist Name',
    ]);
}

public function test_guest_cannot_update_artist(): void
{
    $artist = Artist::factory()->create();
    $updateData = $this->getValidUpdateData();

    $this->actingAs($this->guestUser)
        ->put("/chinook-admin/{$this->getResourcePath()}/{$artist->id}", $updateData)
        ->assertStatus(403);

    $this->assertDatabaseHas('artists', [
        'id' => $artist->id,
        'name' => $artist->name, // Original name unchanged
    ]);
}
```

### Delete Operation Testing

```php
public function test_admin_can_delete_artist(): void
{
    $artist = Artist::factory()->create();

    $this->actingAs($this->adminUser)
        ->delete("/chinook-admin/{$this->getResourcePath()}/{$artist->id}")
        ->assertRedirect();

    $this->assertSoftDeleted($artist);
    
    $artist->refresh();
    $this->assertFalse($artist->is_active);
}

public function test_editor_cannot_delete_artist(): void
{
    $artist = Artist::factory()->create();

    $this->actingAs($this->editorUser)
        ->delete("/chinook-admin/{$this->getResourcePath()}/{$artist->id}")
        ->assertStatus(403);

    $this->assertNotSoftDeleted($artist);
}

public function test_delete_artist_with_albums_fails(): void
{
    $artist = Artist::factory()->create();
    Album::factory()->create(['artist_id' => $artist->id]);

    $this->actingAs($this->adminUser)
        ->delete("/chinook-admin/{$this->getResourcePath()}/{$artist->id}")
        ->assertSessionHasErrors();

    $this->assertNotSoftDeleted($artist);
}
```

## Authorization Testing

### Role-Based Access Testing

```php
public function test_role_based_access_control(): void
{
    $artist = Artist::factory()->create();

    // Admin can do everything
    $this->assertCanAccessResource($this->adminUser, $this->getResourcePath());
    $this->assertCanCreateRecord($this->adminUser, $this->getResourcePath(), $this->getValidCreateData());
    $this->assertCanEditRecord($this->adminUser, $this->getResourcePath(), $artist, $this->getValidUpdateData());
    $this->assertCanDeleteRecord($this->adminUser, $this->getResourcePath(), $artist);

    // Editor can view and edit but not delete
    $this->assertCanAccessResource($this->editorUser, $this->getResourcePath());
    $this->assertCanCreateRecord($this->editorUser, $this->getResourcePath(), $this->getValidCreateData());
    $this->assertCanEditRecord($this->editorUser, $this->getResourcePath(), $artist, $this->getValidUpdateData());
    $this->assertCannotDeleteRecord($this->editorUser, $this->getResourcePath(), $artist);

    // Guest cannot access
    $this->assertCannotAccessResource($this->guestUser, $this->getResourcePath());
}

public function test_ownership_based_access(): void
{
    $user1 = User::factory()->create();
    $user1->assignRole('Editor');
    
    $user2 = User::factory()->create();
    $user2->assignRole('Editor');

    $artist = Artist::factory()->create(['created_by' => $user1->id]);

    // Creator can edit their own record
    $this->actingAs($user1)
        ->put("/chinook-admin/{$this->getResourcePath()}/{$artist->id}", $this->getValidUpdateData())
        ->assertRedirect();

    // Other user cannot edit
    $this->actingAs($user2)
        ->put("/chinook-admin/{$this->getResourcePath()}/{$artist->id}", $this->getValidUpdateData())
        ->assertStatus(403);
}
```

## Validation Testing

### Form Validation Testing

```php
public function test_artist_creation_validation(): void
{
    $invalidData = $this->getInvalidData();

    $this->actingAs($this->adminUser)
        ->post("/chinook-admin/{$this->getResourcePath()}", $invalidData)
        ->assertSessionHasErrors([
            'name',
            'website',
            'formed_year',
            'social_links.0.platform',
        ]);
}

public function test_unique_name_validation(): void
{
    Artist::factory()->create(['name' => 'Existing Artist']);

    $this->actingAs($this->adminUser)
        ->post("/chinook-admin/{$this->getResourcePath()}", [
            'name' => 'Existing Artist',
            'country' => 'US',
        ])
        ->assertSessionHasErrors(['name']);
}

public function test_social_links_validation(): void
{
    $invalidSocialLinks = [
        'name' => 'Test Artist',
        'social_links' => [
            ['platform' => 'facebook', 'url' => 'invalid-url'],
            ['platform' => '', 'url' => 'https://example.com'],
            ['platform' => 'twitter', 'url' => ''],
        ],
    ];

    $this->actingAs($this->adminUser)
        ->post("/chinook-admin/{$this->getResourcePath()}", $invalidSocialLinks)
        ->assertSessionHasErrors([
            'social_links.0.url',
            'social_links.1.platform',
            'social_links.2.url',
        ]);
}
```

## Relationship Testing

### Relationship Manager Testing

```php
public function test_artist_albums_relationship_manager(): void
{
    $artist = Artist::factory()->create();
    $album = Album::factory()->create(['artist_id' => $artist->id]);

    $this->actingAs($this->adminUser)
        ->get("/chinook-admin/{$this->getResourcePath()}/{$artist->id}/albums")
        ->assertStatus(200)
        ->assertSee($album->title);
}

public function test_attach_album_to_artist(): void
{
    $artist = Artist::factory()->create();
    $album = Album::factory()->create(['artist_id' => null]);

    $this->actingAs($this->adminUser)
        ->post("/chinook-admin/{$this->getResourcePath()}/{$artist->id}/albums/attach", [
            'recordId' => $album->id,
        ])
        ->assertRedirect();

    $this->assertDatabaseHas('albums', [
        'id' => $album->id,
        'artist_id' => $artist->id,
    ]);
}
```

## Advanced Feature Testing

### Bulk Actions Testing

```php
public function test_bulk_delete_artists(): void
{
    $artists = Artist::factory()->count(3)->create();

    $this->actingAs($this->adminUser)
        ->post("/chinook-admin/{$this->getResourcePath()}/bulk-delete", [
            'records' => $artists->pluck('id')->toArray(),
        ])
        ->assertRedirect();

    foreach ($artists as $artist) {
        $this->assertSoftDeleted($artist);
    }
}

public function test_bulk_activate_artists(): void
{
    $artists = Artist::factory()->count(3)->create(['is_active' => false]);

    $this->actingAs($this->adminUser)
        ->post("/chinook-admin/{$this->getResourcePath()}/bulk-activate", [
            'records' => $artists->pluck('id')->toArray(),
        ])
        ->assertRedirect();

    foreach ($artists as $artist) {
        $artist->refresh();
        $this->assertTrue($artist->is_active);
    }
}
```

### Import/Export Testing

```php
public function test_export_artists(): void
{
    Artist::factory()->count(5)->create();

    $this->actingAs($this->adminUser)
        ->get("/chinook-admin/{$this->getResourcePath()}/export")
        ->assertStatus(200)
        ->assertHeader('Content-Type', 'text/csv');
}

public function test_import_artists(): void
{
    Storage::fake('local');
    
    $csvContent = "name,country,formed_year\nTest Artist 1,US,2020\nTest Artist 2,CA,2021";
    $file = UploadedFile::fake()->createWithContent('artists.csv', $csvContent);

    $this->actingAs($this->adminUser)
        ->post("/chinook-admin/{$this->getResourcePath()}/import", [
            'file' => $file,
        ])
        ->assertRedirect();

    $this->assertDatabaseHas('artists', ['name' => 'Test Artist 1']);
    $this->assertDatabaseHas('artists', ['name' => 'Test Artist 2']);
}
```

## Performance Testing

### Resource Performance Testing

```php
public function test_artist_index_performance_with_large_dataset(): void
{
    Artist::factory()->count(1000)->create();

    $startTime = microtime(true);

    $this->actingAs($this->adminUser)
        ->get("/chinook-admin/{$this->getResourcePath()}")
        ->assertStatus(200);

    $endTime = microtime(true);
    $executionTime = $endTime - $startTime;

    $this->assertLessThan(2.0, $executionTime, 
        "Artist index took {$executionTime} seconds, exceeding 2-second limit");
}

public function test_artist_search_performance(): void
{
    Artist::factory()->count(500)->create();

    $startTime = microtime(true);

    $this->actingAs($this->adminUser)
        ->get("/chinook-admin/{$this->getResourcePath()}?search=test")
        ->assertStatus(200);

    $endTime = microtime(true);
    $executionTime = $endTime - $startTime;

    $this->assertLessThan(1.0, $executionTime,
        "Artist search took {$executionTime} seconds, exceeding 1-second limit");
}
```

## Next Steps

1. **Implement Base Test Classes** - Create reusable test infrastructure
2. **Test All Resources** - Apply testing patterns to all Filament resources
3. **Add Performance Tests** - Validate resource performance under load
4. **Test Edge Cases** - Cover error conditions and boundary cases
5. **Automate Testing** - Integrate tests into CI/CD pipeline
6. **Monitor Coverage** - Track and improve test coverage

## Related Documentation

- **[Testing Strategy](010-testing-strategy.md)** - Overall testing approach
- **[Form Testing](050-form-testing.md)** - Detailed form validation testing
- **[Authorization Testing](130-authorization-testing.md)** - Permission and access control testing
- **[Performance Testing](150-performance-testing.md)** - Load testing and optimization
