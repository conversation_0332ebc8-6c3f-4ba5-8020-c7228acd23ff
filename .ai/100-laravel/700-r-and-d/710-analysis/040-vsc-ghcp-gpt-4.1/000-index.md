~~~markdown
// .ai/100-laravel/710-analysis/040-vsc-ghcp-gpt-4.1/000-index.md

# 000-Index

Welcome to the "Why Did We Do This?" analysis series for your Laravel R&D stack. This folder contains:

1. 005-architectural-patterns.md – Architectural patterns, principles, and their (mis)application
2. 010-dependency-tree.md – Composer/npm dependency tree, installability, and gotchas
3. 015-capabilities-and-features.md – What the packages actually enable (with extra colour)
4. 020-inconsistencies.md – Pattern/package mismatches, install issues, and other fun surprises

Navigate with caution. Colourful illustrations and sarcasm ahead.

~~~
