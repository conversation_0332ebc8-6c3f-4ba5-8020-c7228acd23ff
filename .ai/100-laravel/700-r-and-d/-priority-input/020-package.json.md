# package.json

```json
{
    "$schema": "https://json.schemastore.org/package.json",
    "private": true,
    "type": "module",
    "scripts": {
        "build": "vite build",
        "build:ssr": "vite build && vite build --ssr",
        "clean": "rimraf dist node_modules/.vite",
        "clean:all": "rimraf dist node_modules",
        "dev": "vite",
        "format": "prettier --write resources/",
        "format:check": "prettier --check resources/",
        "install:post": "simple-git-hooks",
        "lint": "eslint . --fix",
        "optimize": "vite optimize --force",
        "preview": "vite preview",
        "preview:dist": "vite preview --port 5000",
        "test": "vitest",
        "test:coverage": "vitest run --coverage",
        "test:jest": "jest",
        "test:jest:watch": "jest --watch",
        "test:jest:coverage": "jest --coverage",
        "test:e2e": "playwright test",
        "test:e2e:ui": "playwright test --ui",
        "test:e2e:debug": "playwright test --debug",
        "test:e2e:report": "playwright show-report",
        "types": "tsc --noEmit",
        "watch": "vite build --watch"
    },
    "dependencies": {
        "@alpinejs/anchor": "^3.14.9",
        "@alpinejs/collapse": "^3.14.9",
        "@alpinejs/focus": "^3.14.9",
        "@alpinejs/intersect": "^3.14.9",
        "@alpinejs/mask": "^3.14.9",
        "@alpinejs/morph": "^3.14.9",
        "@alpinejs/persist": "^3.14.9",
        "@alpinejs/resize": "^3.14.9",
        "@alpinejs/sort": "^3.14.9",
        "@fylgja/alpinejs-dialog": "^2.1.1",
        "@imacrayon/alpine-ajax": "^0.12.2",
        "@inertiajs/vue3": "^2.0.0",
        "@tailwindcss/vite": "^4.1.6",
        "@vitejs/plugin-vue": "^5.2.1",
        "@vueuse/core": "^13.3.0",
        "autoprefixer": "^10.4.21",
        "axios": "^1.9.0",
        "class-variance-authority": "^0.7.1",
        "clsx": "^2.1.1",
        "commitlint": "^19.8.1",
        "concurrently": "^9.1.2",
        "esbuild": "^0.25.5",
        "globals": "^16.1.0",
        "laravel-vite-plugin": "^1.2.0",
        "lucide-vue-next": "^0.511.0",
        "puppeteer": "^24.8.2",
        "reka-ui": "^2.2.0",
        "shiki": "^3.4.0",
        "tailwind-merge": "^3.3.0",
        "tailwindcss": "^4.1.6",
        "tailwindcss-animate": "^1.0.7",
        "tw-animate-css": "^1.2.5",
        "typescript": "^5.8.3",
        "vite": "^6.3.5",
        "vite-plugin-compression": "^0.5.1",
        "vite-plugin-dynamic-import": "^1.6.0",
        "vite-plugin-eslint": "^1.8.1",
        "vite-plugin-inspector": "^1.0.0",
        "vue": "^3.5.13",
        "ziggy-js": "^2.4.2"
    },
    "devDependencies": {
        "@commitlint/config-conventional": "^19.8.1",
        "@eslint/js": "^9.26.0",
        "@laravel/echo-vue": "^2.1.4",
        "@laravel/vite-plugin-wayfinder": "^0.1.3",
        "@playwright/test": "^1.52.0",
        "@types/node": "^22.15.17",
        "@vue/eslint-config-typescript": "^14.3.0",
        "chokidar": "^4.0.3",
        "eslint": "^9.26.0",
        "eslint-config-prettier": "^10.1.5",
        "eslint-plugin-prettier": "^5.4.0",
        "eslint-plugin-react": "^7.37.5",
        "eslint-plugin-react-hooks": "^5.2.0",
        "eslint-plugin-vue": "^10.1.0",
        "laravel-echo": "^2.1.4",
        "lint-staged": "^16.0.0",
        "prettier": "^3.5.3",
        "prettier-plugin-organize-imports": "^4.1.0",
        "prettier-plugin-tailwindcss": "^0.6.11",
        "pusher-js": "^8.4.0",
        "rimraf": "^6.0.1",
        "rollup-plugin-visualizer": "^5.14.0",
        "simple-git-hooks": "^2.13.0",
        "typescript-eslint": "^8.32.1",
        "vitest": "^3.1.3",
        "vue-tsc": "^2.2.4"
    },
    "lint-staged": {
        "*.{js,jsx,ts,tsx}": [
            "prettier --write",
            "eslint --fix --ignore-pattern 'public/*' --ignore-pattern 'vendor/*' --no-warn-ignored"
        ],
        "*.{css,md}": "prettier --write"
    },
    "simple-git-hooks": {
        "commit-msg": "npx commitlint --edit ${1}",
        "pre-commit": "npx lint-staged",
        "pre-push": "npm run format",
        "preserveUnused": true
    },
    "optionalDependencies": {
        "@rollup/rollup-linux-x64-gnu": "4.9.5",
        "@tailwindcss/oxide-linux-x64-gnu": "^4.0.1",
        "lightningcss-linux-x64-gnu": "^1.29.1"
    }
}
```
