# Phase 1: Event Sourcing Filament Integration

**Version:** 1.0.0 **Date:** 2025-05-19 **Author:** AI Assistant **Status:** New **Progress:** Complete

---

<details>
<summary>Table of Contents</summary>

- [Overview](#overview)
- [Filament Admin Panel](#filament-admin-panel)
  - [Benefits](#benefits)
  - [Components](#components)
- [Integration with Event Sourcing](#integration-with-event-sourcing)
  - [Command Dispatching](#command-dispatching)
  - [Read Model Integration](#read-model-integration)
  - [Event Visualization](#event-visualization)
- [Implementation](#implementation)
  - [Resource Classes](#resource-classes)
  - [Form Components](#form-components)
  - [Table Components](#table-components)
  - [Action Classes](#action-classes)
- [Example: Team Management](#example-team-management)
  - [Team Resource](#team-resource)
  - [Team Form](#team-form)
  - [Team Table](#team-table)
  - [Team Actions](#team-actions)
- [Event Sourcing Debug Tools](#event-sourcing-debug-tools)
  - [Event Stream Viewer](#event-stream-viewer)
  - [Aggregate Inspector](#aggregate-inspector)
  - [Projector Status](#projector-status)
- [Testing](#testing)
- [Related Documents](#related-documents)
- [Version History](#version-history)
</details>

## Overview

This document describes the integration of Filament Admin Panel with event sourcing in the Enhanced Laravel Application
(ELA). This integration provides a powerful and user-friendly interface for managing event-sourced entities, visualizing
event streams, and monitoring the health of the event sourcing system.

## Filament Admin Panel

### Benefits

Filament provides several benefits for building admin interfaces:

1. **Rapid Development**: Filament's resource-based approach allows for rapid development of CRUD interfaces.
2. **Customization**: Extensive customization options for forms, tables, and actions.
3. **Responsive Design**: Mobile-friendly interfaces out of the box.
4. **Authentication**: Built-in authentication and authorization.
5. **Extensibility**: Easy to extend with custom components and plugins.

### Components

Filament consists of several key components:

1. **Resources**: Define the CRUD interfaces for your models.
2. **Forms**: Define the forms for creating and editing resources.
3. **Tables**: Define the tables for displaying resources.
4. **Actions**: Define the actions that can be performed on resources.
5. **Widgets**: Define dashboard widgets and other UI components.

## Integration with Event Sourcing

### Command Dispatching

Filament actions will be used to dispatch commands to the event sourcing system:

1. User interacts with a form or table action
2. Action dispatches a command to the command bus
3. Command handler retrieves the aggregate and applies the command
4. Aggregate emits events
5. Events are stored and dispatched to projectors and reactors

### Read Model Integration

Filament resources will be based on the read models generated by projectors:

1. Projectors update the read model when events occur
2. Filament resources display the read model data
3. Changes to the read model are reflected in the Filament UI

### Event Visualization

Custom Filament pages will be created to visualize event streams and aggregate state:

1. Event stream viewer shows all events for an aggregate
2. Aggregate inspector shows the current state of an aggregate
3. Projector status page shows the health of projectors

## Implementation

### Resource Classes

```php
namespace App\Filament\Resources;

use App\Filament\Resources\TeamResource\Pages;
use App\Models\Team;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;

class TeamResource extends Resource
{
    protected static ?string $model = Team::class;
    protected static ?string $navigationIcon = 'heroicon-o-user-group';
    protected static ?string $recordTitleAttribute = 'name';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Textarea::make('description')
                    ->maxLength(65535),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('description')
                    ->limit(50),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTeams::route('/'),
            'create' => Pages\CreateTeam::route('/create'),
            'edit' => Pages\EditTeam::route('/{record}/edit'),
        ];
    }
}
```

### Form Components

Custom form components can be created to handle event sourcing specific functionality:

```php
namespace App\Filament\Forms\Components;

use Filament\Forms\Components\Field;

class AggregateIdField extends Field
{
    protected string $view = 'filament.forms.components.aggregate-id-field';

    protected function setUp(): void
    {
        parent::setUp();

        $this->default(fn () => (string) \Illuminate\Support\Str::uuid());
        $this->disabled();
    }
}
```

### Table Components

Custom table columns can be created to display event sourcing specific data:

```php
namespace App\Filament\Tables\Columns;

use Filament\Tables\Columns\Column;

class EventCountColumn extends Column
{
    protected string $view = 'filament.tables.columns.event-count-column';

    public function getState(): int
    {
        $record = $this->getRecord();

        return \App\Models\StoredEvent::where('aggregate_uuid', $record->uuid)->count();
    }
}
```

### Action Classes

Custom actions can be created to dispatch commands:

```php
namespace App\Filament\Actions;

use App\CQRS\Commands\CommandBus;
use App\CQRS\Commands\Teams\CreateTeamCommand;
use Filament\Forms\ComponentContainer;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Pages\Actions\Action;
use Illuminate\Support\Str;

class CreateTeamAction extends Action
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Create Team');
        $this->icon('heroicon-o-plus');
        $this->color('primary');

        $this->form([
            TextInput::make('name')
                ->required()
                ->maxLength(255),
            Textarea::make('description')
                ->maxLength(65535),
        ]);

        $this->action(function (ComponentContainer $form, CommandBus $commandBus): void {
            $data = $form->getState();

            $teamUuid = Str::uuid()->toString();

            $commandBus->dispatch(new CreateTeamCommand(
                $teamUuid,
                $data['name'],
                $data['description'] ?? null
            ));

            $this->success();
        });
    }
}
```

## Example: Team Management

### Team Resource

```php
namespace App\Filament\Resources;

use App\Filament\Resources\TeamResource\Pages;
use App\Filament\Resources\TeamResource\RelationManagers;
use App\Models\Team;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;

class TeamResource extends Resource
{
    protected static ?string $model = Team::class;
    protected static ?string $navigationIcon = 'heroicon-o-user-group';
    protected static ?string $recordTitleAttribute = 'name';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Textarea::make('description')
                    ->maxLength(65535),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('description')
                    ->limit(50),
                Tables\Columns\TextColumn::make('members_count')
                    ->counts('members')
                    ->label('Members'),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('viewEvents')
                    ->label('View Events')
                    ->icon('heroicon-o-eye')
                    ->url(fn (Team $record): string => route('filament.resources.teams.events', $record)),
                Tables\Actions\Action::make('addMember')
                    ->label('Add Member')
                    ->icon('heroicon-o-user-add')
                    ->action(function (Team $record, array $data, CommandBus $commandBus): void {
                        $commandBus->dispatch(new AddTeamMemberCommand(
                            $record->uuid,
                            $data['user_id']
                        ));
                    })
                    ->form([
                        Forms\Components\Select::make('user_id')
                            ->label('User')
                            ->options(User::all()->pluck('name', 'id'))
                            ->required(),
                    ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\MembersRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTeams::route('/'),
            'create' => Pages\CreateTeam::route('/create'),
            'edit' => Pages\EditTeam::route('/{record}/edit'),
            'events' => Pages\TeamEvents::route('/{record}/events'),
        ];
    }
}
```

### Team Events Page

```php
namespace App\Filament\Resources\TeamResource\Pages;

use App\Filament\Resources\TeamResource;
use App\Models\StoredEvent;
use App\Models\Team;
use Filament\Resources\Pages\Page;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;

class TeamEvents extends Page implements Tables\Contracts\HasTable
{
    use Tables\Concerns\InteractsWithTable;

    protected static string $resource = TeamResource::class;
    protected static string $view = 'filament.resources.team-resource.pages.team-events';

    public Team $record;

    protected function getTableQuery(): Builder
    {
        return StoredEvent::query()
            ->where('aggregate_uuid', $this->record->uuid)
            ->orderBy('created_at', 'desc');
    }

    protected function getTableColumns(): array
    {
        return [
            Tables\Columns\TextColumn::make('event_class')
                ->label('Event')
                ->formatStateUsing(fn (string $state): string => class_basename($state)),
            Tables\Columns\TextColumn::make('created_at')
                ->label('Timestamp')
                ->dateTime(),
            Tables\Columns\TextColumn::make('event_properties')
                ->label('Properties')
                ->formatStateUsing(fn (array $state): string => json_encode($state, JSON_PRETTY_PRINT)),
        ];
    }
}
```

## Event Sourcing Debug Tools

### Event Stream Viewer

```php
namespace App\Filament\Pages;

use App\Models\StoredEvent;
use Filament\Pages\Page;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;

class EventStreamViewer extends Page implements Tables\Contracts\HasTable
{
    use Tables\Concerns\InteractsWithTable;

    protected static ?string $navigationIcon = 'heroicon-o-collection';
    protected static string $view = 'filament.pages.event-stream-viewer';
    protected static ?string $navigationGroup = 'Event Sourcing';

    protected function getTableQuery(): Builder
    {
        return StoredEvent::query()->orderBy('created_at', 'desc');
    }

    protected function getTableColumns(): array
    {
        return [
            Tables\Columns\TextColumn::make('aggregate_uuid')
                ->label('Aggregate UUID')
                ->searchable(),
            Tables\Columns\TextColumn::make('event_class')
                ->label('Event')
                ->formatStateUsing(fn (string $state): string => class_basename($state))
                ->searchable(),
            Tables\Columns\TextColumn::make('created_at')
                ->label('Timestamp')
                ->dateTime(),
        ];
    }

    protected function getTableFilters(): array
    {
        return [
            Tables\Filters\Filter::make('event_class')
                ->form([
                    Forms\Components\Select::make('event_class')
                        ->options(
                            StoredEvent::query()
                                ->distinct('event_class')
                                ->pluck('event_class', 'event_class')
                                ->map(fn ($class) => class_basename($class))
                                ->toArray()
                        ),
                ])
                ->query(function (Builder $query, array $data): Builder {
                    return $query->when(
                        $data['event_class'],
                        fn (Builder $query, $eventClass): Builder => $query->where('event_class', $eventClass)
                    );
                }),
        ];
    }
}
```

## Related Documents

- [Event Sourcing Implementation](../050-implementation.md)
- [Filament Configuration](../030-core-components/040-filament-configuration.md)
- [CQRS Integration](../130-cqrs-integration.md)

## Version History

| Version | Date       | Changes         | Author       |
| ------- | ---------- | --------------- | ------------ |
| 1.0.0   | 2025-05-19 | Initial version | AI Assistant |
