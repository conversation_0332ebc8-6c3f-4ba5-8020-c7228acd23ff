# Catalogs Index

**Version:** 1.0.0
**Date:** 2025-05-25
**Author:** AI Assistant
**Status:** Draft
**Progress:** 0%

---

<details>
<summary>Table of Contents</summary>

- [1. Overview](#1-overview)
- [2. Catalogs Documents](#2-catalogs-documents)
- [3. Related Documents](#3-related-documents)
- [4. Version History](#4-version-history)

</details>

<details>
<summary>Navigation</summary>

**Main:**
[Home](../../../000-readme.md) |
[Documentation Index](../../../000-index.md) |
[Implementation Plan Index](../../000-index.md) |
[Event Sourcing Index](../000-index.md)

**You are here:**
[Home](../../../000-readme.md) >
[Documentation Index](../../../000-index.md) >
[Implementation Plan Index](../../000-index.md) >
[Event Sourcing Index](../000-index.md) >
**Catalogs Index**

</details>

## 1. Overview

This index provides a comprehensive list of all catalogs documents for the Enhanced Laravel Application (ELA) event sourcing implementation. These documents provide detailed catalogs of commands and events used in the event sourcing system.

## 2. Catalogs Documents

<details>
<summary>Table Details</summary>

| Document | Description |
| --- | --- |
| [010-command-catalog.md](./010-command-catalog.md) | Catalog of all commands used in the event sourcing implementation |
| [020-event-catalog.md](./020-event-catalog.md) | Catalog of all domain events used in the event sourcing implementation |

</details>

## 3. Related Documents

<details>
<summary>Related Documentation</summary>

| Document | Description |
| --- | --- |
| [Event Sourcing Overview](../010-overview.md) | Overview of event sourcing implementation |
| [Aggregates Index](../020-aggregates/000-index.md) | Aggregates that use commands and produce events |
| [Event Sourcing Projectors](../030-projectors.md) | Projectors that react to events |
| [Event Sourcing Reactors](../040-reactors.md) | Reactors that react to events |
| [Event Sourcing Implementation](../050-implementation.md) | Implementation details that use commands and events |
| [CQRS Integration](../120-integrations/010-cqrs-integration.md) | Integration of CQRS with event sourcing |
| [State Transitions](../110-features/040-state-transitions.md) | State transitions triggered by commands and events |

</details>

## 4. Version History

<details>
<summary>Table Details</summary>

| Version | Date | Changes | Author |
| --- | --- | --- | --- |
| 1.0.0 | 2025-05-25 | Initial version | AI Assistant |

</details>

### Navigation Links

**Previous:** [Event Sourcing Roles and Permissions](../090-roles-permissions.md)

**Next:** [Features Index](../110-features/000-index.md)

---

This index is maintained regularly to ensure all catalogs documents are properly cataloged and accessible. If you notice any missing documents or have suggestions for improvements, please update this index accordingly.
