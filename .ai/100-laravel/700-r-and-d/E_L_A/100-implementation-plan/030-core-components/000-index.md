# Core Components Index

**Version:** 1.0.0
**Date:** 2025-05-25
**Author:** AI Assistant
**Status:** Draft
**Progress:** 0%

---

<details>
<summary>Table of Contents</summary>

- [1. Overview](#1-overview)
- [2. Core Components Documents](#2-core-components-documents)
- [3. Related Documents](#3-related-documents)
- [4. Version History](#4-version-history)

</details>

<details>
<summary>Navigation</summary>

**Main:**
[Home](../../000-readme.md) |
[Documentation Index](../../000-index.md) |
[Implementation Plan Index](../000-index.md)

**You are here:**
[Home](../../000-readme.md) >
[Documentation Index](../../000-index.md) >
[Implementation Plan Index](../000-index.md) >
**Core Components Index**

</details>

## 1. Overview

This index provides a comprehensive list of all core components documents for the Enhanced Laravel Application (ELA) implementation plan. These documents provide detailed instructions for installing and configuring essential packages, setting up CQRS, Filament admin panel, API documentation, and frontend components.

## 2. Core Components Documents

<details>
<summary>Table Details</summary>

| Document | Description |
| --- | --- |
| [010-package-installation.md](./010-package-installation.md) | Installation and configuration of required packages for the application |
| [020-spatie-settings-setup.md](./020-spatie-settings-setup.md) | Setup and configuration of Spatie Laravel Settings package |
| [030-cqrs-configuration.md](./030-cqrs-configuration.md) | Configuration of Command Query Responsibility Segregation (CQRS) with hirethunk/verbs |
| [040-filament-configuration.md](./040-filament-configuration.md) | Configuration of Filament admin panel for the application |
| [050-api-documentation.md](./050-api-documentation.md) | Setup and configuration of API documentation with OpenAPI |
| [060-frontend-setup.md](./060-frontend-setup.md) | Setup and configuration of frontend components |

</details>

## 3. Related Documents

<details>
<summary>Related Documentation</summary>

| Document | Description |
| --- | --- |
| [Database Setup](../040-database/010-database-setup.md) | Database configuration that works with core components |
| [Security Setup](../050-security-testing/010-security-setup.md) | Security configuration for core components |
| [App Service Provider](../060-configuration/010-app-service-provider.md) | Service provider configuration for core components |
| [Event Sourcing CQRS Integration](../100-event-sourcing/120-integrations/010-cqrs-integration.md) | Integration of CQRS with event sourcing |
| [Event Sourcing Filament Integration](../100-event-sourcing/120-integrations/040-filament-integration.md) | Integration of Filament with event sourcing |

</details>

## 4. Version History

<details>
<summary>Table Details</summary>

| Version | Date | Changes | Author |
| --- | --- | --- | --- |
| 1.0.0 | 2025-05-25 | Initial version | AI Assistant |

</details>

### Navigation Links

**Previous:** [Environment Setup Index](../020-environment-setup/000-index.md)

**Next:** [Database Configuration Index](../040-database/000-index.md)

---

This index is maintained regularly to ensure all core components documents are properly cataloged and accessible. If you notice any missing documents or have suggestions for improvements, please update this index accordingly.
