<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 2030.61328125 1001.58447265625" style="max-width: 2030.61px; background-color: white;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#552222;}#my-svg .error-text{fill:#552222;stroke:#552222;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#666666;stroke:#666666;}#my-svg .marker.cross{stroke:#666666;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#my-svg .cluster-label text{fill:#333;}#my-svg .cluster-label span{color:#333;}#my-svg .cluster-label span p{background-color:transparent;}#my-svg .label text,#my-svg span{fill:#333;color:#333;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#my-svg .rough-node .label text,#my-svg .node .label text,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-anchor:middle;}#my-svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#my-svg .rough-node .label,#my-svg .node .label,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-align:center;}#my-svg .node.clickable{cursor:pointer;}#my-svg .root .anchor path{fill:#666666!important;stroke-width:0;stroke:#666666;}#my-svg .arrowheadPath{fill:#333333;}#my-svg .edgePath .path{stroke:#666666;stroke-width:2.0px;}#my-svg .flowchart-link{stroke:#666666;fill:none;}#my-svg .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#my-svg .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#my-svg .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#my-svg .cluster text{fill:#333;}#my-svg .cluster span{color:#333;}#my-svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:#ffffff;border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#my-svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#my-svg rect.text{fill:none;stroke-width:0;}#my-svg .icon-shape,#my-svg .image-shape{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .icon-shape p,#my-svg .image-shape p{background-color:rgba(232,232,232, 0.8);padding:2px;}#my-svg .icon-shape rect,#my-svg .image-shape rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"/></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><g class="root"><g class="clusters"><g data-look="classic" id="subGraph6" class="cluster"><rect height="160.42647552490234" width="591.2734375" y="833.1580047607422" x="73.63671875" style=""/><g transform="translate(320.1875, 833.1580047607422)" class="cluster-label"><foreignObject height="24" width="98.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Storage Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph5" class="cluster"><rect height="499.58448028564453" width="1092.9375" y="494" x="825.92578125" style=""/><g transform="translate(1333.82421875, 494)" class="cluster-label"><foreignObject height="24" width="77.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Data Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph4" class="cluster"><rect height="135.1580047607422" width="568.8203125" y="494" x="196.13671875" style=""/><g transform="translate(423.8125, 494)" class="cluster-label"><foreignObject height="24" width="113.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Real-time Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph3" class="cluster"><rect height="104" width="487.03125" y="679.1580047607422" x="292.03125" style=""/><g transform="translate(473.3046875, 679.1580047607422)" class="cluster-label"><foreignObject height="24" width="124.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Queue Processing</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph2" class="cluster"><rect height="128" width="1730.55859375" y="316" x="292.0546875" style=""/><g transform="translate(1094.693359375, 316)" class="cluster-label"><foreignObject height="24" width="125.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Application Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph1" class="cluster"><rect height="104" width="1455.81640625" y="162" x="421.796875" style=""/><g transform="translate(1095.994140625, 162)" class="cluster-label"><foreignObject height="24" width="107.421875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Load Balancing</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph0" class="cluster"><rect height="104" width="1127.14453125" y="8" x="8" style=""/><g transform="translate(528.158203125, 8)" class="cluster-label"><foreignObject height="24" width="86.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Client Layer</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Browser_LB_0" d="M399.126,87L409.571,91.167C420.016,95.333,440.907,103.667,451.352,112C461.797,120.333,461.797,128.667,461.797,137C461.797,145.333,461.797,153.667,517.465,164.75C573.133,175.834,684.468,189.668,740.136,196.585L795.804,203.502"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MobileApp_LB_0" d="M745.289,87L745.289,91.167C745.289,95.333,745.289,103.667,745.289,112C745.289,120.333,745.289,128.667,745.289,137C745.289,145.333,745.289,153.667,755.484,161.76C765.679,169.854,786.07,177.708,796.265,181.635L806.46,185.562"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ExternalAPI_LB_0" d="M999.918,87L999.918,91.167C999.918,95.333,999.918,103.667,999.918,112C999.918,120.333,999.918,128.667,999.918,137C999.918,145.333,999.918,153.667,990.944,161.734C981.969,169.802,964.021,177.604,955.047,181.505L946.072,185.405"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LB_WebServer1_0" d="M960.805,218.744L1094.482,226.62C1228.16,234.496,1495.516,250.248,1629.193,262.291C1762.871,274.333,1762.871,282.667,1762.871,291C1762.871,299.333,1762.871,307.667,1762.871,315.333C1762.871,323,1762.871,330,1762.871,333.5L1762.871,337"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LB_WebServer2_0" d="M880.289,241L880.289,245.167C880.289,249.333,880.289,257.667,880.289,266C880.289,274.333,880.289,282.667,880.289,291C880.289,299.333,880.289,307.667,880.289,315.333C880.289,323,880.289,330,880.289,333.5L880.289,337"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LB_WebServer3_0" d="M799.773,224.005L743.444,231.004C687.115,238.003,574.456,252.002,518.126,263.167C461.797,274.333,461.797,282.667,461.797,291C461.797,299.333,461.797,307.667,461.797,315.333C461.797,323,461.797,330,461.797,333.5L461.797,337"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer1_PrimaryDB_0" d="M1876.972,419L1889.162,423.167C1901.352,427.333,1925.733,435.667,1937.923,444C1950.113,452.333,1950.113,460.667,1931.363,469C1912.613,477.333,1875.113,485.667,1856.363,501.097C1837.613,516.526,1837.613,539.053,1837.613,561.579C1837.613,584.105,1837.613,606.632,1837.613,622.062C1837.613,637.491,1837.613,645.825,1837.613,654.158C1837.613,662.491,1837.613,670.825,1837.613,683.658C1837.613,696.491,1837.613,713.825,1837.613,731.158C1837.613,748.491,1837.613,765.825,1837.613,778.658C1837.613,791.491,1837.613,799.825,1837.613,808.158C1837.613,816.491,1837.613,824.825,1722.932,840.496C1608.251,856.168,1378.889,879.178,1264.208,890.683L1149.527,902.187"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer2_PrimaryDB_0" d="M1010.289,410.59L1033.953,416.158C1057.617,421.727,1104.945,432.863,1128.609,442.598C1152.273,452.333,1152.273,460.667,1253.163,469C1354.053,477.333,1555.833,485.667,1656.723,501.097C1757.613,516.526,1757.613,539.053,1757.613,561.579C1757.613,584.105,1757.613,606.632,1757.613,622.062C1757.613,637.491,1757.613,645.825,1757.613,654.158C1757.613,662.491,1757.613,670.825,1757.613,683.658C1757.613,696.491,1757.613,713.825,1757.613,731.158C1757.613,748.491,1757.613,765.825,1757.613,778.658C1757.613,791.491,1757.613,799.825,1757.613,808.158C1757.613,816.491,1757.613,824.825,1656.265,840.289C1554.916,855.754,1352.219,878.349,1250.871,889.647L1149.522,900.945"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer3_PrimaryDB_0" d="M547.266,419L556.398,423.167C565.529,427.333,583.792,435.667,592.923,444C602.055,452.333,602.055,460.667,662.908,469C723.762,477.333,845.469,485.667,906.322,501.097C967.176,516.526,967.176,539.053,967.176,561.579C967.176,584.105,967.176,606.632,967.176,622.062C967.176,637.491,967.176,645.825,967.176,654.158C967.176,662.491,967.176,670.825,967.176,683.658C967.176,696.491,967.176,713.825,967.176,731.158C967.176,748.491,967.176,765.825,967.176,778.658C967.176,791.491,967.176,799.825,967.176,808.158C967.176,816.491,967.176,824.825,970.432,832.931C973.689,841.037,980.202,848.917,983.459,852.857L986.716,856.796"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer1_ReadReplica_0" d="M1864.784,419L1875.672,423.167C1886.561,427.333,1908.337,435.667,1919.225,444C1930.113,452.333,1930.113,460.667,1911.363,469C1892.613,477.333,1855.113,485.667,1821.124,497.582C1787.135,509.497,1756.657,524.995,1741.418,532.743L1726.179,540.492"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer2_ReadReplica_0" d="M998.796,419L1011.457,423.167C1024.118,427.333,1049.44,435.667,1062.101,444C1074.762,452.333,1074.762,460.667,1166.066,469C1257.371,477.333,1439.98,485.667,1531.285,493.333C1622.59,501,1622.59,508,1622.59,511.5L1622.59,515"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer3_ReadReplica_0" d="M528.224,419L535.32,423.167C542.417,427.333,556.611,435.667,563.708,444C570.805,452.333,570.805,460.667,633.533,469C696.262,477.333,821.719,485.667,979.682,499.362C1137.646,513.058,1328.116,532.115,1423.351,541.644L1518.586,551.173"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer1_Redis_0" d="M1852.597,419L1862.183,423.167C1871.769,427.333,1890.941,435.667,1900.527,444C1910.113,452.333,1910.113,460.667,1891.363,469C1872.613,477.333,1835.113,485.667,1762.847,498.611C1690.58,511.556,1583.547,529.112,1530.03,537.89L1476.514,546.668"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer2_Redis_0" d="M920.496,419L924.792,423.167C929.087,427.333,937.678,435.667,941.974,444C946.27,452.333,946.27,460.667,1019.492,469C1092.715,477.333,1239.16,485.667,1312.383,493.543C1385.605,501.42,1385.605,508.84,1385.605,512.55L1385.605,516.26"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer3_Redis_0" d="M516.036,419L521.831,423.167C527.626,427.333,539.215,435.667,545.01,444C550.805,452.333,550.805,460.667,613.533,469C676.262,477.333,801.719,485.667,925.699,498.863C1049.68,512.059,1172.183,530.118,1233.435,539.147L1294.687,548.176"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Redis_HorizonWorker1_0" d="M1472.566,580.423L1487.407,588.546C1502.247,596.668,1531.928,612.913,1546.769,625.202C1561.609,637.491,1561.609,645.825,1561.609,654.158C1561.609,662.491,1561.609,670.825,1426.017,682.745C1290.425,694.666,1019.24,710.174,883.648,717.928L748.056,725.682"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Redis_HorizonWorker2_0" d="M1298.645,576.533L1279.936,585.304C1261.227,594.074,1223.809,611.616,1205.1,624.554C1186.391,637.491,1186.391,645.825,1186.391,654.158C1186.391,662.491,1186.391,670.825,1074.415,682.577C962.44,694.329,738.489,709.5,626.513,717.086L514.538,724.672"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_HorizonWorker1_PrimaryDB_0" d="M683.584,758.158L688.411,762.325C693.238,766.491,702.893,774.825,743.491,783.158C784.09,791.491,855.633,799.825,891.404,808.158C927.176,816.491,927.176,824.825,932.429,833.503C937.683,842.181,948.189,851.204,953.443,855.715L958.696,860.227"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_HorizonWorker2_PrimaryDB_0" d="M477.785,758.158L486.889,762.325C495.993,766.491,514.202,774.825,585.767,783.158C657.332,791.491,782.254,799.825,844.715,808.158C907.176,816.491,907.176,824.825,913.45,833.942C919.725,843.059,932.275,852.961,938.549,857.912L944.824,862.862"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer1_ReverbServer1_0" d="M1632.871,396.62L1571.105,404.517C1509.339,412.413,1385.806,428.207,1324.04,440.27C1262.273,452.333,1262.273,460.667,1167.615,469C1072.956,477.333,883.638,485.667,784.311,496.063C684.985,506.459,675.649,518.919,670.981,525.148L666.313,531.378"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer2_ReverbServer1_0" d="M841.218,419L837.043,423.167C832.869,427.333,824.52,435.667,820.346,444C816.172,452.333,816.172,460.667,768.363,469C720.555,477.333,624.938,485.667,588,496.257C551.063,506.848,572.805,519.696,583.677,526.12L594.548,532.544"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer3_ReverbServer2_0" d="M503.849,419L508.341,423.167C512.834,427.333,521.819,435.667,526.312,444C530.805,452.333,530.805,460.667,515.239,469C499.673,477.333,468.542,485.667,441.547,496.269C414.553,506.872,391.696,519.744,380.268,526.18L368.839,532.616"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Browser_ReverbServer1_0" d="M255.168,79.618L234.186,85.015C213.203,90.412,171.238,101.206,150.256,110.77C129.273,120.333,129.273,128.667,129.273,137C129.273,145.333,129.273,153.667,129.273,166.5C129.273,179.333,129.273,196.667,129.273,214C129.273,231.333,129.273,248.667,129.273,261.5C129.273,274.333,129.273,282.667,129.273,291C129.273,299.333,129.273,307.667,129.273,322.5C129.273,337.333,129.273,358.667,129.273,380C129.273,401.333,129.273,422.667,129.273,437.5C129.273,452.333,129.273,460.667,173.857,469C218.44,477.333,307.607,485.667,378.32,496.985C449.033,508.303,501.293,522.607,527.422,529.758L553.552,536.91"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Browser_ReverbServer2_0" d="M255.168,76.005L226.579,82.005C197.991,88.004,140.814,100.002,112.225,110.168C83.637,120.333,83.637,128.667,83.637,137C83.637,145.333,83.637,153.667,83.637,166.5C83.637,179.333,83.637,196.667,83.637,214C83.637,231.333,83.637,248.667,83.637,261.5C83.637,274.333,83.637,282.667,83.637,291C83.637,299.333,83.637,307.667,83.637,322.5C83.637,337.333,83.637,358.667,83.637,380C83.637,401.333,83.637,422.667,83.637,437.5C83.637,452.333,83.637,460.667,115.826,469C148.016,477.333,212.395,485.667,248.307,496.025C284.22,506.384,291.667,518.767,295.39,524.959L299.113,531.151"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer1_Typesense_0" d="M1632.871,395.982L1567.771,403.985C1502.672,411.988,1372.473,427.994,1307.373,440.164C1242.273,452.333,1242.273,460.667,1331.497,469C1420.72,477.333,1599.167,485.667,1600.892,498.9C1602.617,512.133,1427.62,530.265,1340.122,539.331L1252.623,548.398"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer2_Typesense_0" d="M829.03,419L823.554,423.167C818.077,427.333,807.125,435.667,801.648,444C796.172,452.333,796.172,460.667,851.045,469C905.918,477.333,1015.664,485.667,1070.537,495.93C1125.41,506.193,1125.41,518.386,1125.41,524.483L1125.41,530.579"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer3_Typesense_0" d="M491.661,419L494.852,423.167C498.042,427.333,504.423,435.667,507.614,444C510.805,452.333,510.805,460.667,569.992,469C629.178,477.333,747.552,485.667,832.062,496.428C916.573,507.19,967.22,520.381,992.543,526.976L1017.867,533.571"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer1_ObjectStorage_0" d="M1632.871,395.39L1564.438,403.492C1496.005,411.594,1359.139,427.797,1290.706,440.065C1222.273,452.333,1222.273,460.667,1341.705,469C1461.137,477.333,1700,485.667,1819.432,501.097C1938.863,516.526,1938.863,539.053,1938.863,561.579C1938.863,584.105,1938.863,606.632,1938.863,622.062C1938.863,637.491,1938.863,645.825,1938.863,654.158C1938.863,662.491,1938.863,670.825,1938.863,683.658C1938.863,696.491,1938.863,713.825,1938.863,731.158C1938.863,748.491,1938.863,765.825,1938.863,778.658C1938.863,791.491,1938.863,799.825,1711.121,808.158C1483.379,816.491,1027.895,824.825,780.489,835.646C533.083,846.468,493.756,859.779,474.093,866.434L454.429,873.089"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer2_ObjectStorage_0" d="M750.289,412.219L728.917,417.516C707.544,422.813,664.799,433.406,643.427,442.87C622.055,452.333,622.055,460.667,652.7,469C683.345,477.333,744.635,485.667,775.281,501.097C805.926,516.526,805.926,539.053,805.926,561.579C805.926,584.105,805.926,606.632,805.926,622.062C805.926,637.491,805.926,645.825,805.926,654.158C805.926,662.491,805.926,670.825,704.294,683.658C602.663,696.491,399.4,713.825,297.768,731.158C196.137,748.491,196.137,765.825,196.137,778.658C196.137,791.491,196.137,799.825,196.137,808.158C196.137,816.491,196.137,824.825,207.485,835.527C218.834,846.23,241.531,859.303,252.88,865.839L264.229,872.375"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer3_ObjectStorage_0" d="M420.821,419L416.443,423.167C412.066,427.333,403.31,435.667,398.932,444C394.555,452.333,394.555,460.667,358.152,469C321.749,477.333,248.943,485.667,212.54,501.097C176.137,516.526,176.137,539.053,176.137,561.579C176.137,584.105,176.137,606.632,176.137,622.062C176.137,637.491,176.137,645.825,176.137,654.158C176.137,662.491,176.137,670.825,176.137,683.658C176.137,696.491,176.137,713.825,176.137,731.158C176.137,748.491,176.137,765.825,176.137,778.658C176.137,791.491,176.137,799.825,176.137,808.158C176.137,816.491,176.137,824.825,189.18,835.56C202.224,846.296,228.311,859.434,241.355,866.003L254.398,872.572"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_HorizonWorker1_ObjectStorage_0" d="M600.436,758.158L592.432,762.325C584.428,766.491,568.419,774.825,560.415,783.158C552.41,791.491,552.41,799.825,552.41,808.158C552.41,816.491,552.41,824.825,534.453,835.629C516.496,846.433,480.582,859.709,462.625,866.347L444.668,872.984"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_HorizonWorker2_ObjectStorage_0" d="M399.829,758.158L396.903,762.325C393.977,766.491,388.125,774.825,385.199,783.158C382.273,791.491,382.273,799.825,382.273,808.158C382.273,816.491,382.273,824.825,378.597,835.285C374.92,845.744,367.567,858.331,363.89,864.624L360.213,870.917"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(331.44140625, 60)" id="flowchart-Browser-0" class="node default"><rect height="54" width="152.546875" y="-27" x="-76.2734375" style="" class="basic label-container"/><g transform="translate(-46.2734375, -12)" style="" class="label"><rect/><foreignObject height="24" width="92.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Web Browser</p></span></div></foreignObject></g></g><g transform="translate(745.2890625, 60)" id="flowchart-MobileApp-1" class="node default"><rect height="54" width="138.0625" y="-27" x="-69.03125" style="" class="basic label-container"/><g transform="translate(-39.03125, -12)" style="" class="label"><rect/><foreignObject height="24" width="78.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Mobile App</p></span></div></foreignObject></g></g><g transform="translate(999.91796875, 60)" id="flowchart-ExternalAPI-2" class="node default"><rect height="54" width="200.453125" y="-27" x="-100.2265625" style="" class="basic label-container"/><g transform="translate(-70.2265625, -12)" style="" class="label"><rect/><foreignObject height="24" width="140.453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>External API Clients</p></span></div></foreignObject></g></g><g transform="translate(880.2890625, 214)" id="flowchart-LB-3" class="node default"><rect height="54" width="161.03125" y="-27" x="-80.515625" style="" class="basic label-container"/><g transform="translate(-50.515625, -12)" style="" class="label"><rect/><foreignObject height="24" width="101.03125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Load Balancer</p></span></div></foreignObject></g></g><g transform="translate(1762.87109375, 380)" id="flowchart-WebServer1-4" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Web Server 1\nFrankenPHP + Laravel Octane</p></span></div></foreignObject></g></g><g transform="translate(880.2890625, 380)" id="flowchart-WebServer2-5" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Web Server 2\nFrankenPHP + Laravel Octane</p></span></div></foreignObject></g></g><g transform="translate(461.796875, 380)" id="flowchart-WebServer3-6" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Web Server 3\nFrankenPHP + Laravel Octane</p></span></div></foreignObject></g></g><g transform="translate(652.3046875, 731.1580047607422)" id="flowchart-HorizonWorker1-7" class="node default"><rect height="54" width="183.515625" y="-27" x="-91.7578125" style="" class="basic label-container"/><g transform="translate(-61.7578125, -12)" style="" class="label"><rect/><foreignObject height="24" width="123.515625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Horizon Worker 1</p></span></div></foreignObject></g></g><g transform="translate(418.7890625, 731.1580047607422)" id="flowchart-HorizonWorker2-8" class="node default"><rect height="54" width="183.515625" y="-27" x="-91.7578125" style="" class="basic label-container"/><g transform="translate(-61.7578125, -12)" style="" class="label"><rect/><foreignObject height="24" width="123.515625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Horizon Worker 2</p></span></div></foreignObject></g></g><g transform="translate(643.68359375, 561.5790023803711)" id="flowchart-ReverbServer1-9" class="node default"><rect height="54" width="172.546875" y="-27" x="-86.2734375" style="" class="basic label-container"/><g transform="translate(-56.2734375, -12)" style="" class="label"><rect/><foreignObject height="24" width="112.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Reverb Server 1</p></span></div></foreignObject></g></g><g transform="translate(317.41015625, 561.5790023803711)" id="flowchart-ReverbServer2-10" class="node default"><rect height="54" width="172.546875" y="-27" x="-86.2734375" style="" class="basic label-container"/><g transform="translate(-56.2734375, -12)" style="" class="label"><rect/><foreignObject height="24" width="112.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Reverb Server 2</p></span></div></foreignObject></g></g><g transform="translate(1038.046875, 913.3712425231934)" id="flowchart-PrimaryDB-11" class="node default"><path transform="translate(-107.5, -55.21323529411765)" style="" class="basic label-container" d="M0,15.808823529411764 a107.5,15.808823529411764 0,0,0 215,0 a107.5,15.808823529411764 0,0,0 -215,0 l0,78.80882352941177 a107.5,15.808823529411764 0,0,0 215,0 l0,-78.80882352941177"/><g transform="translate(-100, -14)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Primary Database\nPostgreSQL</p></span></div></foreignObject></g></g><g transform="translate(1622.58984375, 561.5790023803711)" id="flowchart-ReadReplica-12" class="node default"><path transform="translate(-100.0234375, -42.57900302840937)" style="" class="basic label-container" d="M0,15.386002018939577 a100.0234375,15.386002018939577 0,0,0 200.046875,0 a100.0234375,15.386002018939577 0,0,0 -200.046875,0 l0,54.38600201893958 a100.0234375,15.386002018939577 0,0,0 200.046875,0 l0,-54.38600201893958"/><g transform="translate(-92.5234375, -2)" style="" class="label"><rect/><foreignObject height="24" width="185.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Read Replica\nPostgreSQL</p></span></div></foreignObject></g></g><g transform="translate(1385.60546875, 561.5790023803711)" id="flowchart-Redis-13" class="node default"><path transform="translate(-86.9609375, -41.318645130939316)" style="" class="basic label-container" d="M0,14.545763420626209 a86.9609375,14.545763420626209 0,0,0 173.921875,0 a86.9609375,14.545763420626209 0,0,0 -173.921875,0 l0,53.54576342062621 a86.9609375,14.545763420626209 0,0,0 173.921875,0 l0,-53.54576342062621"/><g transform="translate(-79.4609375, -2)" style="" class="label"><rect/><foreignObject height="24" width="158.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Redis\nCache + Queue</p></span></div></foreignObject></g></g><g transform="translate(1125.41015625, 561.5790023803711)" id="flowchart-Typesense-14" class="node default"><rect height="54" width="246.46875" y="-27" x="-123.234375" style="" class="basic label-container"/><g transform="translate(-93.234375, -12)" style="" class="label"><rect/><foreignObject height="24" width="186.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Typesense\nSearch Engine</p></span></div></foreignObject></g></g><g transform="translate(335.41015625, 913.3712425231934)" id="flowchart-ObjectStorage-15" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Object Storage\nMedia Files</p></span></div></foreignObject></g></g></g></g></g></svg>