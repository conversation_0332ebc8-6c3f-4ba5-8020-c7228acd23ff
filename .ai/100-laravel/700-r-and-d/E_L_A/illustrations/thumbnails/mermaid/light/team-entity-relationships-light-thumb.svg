<svg aria-roledescription="er" role="graphics-document document" viewBox="-80 0 1586.3968505859375 1788.25" style="max-width: 1586.4px; background-color: white;" class="erDiagram" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#552222;}#my-svg .error-text{fill:#552222;stroke:#552222;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#666666;stroke:#666666;}#my-svg .marker.cross{stroke:#666666;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg .entityBox{fill:#ECECFF;stroke:#9370DB;}#my-svg .relationshipLabelBox{fill:#ffffff;opacity:0.7;background-color:#ffffff;}#my-svg .relationshipLabelBox rect{opacity:0.5;}#my-svg .labelBkg{background-color:rgba(255, 255, 255, 0.5);}#my-svg .edgeLabel .label{fill:#9370DB;font-size:14px;}#my-svg .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#my-svg .edge-pattern-dashed{stroke-dasharray:8,8;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#my-svg .relationshipLine{stroke:#666666;stroke-width:1;fill:none;}#my-svg .marker{fill:none!important;stroke:#666666!important;stroke-width:1;}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><defs><marker orient="auto" markerHeight="18" markerWidth="18" refY="9" refX="0" class="marker onlyOne er" id="my-svg_er-onlyOneStart"><path d="M9,0 L9,18 M15,0 L15,18"/></marker></defs><defs><marker orient="auto" markerHeight="18" markerWidth="18" refY="9" refX="18" class="marker onlyOne er" id="my-svg_er-onlyOneEnd"><path d="M3,0 L3,18 M9,0 L9,18"/></marker></defs><defs><marker orient="auto" markerHeight="18" markerWidth="30" refY="9" refX="0" class="marker zeroOrOne er" id="my-svg_er-zeroOrOneStart"><circle r="6" cy="9" cx="21" fill="white"/><path d="M9,0 L9,18"/></marker></defs><defs><marker orient="auto" markerHeight="18" markerWidth="30" refY="9" refX="30" class="marker zeroOrOne er" id="my-svg_er-zeroOrOneEnd"><circle r="6" cy="9" cx="9" fill="white"/><path d="M21,0 L21,18"/></marker></defs><defs><marker orient="auto" markerHeight="36" markerWidth="45" refY="18" refX="18" class="marker oneOrMore er" id="my-svg_er-oneOrMoreStart"><path d="M0,18 Q 18,0 36,18 Q 18,36 0,18 M42,9 L42,27"/></marker></defs><defs><marker orient="auto" markerHeight="36" markerWidth="45" refY="18" refX="27" class="marker oneOrMore er" id="my-svg_er-oneOrMoreEnd"><path d="M3,9 L3,27 M9,18 Q27,0 45,18 Q27,36 9,18"/></marker></defs><defs><marker orient="auto" markerHeight="36" markerWidth="57" refY="18" refX="18" class="marker zeroOrMore er" id="my-svg_er-zeroOrMoreStart"><circle r="6" cy="18" cx="48" fill="white"/><path d="M0,18 Q18,0 36,18 Q18,36 0,18"/></marker></defs><defs><marker orient="auto" markerHeight="36" markerWidth="57" refY="18" refX="39" class="marker zeroOrMore er" id="my-svg_er-zeroOrMoreEnd"><circle r="6" cy="18" cx="9" fill="white"/><path d="M21,18 Q39,0 57,18 Q39,36 21,18"/></marker></defs><g class="root"><g class="clusters"/><g class="edgePaths"><path marker-start="url(#my-svg_er-onlyOneStart)" style="undefined;" class="edge-thickness-normal edge-pattern-solid relationshipLine" id="entity-TEAM-0-cyclic-special-1" d="M928.738,823.662L958.066,855.468C987.394,887.275,1046.05,950.887,1075.378,1033.852C1104.706,1116.817,1104.706,1219.133,1104.706,1270.292L1104.706,1321.45"/><path style="undefined;" class="edge-thickness-normal edge-pattern-solid relationshipLine" id="entity-TEAM-0-cyclic-special-mid" d="M1104.706,1321.55L1104.706,1372.708C1104.706,1423.867,1104.706,1526.183,1006.656,1585.766C908.605,1645.349,712.504,1662.197,614.454,1670.621L516.403,1679.046"/><path marker-end="url(#my-svg_er-zeroOrMoreEnd)" style="undefined;" class="edge-thickness-normal edge-pattern-solid relationshipLine" id="entity-TEAM-0-cyclic-special-2" d="M516.303,1679.046L418.253,1670.621C320.202,1662.197,124.101,1645.349,26.051,1585.758C-72,1526.167,-72,1423.833,-72,1321.5C-72,1219.167,-72,1116.833,52.516,1018.88C177.033,920.926,426.065,827.352,550.581,780.565L675.098,733.778"/><path marker-end="url(#my-svg_er-oneOrMoreEnd)" marker-start="url(#my-svg_er-onlyOneStart)" style="undefined;" class="edge-thickness-normal edge-pattern-solid relationshipLine" id="id_entity-TEAM-0_entity-CATEGORY-1_1" d="M928.738,759.23L1002.545,801.775C1076.351,844.32,1223.964,929.41,1297.77,980.372C1371.577,1031.333,1371.577,1048.167,1371.577,1056.583L1371.577,1065"/><path marker-end="url(#my-svg_er-zeroOrMoreEnd)" marker-start="url(#my-svg_er-onlyOneStart)" style="undefined;" class="edge-thickness-normal edge-pattern-solid relationshipLine" id="id_entity-TEAM-0_entity-TODO-2_2" d="M675.098,748.356L584.702,792.714C494.307,837.071,313.517,925.785,223.122,992.809C132.727,1059.833,132.727,1105.167,132.727,1127.833L132.727,1150.5"/><path marker-end="url(#my-svg_er-zeroOrMoreEnd)" marker-start="url(#my-svg_er-zeroOrMoreStart)" style="undefined;" class="edge-thickness-normal edge-pattern-solid relationshipLine" id="id_entity-TEAM-0_entity-USER-3_3" d="M675.098,921.67L666.768,937.142C658.438,952.614,641.777,983.557,616.485,1035.945C591.194,1088.333,557.27,1162.167,540.308,1199.083L523.346,1236"/><path marker-end="url(#my-svg_er-zeroOrMoreEnd)" marker-start="url(#my-svg_er-onlyOneStart)" style="undefined;" class="edge-thickness-normal edge-pattern-solid relationshipLine" id="id_entity-TEAM-0_entity-STATUS-4_4" d="M832.167,964L833.083,972.417C833.999,980.833,835.832,997.667,836.748,1028.75C837.664,1059.833,837.664,1105.167,837.664,1127.833L837.664,1150.5"/><path marker-end="url(#my-svg_er-onlyOneEnd)" marker-start="url(#my-svg_er-oneOrMoreStart)" style="undefined;" class="edge-thickness-normal edge-pattern-solid relationshipLine" id="id_entity-TEAM_USER-5_entity-USER-3_5" d="M535.066,234.67L501.573,255.183C468.079,275.697,401.092,316.723,367.599,391.966C334.105,467.208,334.105,576.667,334.105,686.125C334.105,795.583,334.105,905.042,352.138,996.688C370.17,1088.333,406.235,1162.167,424.267,1199.083L442.299,1236"/><path marker-end="url(#my-svg_er-onlyOneEnd)" marker-start="url(#my-svg_er-oneOrMoreStart)" style="undefined;" class="edge-thickness-normal edge-pattern-solid relationshipLine" id="id_entity-TEAM_USER-5_entity-TEAM-0_6" d="M766.324,307.25L772.256,315.667C778.189,324.083,790.053,340.917,795.986,357.75C801.918,374.583,801.918,391.417,801.918,399.833L801.918,408.25"/><path marker-start="url(#my-svg_er-onlyOneStart)" style="undefined;" class="edge-thickness-normal edge-pattern-solid relationshipLine" id="entity-CATEGORY-1-cyclic-special-1" d="M1405.018,1578L1406.115,1586.417C1407.212,1594.833,1409.407,1611.667,1410.504,1628.5C1411.602,1645.333,1411.602,1662.167,1411.602,1670.583L1411.602,1679"/><path style="undefined;" class="edge-thickness-normal edge-pattern-solid relationshipLine" id="entity-CATEGORY-1-cyclic-special-mid" d="M1411.602,1679.1L1411.602,1687.517C1411.602,1695.933,1411.602,1712.767,1404.937,1729.6C1398.273,1746.433,1384.945,1763.267,1378.28,1771.683L1371.616,1780.1"/><path marker-end="url(#my-svg_er-zeroOrMoreEnd)" style="undefined;" class="edge-thickness-normal edge-pattern-solid relationshipLine" id="entity-CATEGORY-1-cyclic-special-2" d="M1371.527,1780.123L1355.967,1771.702C1340.407,1763.282,1309.286,1746.441,1293.726,1729.595C1278.166,1712.75,1278.166,1695.9,1278.166,1679.05C1278.166,1662.2,1278.166,1645.35,1280.727,1628.508C1283.288,1611.667,1288.41,1594.833,1290.971,1586.417L1293.532,1578"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1104.706250000745, 1628.5)" class="edgeLabel"><g transform="translate(-29.171875, -10.5)" class="label"><foreignObject height="21" width="58.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>parent of</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1371.5765625014901, 1014.5)" class="edgeLabel"><g transform="translate(-10.3359375, -10.5)" class="label"><foreignObject height="21" width="20.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>has</p></span></div></foreignObject></g></g><g transform="translate(132.7265625, 1014.5)" class="edgeLabel"><g transform="translate(-31.421875, -10.5)" class="label"><foreignObject height="21" width="62.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>related to</p></span></div></foreignObject></g></g><g transform="translate(625.1171875, 1014.5)" class="edgeLabel"><g transform="translate(-41.15625, -10.5)" class="label"><foreignObject height="21" width="82.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>has members</p></span></div></foreignObject></g></g><g transform="translate(837.6640625, 1014.5)" class="edgeLabel"><g transform="translate(-10.3359375, -10.5)" class="label"><foreignObject height="21" width="20.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>has</p></span></div></foreignObject></g></g><g transform="translate(334.10546875, 686.125)" class="edgeLabel"><g transform="translate(-32.3515625, -10.5)" class="label"><foreignObject height="21" width="64.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>belongs to</p></span></div></foreignObject></g></g><g transform="translate(801.91796875, 357.75)" class="edgeLabel"><g transform="translate(-32.3515625, -10.5)" class="label"><foreignObject height="21" width="64.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>belongs to</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1411.6015625018626, 1729.6000000014901)" class="edgeLabel"><g transform="translate(-29.171875, -10.5)" class="label"><foreignObject height="21" width="58.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>parent of</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(801.91796875, 686.125)" id="entity-TEAM-0" class="node default"><g style=""><path fill="#ECECFF" stroke-width="0" stroke="none" d="M-126.8203125 -277.875 L126.8203125 -277.875 L126.8203125 277.875 L-126.8203125 277.875"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 -277.875 C-25.422436528542946 -277.875, 75.97543944291411 -277.875, 126.8203125 -277.875 M-126.8203125 -277.875 C-34.99587341594716 -277.875, 56.82856566810568 -277.875, 126.8203125 -277.875 M126.8203125 -277.875 C126.8203125 -67.78711696241243, 126.8203125 142.30076607517515, 126.8203125 277.875 M126.8203125 -277.875 C126.8203125 -107.13858417569088, 126.8203125 63.59783164861824, 126.8203125 277.875 M126.8203125 277.875 C42.66528730906194 277.875, -41.489737881876124 277.875, -126.8203125 277.875 M126.8203125 277.875 C47.292517613002445 277.875, -32.23527727399511 277.875, -126.8203125 277.875 M-126.8203125 277.875 C-126.8203125 160.07384292276873, -126.8203125 42.27268584553744, -126.8203125 -277.875 M-126.8203125 277.875 C-126.8203125 90.20049125230128, -126.8203125 -97.47401749539745, -126.8203125 -277.875"/></g><g class="row-rect-odd" style=""><path fill="hsl(0, 0%, 100%)" stroke-width="0" stroke="none" d="M-126.8203125 -149.625 L126.8203125 -149.625 L126.8203125 -106.875 L-126.8203125 -106.875"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 -149.625 C-68.34301582228349 -149.625, -9.865719144566981 -149.625, 126.8203125 -149.625 M-126.8203125 -149.625 C-56.176485422257855 -149.625, 14.46734165548429 -149.625, 126.8203125 -149.625 M126.8203125 -149.625 C126.8203125 -137.28344309873637, 126.8203125 -124.94188619747275, 126.8203125 -106.875 M126.8203125 -149.625 C126.8203125 -134.48394504228625, 126.8203125 -119.34289008457249, 126.8203125 -106.875 M126.8203125 -106.875 C64.39607967355536 -106.875, 1.9718468471107258 -106.875, -126.8203125 -106.875 M126.8203125 -106.875 C75.67280093616293 -106.875, 24.525289372325844 -106.875, -126.8203125 -106.875 M-126.8203125 -106.875 C-126.8203125 -121.99842075143351, -126.8203125 -137.12184150286703, -126.8203125 -149.625 M-126.8203125 -106.875 C-126.8203125 -122.12648789734186, -126.8203125 -137.37797579468372, -126.8203125 -149.625"/></g><g class="row-rect-even" style=""><path fill="hsl(0, 0%, 97.0784313725%)" stroke-width="0" stroke="none" d="M-126.8203125 -106.875 L126.8203125 -106.875 L126.8203125 -64.125 L-126.8203125 -64.125"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 -106.875 C-29.69786157964515 -106.875, 67.4245893407097 -106.875, 126.8203125 -106.875 M-126.8203125 -106.875 C-46.530845023699825 -106.875, 33.75862245260035 -106.875, 126.8203125 -106.875 M126.8203125 -106.875 C126.8203125 -93.13587728759084, 126.8203125 -79.39675457518169, 126.8203125 -64.125 M126.8203125 -106.875 C126.8203125 -95.15513162936513, 126.8203125 -83.43526325873025, 126.8203125 -64.125 M126.8203125 -64.125 C59.466072851784475 -64.125, -7.888166796431051 -64.125, -126.8203125 -64.125 M126.8203125 -64.125 C28.9176475873076 -64.125, -68.9850173253848 -64.125, -126.8203125 -64.125 M-126.8203125 -64.125 C-126.8203125 -72.97457448545377, -126.8203125 -81.82414897090754, -126.8203125 -106.875 M-126.8203125 -64.125 C-126.8203125 -74.00594043780521, -126.8203125 -83.88688087561043, -126.8203125 -106.875"/></g><g class="row-rect-odd" style=""><path fill="hsl(0, 0%, 100%)" stroke-width="0" stroke="none" d="M-126.8203125 -64.125 L126.8203125 -64.125 L126.8203125 -21.375 L-126.8203125 -21.375"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 -64.125 C-26.945748229996013 -64.125, 72.92881604000797 -64.125, 126.8203125 -64.125 M-126.8203125 -64.125 C-57.32842163396114 -64.125, 12.163469232077716 -64.125, 126.8203125 -64.125 M126.8203125 -64.125 C126.8203125 -54.22490373854048, 126.8203125 -44.32480747708097, 126.8203125 -21.375 M126.8203125 -64.125 C126.8203125 -52.006905957363365, 126.8203125 -39.88881191472673, 126.8203125 -21.375 M126.8203125 -21.375 C54.00967376584863 -21.375, -18.800964968302736 -21.375, -126.8203125 -21.375 M126.8203125 -21.375 C25.745311704943788 -21.375, -75.32968909011242 -21.375, -126.8203125 -21.375 M-126.8203125 -21.375 C-126.8203125 -32.48062186765534, -126.8203125 -43.586243735310674, -126.8203125 -64.125 M-126.8203125 -21.375 C-126.8203125 -38.403263842493274, -126.8203125 -55.43152768498654, -126.8203125 -64.125"/></g><g class="row-rect-even" style=""><path fill="hsl(0, 0%, 97.0784313725%)" stroke-width="0" stroke="none" d="M-126.8203125 -21.375 L126.8203125 -21.375 L126.8203125 21.375 L-126.8203125 21.375"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 -21.375 C-46.40536772827349 -21.375, 34.00957704345302 -21.375, 126.8203125 -21.375 M-126.8203125 -21.375 C-50.20320299267895 -21.375, 26.413906514642093 -21.375, 126.8203125 -21.375 M126.8203125 -21.375 C126.8203125 -12.398427362191955, 126.8203125 -3.421854724383909, 126.8203125 21.375 M126.8203125 -21.375 C126.8203125 -10.09270762927131, 126.8203125 1.1895847414573808, 126.8203125 21.375 M126.8203125 21.375 C49.80637209280218 21.375, -27.207568314395644 21.375, -126.8203125 21.375 M126.8203125 21.375 C40.6005477887919 21.375, -45.6192169224162 21.375, -126.8203125 21.375 M-126.8203125 21.375 C-126.8203125 11.024652698220214, -126.8203125 0.6743053964404275, -126.8203125 -21.375 M-126.8203125 21.375 C-126.8203125 8.359579984206622, -126.8203125 -4.655840031586756, -126.8203125 -21.375"/></g><g class="row-rect-odd" style=""><path fill="hsl(0, 0%, 100%)" stroke-width="0" stroke="none" d="M-126.8203125 21.375 L126.8203125 21.375 L126.8203125 64.125 L-126.8203125 64.125"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 21.375 C-33.68720353774103 21.375, 59.445905424517946 21.375, 126.8203125 21.375 M-126.8203125 21.375 C-69.47985678312688 21.375, -12.13940106625374 21.375, 126.8203125 21.375 M126.8203125 21.375 C126.8203125 35.39796618327698, 126.8203125 49.420932366553956, 126.8203125 64.125 M126.8203125 21.375 C126.8203125 31.947441560762925, 126.8203125 42.51988312152585, 126.8203125 64.125 M126.8203125 64.125 C46.05640632228757 64.125, -34.70749985542486 64.125, -126.8203125 64.125 M126.8203125 64.125 C27.02315983621324 64.125, -72.77399282757352 64.125, -126.8203125 64.125 M-126.8203125 64.125 C-126.8203125 50.38102441428192, -126.8203125 36.637048828563834, -126.8203125 21.375 M-126.8203125 64.125 C-126.8203125 52.039862423680255, -126.8203125 39.95472484736051, -126.8203125 21.375"/></g><g class="row-rect-even" style=""><path fill="hsl(0, 0%, 97.0784313725%)" stroke-width="0" stroke="none" d="M-126.8203125 64.125 L126.8203125 64.125 L126.8203125 106.875 L-126.8203125 106.875"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 64.125 C-44.23473726094899 64.125, 38.35083797810202 64.125, 126.8203125 64.125 M-126.8203125 64.125 C-53.81771357038947 64.125, 19.184885359221056 64.125, 126.8203125 64.125 M126.8203125 64.125 C126.8203125 77.05132708891755, 126.8203125 89.9776541778351, 126.8203125 106.875 M126.8203125 64.125 C126.8203125 77.41861290062818, 126.8203125 90.71222580125635, 126.8203125 106.875 M126.8203125 106.875 C59.914957100280645 106.875, -6.990398299438709 106.875, -126.8203125 106.875 M126.8203125 106.875 C39.999786486282574 106.875, -46.82073952743485 106.875, -126.8203125 106.875 M-126.8203125 106.875 C-126.8203125 90.22535876438673, -126.8203125 73.57571752877345, -126.8203125 64.125 M-126.8203125 106.875 C-126.8203125 94.96440028516379, -126.8203125 83.05380057032758, -126.8203125 64.125"/></g><g class="row-rect-odd" style=""><path fill="hsl(0, 0%, 100%)" stroke-width="0" stroke="none" d="M-126.8203125 106.875 L126.8203125 106.875 L126.8203125 149.625 L-126.8203125 149.625"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 106.875 C-33.047321158666946 106.875, 60.72567018266611 106.875, 126.8203125 106.875 M-126.8203125 106.875 C-29.710954592830902 106.875, 67.3984033143382 106.875, 126.8203125 106.875 M126.8203125 106.875 C126.8203125 119.42722167866043, 126.8203125 131.97944335732086, 126.8203125 149.625 M126.8203125 106.875 C126.8203125 117.97986743164833, 126.8203125 129.08473486329666, 126.8203125 149.625 M126.8203125 149.625 C43.65439186315936 149.625, -39.51152877368128 149.625, -126.8203125 149.625 M126.8203125 149.625 C29.079276004230024 149.625, -68.66176049153995 149.625, -126.8203125 149.625 M-126.8203125 149.625 C-126.8203125 137.67730507556834, -126.8203125 125.72961015113668, -126.8203125 106.875 M-126.8203125 149.625 C-126.8203125 139.7858691689668, -126.8203125 129.9467383379336, -126.8203125 106.875"/></g><g class="row-rect-even" style=""><path fill="hsl(0, 0%, 97.0784313725%)" stroke-width="0" stroke="none" d="M-126.8203125 149.625 L126.8203125 149.625 L126.8203125 192.375 L-126.8203125 192.375"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 149.625 C-57.56973594789123 149.625, 11.680840604217536 149.625, 126.8203125 149.625 M-126.8203125 149.625 C-38.27595981130273 149.625, 50.26839287739455 149.625, 126.8203125 149.625 M126.8203125 149.625 C126.8203125 159.11416738019787, 126.8203125 168.60333476039574, 126.8203125 192.375 M126.8203125 149.625 C126.8203125 163.35885162178266, 126.8203125 177.09270324356535, 126.8203125 192.375 M126.8203125 192.375 C46.98337677346073 192.375, -32.85355895307853 192.375, -126.8203125 192.375 M126.8203125 192.375 C51.77691556713674 192.375, -23.26648136572652 192.375, -126.8203125 192.375 M-126.8203125 192.375 C-126.8203125 180.72978440135108, -126.8203125 169.08456880270214, -126.8203125 149.625 M-126.8203125 192.375 C-126.8203125 182.647175185643, -126.8203125 172.919350371286, -126.8203125 149.625"/></g><g class="row-rect-odd" style=""><path fill="hsl(0, 0%, 100%)" stroke-width="0" stroke="none" d="M-126.8203125 192.375 L126.8203125 192.375 L126.8203125 235.125 L-126.8203125 235.125"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 192.375 C-57.03627734519621 192.375, 12.747757809607577 192.375, 126.8203125 192.375 M-126.8203125 192.375 C-63.73312515804676 192.375, -0.6459378160935216 192.375, 126.8203125 192.375 M126.8203125 192.375 C126.8203125 205.41224138868085, 126.8203125 218.44948277736168, 126.8203125 235.125 M126.8203125 192.375 C126.8203125 204.67382627270655, 126.8203125 216.9726525454131, 126.8203125 235.125 M126.8203125 235.125 C48.73888957632215 235.125, -29.342533347355698 235.125, -126.8203125 235.125 M126.8203125 235.125 C60.364194990721714 235.125, -6.091922518556572 235.125, -126.8203125 235.125 M-126.8203125 235.125 C-126.8203125 218.45681141714556, -126.8203125 201.7886228342911, -126.8203125 192.375 M-126.8203125 235.125 C-126.8203125 221.6746660539778, -126.8203125 208.22433210795558, -126.8203125 192.375"/></g><g class="row-rect-even" style=""><path fill="hsl(0, 0%, 97.0784313725%)" stroke-width="0" stroke="none" d="M-126.8203125 235.125 L126.8203125 235.125 L126.8203125 277.875 L-126.8203125 277.875"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 235.125 C-37.237329822554216 235.125, 52.34565285489157 235.125, 126.8203125 235.125 M-126.8203125 235.125 C-50.426464538677564 235.125, 25.967383422644872 235.125, 126.8203125 235.125 M126.8203125 235.125 C126.8203125 251.47061083277757, 126.8203125 267.81622166555513, 126.8203125 277.875 M126.8203125 235.125 C126.8203125 246.66045343870977, 126.8203125 258.19590687741953, 126.8203125 277.875 M126.8203125 277.875 C46.71229969116577 277.875, -33.395713117668464 277.875, -126.8203125 277.875 M126.8203125 277.875 C45.51448144355845 277.875, -35.791349612883096 277.875, -126.8203125 277.875 M-126.8203125 277.875 C-126.8203125 262.7860375382376, -126.8203125 247.69707507647524, -126.8203125 235.125 M-126.8203125 277.875 C-126.8203125 263.15879782157117, -126.8203125 248.4425956431423, -126.8203125 235.125"/></g><g class="row-rect-odd" style=""><path fill="hsl(0, 0%, 100%)" stroke-width="0" stroke="none" d="M-126.8203125 -235.125 L126.8203125 -235.125 L126.8203125 -192.375 L-126.8203125 -192.375"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 -235.125 C-37.04791799166041 -235.125, 52.724476516679175 -235.125, 126.8203125 -235.125 M-126.8203125 -235.125 C-31.75395168382721 -235.125, 63.31240913234558 -235.125, 126.8203125 -235.125 M126.8203125 -235.125 C126.8203125 -220.72343097446102, 126.8203125 -206.32186194892208, 126.8203125 -192.375 M126.8203125 -235.125 C126.8203125 -224.06207807653786, 126.8203125 -212.99915615307572, 126.8203125 -192.375 M126.8203125 -192.375 C70.59865659997601 -192.375, 14.377000699952006 -192.375, -126.8203125 -192.375 M126.8203125 -192.375 C34.37523777883072 -192.375, -58.069836942338554 -192.375, -126.8203125 -192.375 M-126.8203125 -192.375 C-126.8203125 -201.53109823673933, -126.8203125 -210.68719647347865, -126.8203125 -235.125 M-126.8203125 -192.375 C-126.8203125 -207.2053566537354, -126.8203125 -222.03571330747081, -126.8203125 -235.125"/></g><g style="" transform="translate(-19.328125, -268.5)" class="label name"><foreignObject height="24" width="38.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 145px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>TEAM</p></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, -225.75)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, -225.75)" class="label attribute-name"><foreignObject height="24" width="13.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 112px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>id</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, -225.75)" class="label attribute-keys"><foreignObject height="24" width="18.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PK</p></span></div></foreignObject></g><g style="" transform="translate(139.3203125, -225.75)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, -183)" class="label attribute-type"><foreignObject height="24" width="40.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, -183)" class="label attribute-name"><foreignObject height="24" width="39.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 135px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>name</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, -183)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.3203125, -183)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, -140.25)" class="label attribute-type"><foreignObject height="24" width="40.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, -140.25)" class="label attribute-name"><foreignObject height="24" width="27.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 127px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>slug</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, -140.25)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.3203125, -140.25)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, -97.5)" class="label attribute-type"><foreignObject height="24" width="29.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 124px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>text</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, -97.5)" class="label attribute-name"><foreignObject height="24" width="79.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 171px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>description</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, -97.5)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.3203125, -97.5)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, -54.75)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, -54.75)" class="label attribute-name"><foreignObject height="24" width="69.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 161px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>parent_id</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, -54.75)" class="label attribute-keys"><foreignObject height="24" width="17.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g style="" transform="translate(139.3203125, -54.75)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, -12)" class="label attribute-type"><foreignObject height="24" width="40.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, -12)" class="label attribute-name"><foreignObject height="24" width="42.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>status</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, -12)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.3203125, -12)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, 30.75)" class="label attribute-type"><foreignObject height="24" width="76.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 167px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, 30.75)" class="label attribute-name"><foreignObject height="24" width="78.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 166px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>created_at</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, 30.75)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.3203125, 30.75)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, 73.5)" class="label attribute-type"><foreignObject height="24" width="76.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 167px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, 73.5)" class="label attribute-name"><foreignObject height="24" width="82.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 170px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>updated_at</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, 73.5)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.3203125, 73.5)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, 116.25)" class="label attribute-type"><foreignObject height="24" width="76.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 167px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, 116.25)" class="label attribute-name"><foreignObject height="24" width="78.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 166px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>deleted_at</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, 116.25)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.3203125, 116.25)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, 159)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, 159)" class="label attribute-name"><foreignObject height="24" width="80.453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 170px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>created_by</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, 159)" class="label attribute-keys"><foreignObject height="24" width="17.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g style="" transform="translate(139.3203125, 159)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, 201.75)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, 201.75)" class="label attribute-name"><foreignObject height="24" width="84.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 175px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>updated_by</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, 201.75)" class="label attribute-keys"><foreignObject height="24" width="17.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g style="" transform="translate(139.3203125, 201.75)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, 244.5)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, 244.5)" class="label attribute-name"><foreignObject height="24" width="80.265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 170px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>deleted_by</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, 244.5)" class="label attribute-keys"><foreignObject height="24" width="17.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g style="" transform="translate(139.3203125, 244.5)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 -235.125 C-72.88587258434524 -235.125, -18.951432668690487 -235.125, 126.8203125 -235.125 M-126.8203125 -235.125 C-34.281246987388755 -235.125, 58.25781852522249 -235.125, 126.8203125 -235.125"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-25.4765625 -235.125 C-25.4765625 -77.49127818058133, -25.4765625 80.14244363883734, -25.4765625 277.875 M-25.4765625 -235.125 C-25.4765625 -117.90012855709449, -25.4765625 -0.6752571141889803, -25.4765625 277.875"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M83.6796875 -235.125 C83.6796875 -129.669179225944, 83.6796875 -24.213358451888013, 83.6796875 277.875 M83.6796875 -235.125 C83.6796875 -58.968384337348, 83.6796875 117.188231325304, 83.6796875 277.875"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 -192.375 C-27.425560063245513 -192.375, 71.96919237350897 -192.375, 126.8203125 -192.375 M-126.8203125 -192.375 C-67.12793377329241 -192.375, -7.435555046584824 -192.375, 126.8203125 -192.375"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 -149.625 C-47.91564709854332 -149.625, 30.989018302913365 -149.625, 126.8203125 -149.625 M-126.8203125 -149.625 C-75.18836926545501 -149.625, -23.556426030910004 -149.625, 126.8203125 -149.625"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 -106.875 C-34.97232317177429 -106.875, 56.875666156451416 -106.875, 126.8203125 -106.875 M-126.8203125 -106.875 C-39.35618519201631 -106.875, 48.107942115967376 -106.875, 126.8203125 -106.875"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 -64.125 C-30.814482688212408 -64.125, 65.19134712357518 -64.125, 126.8203125 -64.125 M-126.8203125 -64.125 C-62.30067294973381 -64.125, 2.218966600532383 -64.125, 126.8203125 -64.125"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 -21.375 C-47.71170425649058 -21.375, 31.396903987018845 -21.375, 126.8203125 -21.375 M-126.8203125 -21.375 C-35.43278567072167 -21.375, 55.95474115855666 -21.375, 126.8203125 -21.375"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 21.375 C-44.49421126258751 21.375, 37.831889974824975 21.375, 126.8203125 21.375 M-126.8203125 21.375 C-31.91629386636012 21.375, 62.98772476727976 21.375, 126.8203125 21.375"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 64.125 C-38.203423386450126 64.125, 50.41346572709975 64.125, 126.8203125 64.125 M-126.8203125 64.125 C-65.70415307958112 64.125, -4.587993659162223 64.125, 126.8203125 64.125"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 106.875 C-75.6561128760759 106.875, -24.491913252151804 106.875, 126.8203125 106.875 M-126.8203125 106.875 C-33.538008966542094 106.875, 59.74429456691581 106.875, 126.8203125 106.875"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 149.625 C-32.52443696955392 149.625, 61.771438560892165 149.625, 126.8203125 149.625 M-126.8203125 149.625 C-41.22150602796265 149.625, 44.377300444074706 149.625, 126.8203125 149.625"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 192.375 C-28.34500556758273 192.375, 70.13030136483454 192.375, 126.8203125 192.375 M-126.8203125 192.375 C-54.530828760914986 192.375, 17.75865497817003 192.375, 126.8203125 192.375"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 235.125 C-54.44919120670134 235.125, 17.921930086597314 235.125, 126.8203125 235.125 M-126.8203125 235.125 C-42.96948943375972 235.125, 40.88133363248056 235.125, 126.8203125 235.125"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 -235.125 C-62.76876121393248 -235.125, 1.2827900721350431 -235.125, 126.8203125 -235.125 M-126.8203125 -235.125 C-28.687604887556702 -235.125, 69.4451027248866 -235.125, 126.8203125 -235.125"/></g></g><g transform="translate(1371.5765625014901, 1321.5)" id="entity-CATEGORY-1" class="node default"><g style=""><path fill="#ECECFF" stroke-width="0" stroke="none" d="M-126.8203125 -256.5 L126.8203125 -256.5 L126.8203125 256.5 L-126.8203125 256.5"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 -256.5 C-67.43233034565488 -256.5, -8.044348191309751 -256.5, 126.8203125 -256.5 M-126.8203125 -256.5 C-72.68730159217395 -256.5, -18.55429068434789 -256.5, 126.8203125 -256.5 M126.8203125 -256.5 C126.8203125 -106.926522497436, 126.8203125 42.64695500512801, 126.8203125 256.5 M126.8203125 -256.5 C126.8203125 -70.42196063393425, 126.8203125 115.6560787321315, 126.8203125 256.5 M126.8203125 256.5 C33.5381599801801 256.5, -59.743992539639805 256.5, -126.8203125 256.5 M126.8203125 256.5 C73.43249946418481 256.5, 20.044686428369644 256.5, -126.8203125 256.5 M-126.8203125 256.5 C-126.8203125 62.149496829856645, -126.8203125 -132.2010063402867, -126.8203125 -256.5 M-126.8203125 256.5 C-126.8203125 67.25207870599493, -126.8203125 -121.99584258801013, -126.8203125 -256.5"/></g><g class="row-rect-odd" style=""><path fill="hsl(0, 0%, 100%)" stroke-width="0" stroke="none" d="M-126.8203125 -128.25 L126.8203125 -128.25 L126.8203125 -85.5 L-126.8203125 -85.5"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 -128.25 C-44.47315645688826 -128.25, 37.873999586223476 -128.25, 126.8203125 -128.25 M-126.8203125 -128.25 C-52.55743019800596 -128.25, 21.70545210398808 -128.25, 126.8203125 -128.25 M126.8203125 -128.25 C126.8203125 -116.12499431361479, 126.8203125 -103.99998862722958, 126.8203125 -85.5 M126.8203125 -128.25 C126.8203125 -112.03183011313007, 126.8203125 -95.81366022626015, 126.8203125 -85.5 M126.8203125 -85.5 C54.78122066505084 -85.5, -17.257871169898323 -85.5, -126.8203125 -85.5 M126.8203125 -85.5 C48.87348141986466 -85.5, -29.07334966027068 -85.5, -126.8203125 -85.5 M-126.8203125 -85.5 C-126.8203125 -100.83580988891251, -126.8203125 -116.17161977782501, -126.8203125 -128.25 M-126.8203125 -85.5 C-126.8203125 -96.76510649760941, -126.8203125 -108.0302129952188, -126.8203125 -128.25"/></g><g class="row-rect-even" style=""><path fill="hsl(0, 0%, 97.0784313725%)" stroke-width="0" stroke="none" d="M-126.8203125 -85.5 L126.8203125 -85.5 L126.8203125 -42.75 L-126.8203125 -42.75"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 -85.5 C-71.98750123034175 -85.5, -17.1546899606835 -85.5, 126.8203125 -85.5 M-126.8203125 -85.5 C-45.81620575233646 -85.5, 35.187900995327084 -85.5, 126.8203125 -85.5 M126.8203125 -85.5 C126.8203125 -69.35299329459522, 126.8203125 -53.205986589190445, 126.8203125 -42.75 M126.8203125 -85.5 C126.8203125 -70.02772927972896, 126.8203125 -54.55545855945792, 126.8203125 -42.75 M126.8203125 -42.75 C34.47057179039939 -42.75, -57.87916891920122 -42.75, -126.8203125 -42.75 M126.8203125 -42.75 C40.55499995465638 -42.75, -45.71031259068724 -42.75, -126.8203125 -42.75 M-126.8203125 -42.75 C-126.8203125 -55.09875084126517, -126.8203125 -67.44750168253034, -126.8203125 -85.5 M-126.8203125 -42.75 C-126.8203125 -55.36337955789861, -126.8203125 -67.97675911579722, -126.8203125 -85.5"/></g><g class="row-rect-odd" style=""><path fill="hsl(0, 0%, 100%)" stroke-width="0" stroke="none" d="M-126.8203125 -42.75 L126.8203125 -42.75 L126.8203125 0 L-126.8203125 0"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 -42.75 C-47.59949562593464 -42.75, 31.621321248130727 -42.75, 126.8203125 -42.75 M-126.8203125 -42.75 C-59.07837505579461 -42.75, 8.663562388410782 -42.75, 126.8203125 -42.75 M126.8203125 -42.75 C126.8203125 -31.449936695736703, 126.8203125 -20.14987339147341, 126.8203125 0 M126.8203125 -42.75 C126.8203125 -25.796103074301584, 126.8203125 -8.842206148603168, 126.8203125 0 M126.8203125 0 C36.123429680713514 0, -54.57345313857297 0, -126.8203125 0 M126.8203125 0 C42.405092796355504 0, -42.01012690728899 0, -126.8203125 0 M-126.8203125 0 C-126.8203125 -13.827133514964238, -126.8203125 -27.654267029928477, -126.8203125 -42.75 M-126.8203125 0 C-126.8203125 -16.53004263623967, -126.8203125 -33.06008527247934, -126.8203125 -42.75"/></g><g class="row-rect-even" style=""><path fill="hsl(0, 0%, 97.0784313725%)" stroke-width="0" stroke="none" d="M-126.8203125 0 L126.8203125 0 L126.8203125 42.75 L-126.8203125 42.75"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 0 C-31.517403365544013 0, 63.785505768911975 0, 126.8203125 0 M-126.8203125 0 C-64.05826568373845 0, -1.2962188674769237 0, 126.8203125 0 M126.8203125 0 C126.8203125 9.945836670995039, 126.8203125 19.891673341990078, 126.8203125 42.75 M126.8203125 0 C126.8203125 14.809817477119715, 126.8203125 29.61963495423943, 126.8203125 42.75 M126.8203125 42.75 C42.7317848707781 42.75, -41.3567427584438 42.75, -126.8203125 42.75 M126.8203125 42.75 C30.654350420752692 42.75, -65.51161165849462 42.75, -126.8203125 42.75 M-126.8203125 42.75 C-126.8203125 31.886721840413628, -126.8203125 21.02344368082726, -126.8203125 0 M-126.8203125 42.75 C-126.8203125 33.30724888210977, -126.8203125 23.86449776421954, -126.8203125 0"/></g><g class="row-rect-odd" style=""><path fill="hsl(0, 0%, 100%)" stroke-width="0" stroke="none" d="M-126.8203125 42.75 L126.8203125 42.75 L126.8203125 85.5 L-126.8203125 85.5"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 42.75 C-29.522112574886506 42.75, 67.77608735022699 42.75, 126.8203125 42.75 M-126.8203125 42.75 C-53.28302201001152 42.75, 20.254268479976957 42.75, 126.8203125 42.75 M126.8203125 42.75 C126.8203125 56.5583963739233, 126.8203125 70.3667927478466, 126.8203125 85.5 M126.8203125 42.75 C126.8203125 55.40249074026208, 126.8203125 68.05498148052416, 126.8203125 85.5 M126.8203125 85.5 C64.23474473601206 85.5, 1.649176972024108 85.5, -126.8203125 85.5 M126.8203125 85.5 C48.711057279205406 85.5, -29.39819794158919 85.5, -126.8203125 85.5 M-126.8203125 85.5 C-126.8203125 70.31651493242416, -126.8203125 55.1330298648483, -126.8203125 42.75 M-126.8203125 85.5 C-126.8203125 72.17939938572414, -126.8203125 58.858798771448264, -126.8203125 42.75"/></g><g class="row-rect-even" style=""><path fill="hsl(0, 0%, 97.0784313725%)" stroke-width="0" stroke="none" d="M-126.8203125 85.5 L126.8203125 85.5 L126.8203125 128.25 L-126.8203125 128.25"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 85.5 C-72.1220381046027 85.5, -17.423763709205403 85.5, 126.8203125 85.5 M-126.8203125 85.5 C-35.53829866438146 85.5, 55.743715171237085 85.5, 126.8203125 85.5 M126.8203125 85.5 C126.8203125 97.07090942280631, 126.8203125 108.6418188456126, 126.8203125 128.25 M126.8203125 85.5 C126.8203125 94.58528985441356, 126.8203125 103.67057970882713, 126.8203125 128.25 M126.8203125 128.25 C67.98845885173212 128.25, 9.156605203464238 128.25, -126.8203125 128.25 M126.8203125 128.25 C75.54084290316237 128.25, 24.261373306324742 128.25, -126.8203125 128.25 M-126.8203125 128.25 C-126.8203125 113.73887906235421, -126.8203125 99.22775812470843, -126.8203125 85.5 M-126.8203125 128.25 C-126.8203125 115.66214096329269, -126.8203125 103.07428192658537, -126.8203125 85.5"/></g><g class="row-rect-odd" style=""><path fill="hsl(0, 0%, 100%)" stroke-width="0" stroke="none" d="M-126.8203125 128.25 L126.8203125 128.25 L126.8203125 171 L-126.8203125 171"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 128.25 C-44.77102852096053 128.25, 37.278255458078945 128.25, 126.8203125 128.25 M-126.8203125 128.25 C-60.6110878713545 128.25, 5.598136757291002 128.25, 126.8203125 128.25 M126.8203125 128.25 C126.8203125 137.9102855970036, 126.8203125 147.57057119400716, 126.8203125 171 M126.8203125 128.25 C126.8203125 138.7660770566045, 126.8203125 149.28215411320897, 126.8203125 171 M126.8203125 171 C53.90681194253098 171, -19.006688614938042 171, -126.8203125 171 M126.8203125 171 C47.530082719106574 171, -31.760147061786853 171, -126.8203125 171 M-126.8203125 171 C-126.8203125 160.1842636415758, -126.8203125 149.36852728315162, -126.8203125 128.25 M-126.8203125 171 C-126.8203125 158.73642053270672, -126.8203125 146.47284106541343, -126.8203125 128.25"/></g><g class="row-rect-even" style=""><path fill="hsl(0, 0%, 97.0784313725%)" stroke-width="0" stroke="none" d="M-126.8203125 171 L126.8203125 171 L126.8203125 213.75 L-126.8203125 213.75"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 171 C-71.25934435446052 171, -15.698376208921047 171, 126.8203125 171 M-126.8203125 171 C-42.71986713371089 171, 41.380578232578216 171, 126.8203125 171 M126.8203125 171 C126.8203125 186.44357859530066, 126.8203125 201.88715719060136, 126.8203125 213.75 M126.8203125 171 C126.8203125 185.01332910671812, 126.8203125 199.02665821343624, 126.8203125 213.75 M126.8203125 213.75 C69.31002962340514 213.75, 11.799746746810271 213.75, -126.8203125 213.75 M126.8203125 213.75 C34.71407097982832 213.75, -57.39217054034336 213.75, -126.8203125 213.75 M-126.8203125 213.75 C-126.8203125 196.78483533995555, -126.8203125 179.81967067991113, -126.8203125 171 M-126.8203125 213.75 C-126.8203125 201.0725523467209, -126.8203125 188.39510469344177, -126.8203125 171"/></g><g class="row-rect-odd" style=""><path fill="hsl(0, 0%, 100%)" stroke-width="0" stroke="none" d="M-126.8203125 213.75 L126.8203125 213.75 L126.8203125 256.5 L-126.8203125 256.5"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 213.75 C-73.27917513629802 213.75, -19.73803777259603 213.75, 126.8203125 213.75 M-126.8203125 213.75 C-56.05061760345882 213.75, 14.719077293082364 213.75, 126.8203125 213.75 M126.8203125 213.75 C126.8203125 228.08832777600472, 126.8203125 242.42665555200946, 126.8203125 256.5 M126.8203125 213.75 C126.8203125 230.23671312375663, 126.8203125 246.72342624751326, 126.8203125 256.5 M126.8203125 256.5 C40.48022727557043 256.5, -45.859857948859144 256.5, -126.8203125 256.5 M126.8203125 256.5 C75.76496447171553 256.5, 24.709616443431045 256.5, -126.8203125 256.5 M-126.8203125 256.5 C-126.8203125 245.81729736453332, -126.8203125 235.1345947290666, -126.8203125 213.75 M-126.8203125 256.5 C-126.8203125 244.18824052962165, -126.8203125 231.87648105924328, -126.8203125 213.75"/></g><g class="row-rect-even" style=""><path fill="hsl(0, 0%, 100%)" stroke-width="0" stroke="none" d="M-126.8203125 -213.75 L126.8203125 -213.75 L126.8203125 -171 L-126.8203125 -171"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 -213.75 C-58.64568152602948 -213.75, 9.528949447941045 -213.75, 126.8203125 -213.75 M-126.8203125 -213.75 C-63.929756683846506 -213.75, -1.0392008676930118 -213.75, 126.8203125 -213.75 M126.8203125 -213.75 C126.8203125 -204.41616849407646, 126.8203125 -195.08233698815292, 126.8203125 -171 M126.8203125 -213.75 C126.8203125 -197.11318762939084, 126.8203125 -180.47637525878167, 126.8203125 -171 M126.8203125 -171 C72.43309537443704 -171, 18.045878248874075 -171, -126.8203125 -171 M126.8203125 -171 C66.57440606921554 -171, 6.328499638431069 -171, -126.8203125 -171 M-126.8203125 -171 C-126.8203125 -186.59328656574579, -126.8203125 -202.18657313149157, -126.8203125 -213.75 M-126.8203125 -171 C-126.8203125 -183.35569364816033, -126.8203125 -195.71138729632065, -126.8203125 -213.75"/></g><g style="" transform="translate(-37.1640625, -247.125)" class="label name"><foreignObject height="24" width="74.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 184px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>CATEGORY</p></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, -204.375)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, -204.375)" class="label attribute-name"><foreignObject height="24" width="13.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 112px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>id</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, -204.375)" class="label attribute-keys"><foreignObject height="24" width="18.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PK</p></span></div></foreignObject></g><g style="" transform="translate(139.3203125, -204.375)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, -161.625)" class="label attribute-type"><foreignObject height="24" width="40.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, -161.625)" class="label attribute-name"><foreignObject height="24" width="39.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 135px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>name</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, -161.625)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.3203125, -161.625)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, -118.875)" class="label attribute-type"><foreignObject height="24" width="29.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 124px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>text</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, -118.875)" class="label attribute-name"><foreignObject height="24" width="79.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 171px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>description</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, -118.875)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.3203125, -118.875)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, -76.125)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, -76.125)" class="label attribute-name"><foreignObject height="24" width="58.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 152px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>team_id</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, -76.125)" class="label attribute-keys"><foreignObject height="24" width="17.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g style="" transform="translate(139.3203125, -76.125)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, -33.375)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, -33.375)" class="label attribute-name"><foreignObject height="24" width="69.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 161px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>parent_id</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, -33.375)" class="label attribute-keys"><foreignObject height="24" width="17.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g style="" transform="translate(139.3203125, -33.375)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, 9.375)" class="label attribute-type"><foreignObject height="24" width="76.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 167px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, 9.375)" class="label attribute-name"><foreignObject height="24" width="78.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 166px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>created_at</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, 9.375)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.3203125, 9.375)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, 52.125)" class="label attribute-type"><foreignObject height="24" width="76.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 167px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, 52.125)" class="label attribute-name"><foreignObject height="24" width="82.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 170px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>updated_at</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, 52.125)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.3203125, 52.125)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, 94.875)" class="label attribute-type"><foreignObject height="24" width="76.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 167px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, 94.875)" class="label attribute-name"><foreignObject height="24" width="78.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 166px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>deleted_at</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, 94.875)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.3203125, 94.875)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, 137.625)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, 137.625)" class="label attribute-name"><foreignObject height="24" width="80.453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 170px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>created_by</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, 137.625)" class="label attribute-keys"><foreignObject height="24" width="17.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g style="" transform="translate(139.3203125, 137.625)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, 180.375)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, 180.375)" class="label attribute-name"><foreignObject height="24" width="84.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 175px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>updated_by</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, 180.375)" class="label attribute-keys"><foreignObject height="24" width="17.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g style="" transform="translate(139.3203125, 180.375)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, 223.125)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, 223.125)" class="label attribute-name"><foreignObject height="24" width="80.265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 170px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>deleted_by</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, 223.125)" class="label attribute-keys"><foreignObject height="24" width="17.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g style="" transform="translate(139.3203125, 223.125)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 -213.75 C-55.71262062493635 -213.75, 15.395071250127302 -213.75, 126.8203125 -213.75 M-126.8203125 -213.75 C-39.5555975538104 -213.75, 47.7091173923792 -213.75, 126.8203125 -213.75"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-25.4765625 -213.75 C-25.4765625 -41.16932124828557, -25.4765625 131.41135750342886, -25.4765625 256.5 M-25.4765625 -213.75 C-25.4765625 -45.8768806523118, -25.4765625 121.9962386953764, -25.4765625 256.5"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M83.6796875 -213.75 C83.6796875 -28.389854980543532, 83.6796875 156.97029003891294, 83.6796875 256.5 M83.6796875 -213.75 C83.6796875 -43.024005531369454, 83.6796875 127.70198893726109, 83.6796875 256.5"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 -171 C-53.15276196676871 -171, 20.514788566462585 -171, 126.8203125 -171 M-126.8203125 -171 C-57.71570826560789 -171, 11.388895968784226 -171, 126.8203125 -171"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 -128.25 C-45.90514553772594 -128.25, 35.010021424548114 -128.25, 126.8203125 -128.25 M-126.8203125 -128.25 C-30.182644298451564 -128.25, 66.45502390309687 -128.25, 126.8203125 -128.25"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 -85.5 C-27.31871686430958 -85.5, 72.18287877138084 -85.5, 126.8203125 -85.5 M-126.8203125 -85.5 C-70.31287094887318 -85.5, -13.805429397746352 -85.5, 126.8203125 -85.5"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 -42.75 C-72.59723758519281 -42.75, -18.37416267038563 -42.75, 126.8203125 -42.75 M-126.8203125 -42.75 C-30.452713409092453 -42.75, 65.9148856818151 -42.75, 126.8203125 -42.75"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 0 C-70.12640213153108 0, -13.432491763062174 0, 126.8203125 0 M-126.8203125 0 C-63.93436272471896 0, -1.0484129494379175 0, 126.8203125 0"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 42.75 C-45.32595540052837 42.75, 36.168401698943256 42.75, 126.8203125 42.75 M-126.8203125 42.75 C-60.79263475902928 42.75, 5.235042981941433 42.75, 126.8203125 42.75"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 85.5 C-35.60979255034677 85.5, 55.600727399306464 85.5, 126.8203125 85.5 M-126.8203125 85.5 C-46.89905204619329 85.5, 33.02220840761342 85.5, 126.8203125 85.5"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 128.25 C-44.773127605003936 128.25, 37.27405728999213 128.25, 126.8203125 128.25 M-126.8203125 128.25 C-29.5283853167562 128.25, 67.7635418664876 128.25, 126.8203125 128.25"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 171 C-25.923815808862287 171, 74.97268088227543 171, 126.8203125 171 M-126.8203125 171 C-32.59117226888766 171, 61.63796796222468 171, 126.8203125 171"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 213.75 C-31.86340850270436 213.75, 63.09349549459128 213.75, 126.8203125 213.75 M-126.8203125 213.75 C-27.576389361234263 213.75, 71.66753377753147 213.75, 126.8203125 213.75"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.8203125 -213.75 C-35.040973287717904 -213.75, 56.73836592456419 -213.75, 126.8203125 -213.75 M-126.8203125 -213.75 C-41.4035862662019 -213.75, 44.0131399675962 -213.75, 126.8203125 -213.75"/></g></g><g transform="translate(132.7265625, 1321.5)" id="entity-TODO-2" class="node default"><g style=""><path fill="#ECECFF" stroke-width="0" stroke="none" d="M-124.7265625 -171 L124.7265625 -171 L124.7265625 171 L-124.7265625 171"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-124.7265625 -171 C-64.64851343703467 -171, -4.570464374069346 -171, 124.7265625 -171 M-124.7265625 -171 C-66.04338045174597 -171, -7.36019840349195 -171, 124.7265625 -171 M124.7265625 -171 C124.7265625 -97.48436460596494, 124.7265625 -23.968729211929883, 124.7265625 171 M124.7265625 -171 C124.7265625 -82.36897937980946, 124.7265625 6.26204124038108, 124.7265625 171 M124.7265625 171 C41.82112344898924 171, -41.08431560202152 171, -124.7265625 171 M124.7265625 171 C51.198475557219396 171, -22.329611385561208 171, -124.7265625 171 M-124.7265625 171 C-124.7265625 67.43089574572305, -124.7265625 -36.1382085085539, -124.7265625 -171 M-124.7265625 171 C-124.7265625 99.84754991246356, -124.7265625 28.695099824927127, -124.7265625 -171"/></g><g class="row-rect-odd" style=""><path fill="hsl(0, 0%, 100%)" stroke-width="0" stroke="none" d="M-124.7265625 -42.75 L124.7265625 -42.75 L124.7265625 0 L-124.7265625 0"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-124.7265625 -42.75 C-52.038190283410955 -42.75, 20.65018193317809 -42.75, 124.7265625 -42.75 M-124.7265625 -42.75 C-65.3540292043935 -42.75, -5.9814959087869966 -42.75, 124.7265625 -42.75 M124.7265625 -42.75 C124.7265625 -26.88810475567219, 124.7265625 -11.026209511344383, 124.7265625 0 M124.7265625 -42.75 C124.7265625 -27.176376897356654, 124.7265625 -11.602753794713305, 124.7265625 0 M124.7265625 0 C45.557580905676986 0, -33.61140068864603 0, -124.7265625 0 M124.7265625 0 C62.34394573093587 0, -0.038671038128256896 0, -124.7265625 0 M-124.7265625 0 C-124.7265625 -11.479908270383817, -124.7265625 -22.959816540767633, -124.7265625 -42.75 M-124.7265625 0 C-124.7265625 -16.510442425297096, -124.7265625 -33.02088485059419, -124.7265625 -42.75"/></g><g class="row-rect-even" style=""><path fill="hsl(0, 0%, 97.0784313725%)" stroke-width="0" stroke="none" d="M-124.7265625 0 L124.7265625 0 L124.7265625 42.75 L-124.7265625 42.75"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-124.7265625 0 C-53.17325175542179 0, 18.380058989156424 0, 124.7265625 0 M-124.7265625 0 C-66.57628326667565 0, -8.42600403335129 0, 124.7265625 0 M124.7265625 0 C124.7265625 10.775614649457163, 124.7265625 21.551229298914325, 124.7265625 42.75 M124.7265625 0 C124.7265625 16.462628174613087, 124.7265625 32.925256349226174, 124.7265625 42.75 M124.7265625 42.75 C68.0092700547295 42.75, 11.291977609458996 42.75, -124.7265625 42.75 M124.7265625 42.75 C38.1644860883864 42.75, -48.397590323227206 42.75, -124.7265625 42.75 M-124.7265625 42.75 C-124.7265625 33.752512356598, -124.7265625 24.75502471319599, -124.7265625 0 M-124.7265625 42.75 C-124.7265625 27.44823835277554, -124.7265625 12.146476705551084, -124.7265625 0"/></g><g class="row-rect-odd" style=""><path fill="hsl(0, 0%, 100%)" stroke-width="0" stroke="none" d="M-124.7265625 42.75 L124.7265625 42.75 L124.7265625 85.5 L-124.7265625 85.5"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-124.7265625 42.75 C-49.37224319714258 42.75, 25.98207610571484 42.75, 124.7265625 42.75 M-124.7265625 42.75 C-70.73069012909752 42.75, -16.734817758195064 42.75, 124.7265625 42.75 M124.7265625 42.75 C124.7265625 57.908277835634735, 124.7265625 73.06655567126947, 124.7265625 85.5 M124.7265625 42.75 C124.7265625 58.67487994781157, 124.7265625 74.59975989562314, 124.7265625 85.5 M124.7265625 85.5 C41.32427588389544 85.5, -42.07801073220912 85.5, -124.7265625 85.5 M124.7265625 85.5 C47.74608406568309 85.5, -29.234394368633815 85.5, -124.7265625 85.5 M-124.7265625 85.5 C-124.7265625 73.79209682511049, -124.7265625 62.084193650220996, -124.7265625 42.75 M-124.7265625 85.5 C-124.7265625 73.23558675628665, -124.7265625 60.97117351257331, -124.7265625 42.75"/></g><g class="row-rect-even" style=""><path fill="hsl(0, 0%, 97.0784313725%)" stroke-width="0" stroke="none" d="M-124.7265625 85.5 L124.7265625 85.5 L124.7265625 128.25 L-124.7265625 128.25"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-124.7265625 85.5 C-44.08254954145292 85.5, 36.56146341709416 85.5, 124.7265625 85.5 M-124.7265625 85.5 C-71.88462691112426 85.5, -19.04269132224853 85.5, 124.7265625 85.5 M124.7265625 85.5 C124.7265625 97.33618750852688, 124.7265625 109.17237501705377, 124.7265625 128.25 M124.7265625 85.5 C124.7265625 99.41126853102416, 124.7265625 113.32253706204834, 124.7265625 128.25 M124.7265625 128.25 C59.972886809072065 128.25, -4.7807888818558695 128.25, -124.7265625 128.25 M124.7265625 128.25 C62.45903070675272 128.25, 0.19149891350544124 128.25, -124.7265625 128.25 M-124.7265625 128.25 C-124.7265625 113.53405712295509, -124.7265625 98.8181142459102, -124.7265625 85.5 M-124.7265625 128.25 C-124.7265625 118.1275321741108, -124.7265625 108.0050643482216, -124.7265625 85.5"/></g><g class="row-rect-odd" style=""><path fill="hsl(0, 0%, 100%)" stroke-width="0" stroke="none" d="M-124.7265625 128.25 L124.7265625 128.25 L124.7265625 171 L-124.7265625 171"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-124.7265625 128.25 C-37.63864394384473 128.25, 49.449274612310546 128.25, 124.7265625 128.25 M-124.7265625 128.25 C-63.8376098782595 128.25, -2.948657256518999 128.25, 124.7265625 128.25 M124.7265625 128.25 C124.7265625 139.0813656959336, 124.7265625 149.91273139186723, 124.7265625 171 M124.7265625 128.25 C124.7265625 143.28000719606092, 124.7265625 158.31001439212184, 124.7265625 171 M124.7265625 171 C60.901096651899785 171, -2.9243691962004306 171, -124.7265625 171 M124.7265625 171 C71.2503526611508 171, 17.7741428223016 171, -124.7265625 171 M-124.7265625 171 C-124.7265625 161.53624276096187, -124.7265625 152.07248552192374, -124.7265625 128.25 M-124.7265625 171 C-124.7265625 157.81783913923746, -124.7265625 144.63567827847493, -124.7265625 128.25"/></g><g class="row-rect-even" style=""><path fill="hsl(0, 0%, 100%)" stroke-width="0" stroke="none" d="M-124.7265625 -128.25 L124.7265625 -128.25 L124.7265625 -85.5 L-124.7265625 -85.5"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-124.7265625 -128.25 C-69.81561417402415 -128.25, -14.90466584804831 -128.25, 124.7265625 -128.25 M-124.7265625 -128.25 C-71.61563231691485 -128.25, -18.504702133829724 -128.25, 124.7265625 -128.25 M124.7265625 -128.25 C124.7265625 -117.80487892159559, 124.7265625 -107.35975784319119, 124.7265625 -85.5 M124.7265625 -128.25 C124.7265625 -118.53821204460489, 124.7265625 -108.82642408920978, 124.7265625 -85.5 M124.7265625 -85.5 C53.89491550551239 -85.5, -16.936731488975227 -85.5, -124.7265625 -85.5 M124.7265625 -85.5 C49.64727934112102 -85.5, -25.43200381775796 -85.5, -124.7265625 -85.5 M-124.7265625 -85.5 C-124.7265625 -101.81572128507057, -124.7265625 -118.13144257014113, -124.7265625 -128.25 M-124.7265625 -85.5 C-124.7265625 -102.17165745347621, -124.7265625 -118.84331490695243, -124.7265625 -128.25"/></g><g style="" transform="translate(-19.890625, -161.625)" class="label name"><foreignObject height="24" width="39.78125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 144px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>TODO</p></span></div></foreignObject></g><g style="" transform="translate(-112.2265625, -118.875)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-10.8828125, -118.875)" class="label attribute-name"><foreignObject height="24" width="13.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 112px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>id</p></span></div></foreignObject></g><g style="" transform="translate(94.0859375, -118.875)" class="label attribute-keys"><foreignObject height="24" width="18.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PK</p></span></div></foreignObject></g><g style="" transform="translate(137.2265625, -118.875)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-112.2265625, -76.125)" class="label attribute-type"><foreignObject height="24" width="40.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g style="" transform="translate(-10.8828125, -76.125)" class="label attribute-name"><foreignObject height="24" width="30.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 125px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>title</p></span></div></foreignObject></g><g style="" transform="translate(94.0859375, -76.125)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(137.2265625, -76.125)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-112.2265625, -33.375)" class="label attribute-type"><foreignObject height="24" width="29.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 124px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>text</p></span></div></foreignObject></g><g style="" transform="translate(-10.8828125, -33.375)" class="label attribute-name"><foreignObject height="24" width="79.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 171px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>description</p></span></div></foreignObject></g><g style="" transform="translate(94.0859375, -33.375)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(137.2265625, -33.375)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-112.2265625, 9.375)" class="label attribute-type"><foreignObject height="24" width="40.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g style="" transform="translate(-10.8828125, 9.375)" class="label attribute-name"><foreignObject height="24" width="42.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>status</p></span></div></foreignObject></g><g style="" transform="translate(94.0859375, 9.375)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(137.2265625, 9.375)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-112.2265625, 52.125)" class="label attribute-type"><foreignObject height="24" width="76.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 167px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g style="" transform="translate(-10.8828125, 52.125)" class="label attribute-name"><foreignObject height="24" width="67.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 158px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>due_date</p></span></div></foreignObject></g><g style="" transform="translate(94.0859375, 52.125)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(137.2265625, 52.125)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-112.2265625, 94.875)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-10.8828125, 94.875)" class="label attribute-name"><foreignObject height="24" width="52.03125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 147px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>user_id</p></span></div></foreignObject></g><g style="" transform="translate(94.0859375, 94.875)" class="label attribute-keys"><foreignObject height="24" width="17.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g style="" transform="translate(137.2265625, 94.875)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-112.2265625, 137.625)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-10.8828125, 137.625)" class="label attribute-name"><foreignObject height="24" width="58.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 152px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>team_id</p></span></div></foreignObject></g><g style="" transform="translate(94.0859375, 137.625)" class="label attribute-keys"><foreignObject height="24" width="17.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g style="" transform="translate(137.2265625, 137.625)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-124.7265625 -128.25 C-26.09244274515069 -128.25, 72.54167700969862 -128.25, 124.7265625 -128.25 M-124.7265625 -128.25 C-37.08134291176239 -128.25, 50.56387667647522 -128.25, 124.7265625 -128.25"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-23.3828125 -128.25 C-23.3828125 -13.744342994868958, -23.3828125 100.76131401026208, -23.3828125 171 M-23.3828125 -128.25 C-23.3828125 -60.89112934144559, -23.3828125 6.467741317108818, -23.3828125 171"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M81.5859375 -128.25 C81.5859375 -50.78438662011705, 81.5859375 26.681226759765906, 81.5859375 171 M81.5859375 -128.25 C81.5859375 -50.09320958336107, 81.5859375 28.063580833277854, 81.5859375 171"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-124.7265625 -85.5 C-39.838454373202495 -85.5, 45.04965375359501 -85.5, 124.7265625 -85.5 M-124.7265625 -85.5 C-68.73674564422836 -85.5, -12.746928788456714 -85.5, 124.7265625 -85.5"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-124.7265625 -42.75 C-59.76640462024713 -42.75, 5.1937532595057405 -42.75, 124.7265625 -42.75 M-124.7265625 -42.75 C-28.005177797456255 -42.75, 68.71620690508749 -42.75, 124.7265625 -42.75"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-124.7265625 0 C-45.69597534661118 0, 33.334611806777644 0, 124.7265625 0 M-124.7265625 0 C-74.59718617851765 0, -24.46780985703529 0, 124.7265625 0"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-124.7265625 42.75 C-62.6986633224105 42.75, -0.6707641448210069 42.75, 124.7265625 42.75 M-124.7265625 42.75 C-30.214386256247565 42.75, 64.29778998750487 42.75, 124.7265625 42.75"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-124.7265625 85.5 C-30.819995493865292 85.5, 63.086571512269416 85.5, 124.7265625 85.5 M-124.7265625 85.5 C-43.12291567440583 85.5, 38.48073115118834 85.5, 124.7265625 85.5"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-124.7265625 128.25 C-39.895469015416424 128.25, 44.93562446916715 128.25, 124.7265625 128.25 M-124.7265625 128.25 C-43.30663535349083 128.25, 38.113291793018334 128.25, 124.7265625 128.25"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-124.7265625 -128.25 C-46.02392929155373 -128.25, 32.678703916892545 -128.25, 124.7265625 -128.25 M-124.7265625 -128.25 C-65.36364309710662 -128.25, -6.000723694213235 -128.25, 124.7265625 -128.25"/></g></g><g transform="translate(484.0625, 1321.5)" id="entity-USER-3" class="node default"><g style=""><path fill="#ECECFF" stroke-width="0" stroke="none" d="M-86.609375 -85.5 L86.609375 -85.5 L86.609375 85.5 L-86.609375 85.5"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-86.609375 -85.5 C-38.602550424051906 -85.5, 9.404274151896189 -85.5, 86.609375 -85.5 M-86.609375 -85.5 C-51.46908420406766 -85.5, -16.328793408135326 -85.5, 86.609375 -85.5 M86.609375 -85.5 C86.609375 -46.86121323699183, 86.609375 -8.22242647398366, 86.609375 85.5 M86.609375 -85.5 C86.609375 -31.70855568340118, 86.609375 22.08288863319764, 86.609375 85.5 M86.609375 85.5 C39.97426945683988 85.5, -6.660836086320245 85.5, -86.609375 85.5 M86.609375 85.5 C27.215128967340057 85.5, -32.179117065319886 85.5, -86.609375 85.5 M-86.609375 85.5 C-86.609375 20.441798169080954, -86.609375 -44.61640366183809, -86.609375 -85.5 M-86.609375 85.5 C-86.609375 34.57263606254302, -86.609375 -16.354727874913962, -86.609375 -85.5"/></g><g class="row-rect-odd" style=""><path fill="hsl(0, 0%, 100%)" stroke-width="0" stroke="none" d="M-86.609375 42.75 L86.609375 42.75 L86.609375 85.5 L-86.609375 85.5"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-86.609375 42.75 C-38.71459123633607 42.75, 9.180192527327861 42.75, 86.609375 42.75 M-86.609375 42.75 C-47.969068503333396 42.75, -9.328762006666793 42.75, 86.609375 42.75 M86.609375 42.75 C86.609375 51.85471916832569, 86.609375 60.959438336651374, 86.609375 85.5 M86.609375 42.75 C86.609375 58.26476140311924, 86.609375 73.77952280623848, 86.609375 85.5 M86.609375 85.5 C36.92542081815682 85.5, -12.758533363686354 85.5, -86.609375 85.5 M86.609375 85.5 C40.68990699148126 85.5, -5.229561017037483 85.5, -86.609375 85.5 M-86.609375 85.5 C-86.609375 73.08761097376293, -86.609375 60.675221947525884, -86.609375 42.75 M-86.609375 85.5 C-86.609375 75.8034910323404, -86.609375 66.1069820646808, -86.609375 42.75"/></g><g class="row-rect-even" style=""><path fill="hsl(0, 0%, 100%)" stroke-width="0" stroke="none" d="M-86.609375 -42.75 L86.609375 -42.75 L86.609375 0 L-86.609375 0"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-86.609375 -42.75 C-44.08239996325131 -42.75, -1.5554249265026243 -42.75, 86.609375 -42.75 M-86.609375 -42.75 C-50.094418093699325 -42.75, -13.57946118739865 -42.75, 86.609375 -42.75 M86.609375 -42.75 C86.609375 -33.37851129305862, 86.609375 -24.007022586117237, 86.609375 0 M86.609375 -42.75 C86.609375 -26.184956096033336, 86.609375 -9.619912192066671, 86.609375 0 M86.609375 0 C20.974976414810826 0, -44.65942217037835 0, -86.609375 0 M86.609375 0 C28.452683451582068 0, -29.704008096835864 0, -86.609375 0 M-86.609375 0 C-86.609375 -11.849586227048581, -86.609375 -23.699172454097162, -86.609375 -42.75 M-86.609375 0 C-86.609375 -15.629903409022697, -86.609375 -31.259806818045394, -86.609375 -42.75"/></g><g style="" transform="translate(-17.9765625, -76.125)" class="label name"><foreignObject height="24" width="35.953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 141px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>USER</p></span></div></foreignObject></g><g style="" transform="translate(-74.109375, -33.375)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-8.734375, -33.375)" class="label attribute-name"><foreignObject height="24" width="13.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 112px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>id</p></span></div></foreignObject></g><g style="" transform="translate(55.96875, -33.375)" class="label attribute-keys"><foreignObject height="24" width="18.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PK</p></span></div></foreignObject></g><g style="" transform="translate(99.109375, -33.375)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-74.109375, 9.375)" class="label attribute-type"><foreignObject height="24" width="40.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g style="" transform="translate(-8.734375, 9.375)" class="label attribute-name"><foreignObject height="24" width="39.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 135px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>name</p></span></div></foreignObject></g><g style="" transform="translate(55.96875, 9.375)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(99.109375, 9.375)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-74.109375, 52.125)" class="label attribute-type"><foreignObject height="24" width="40.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g style="" transform="translate(-8.734375, 52.125)" class="label attribute-name"><foreignObject height="24" width="39.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>email</p></span></div></foreignObject></g><g style="" transform="translate(55.96875, 52.125)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(99.109375, 52.125)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-86.609375 -42.75 C-26.832245640386454 -42.75, 32.94488371922709 -42.75, 86.609375 -42.75 M-86.609375 -42.75 C-39.83813555878043 -42.75, 6.933103882439141 -42.75, 86.609375 -42.75"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-21.234375 -42.75 C-21.234375 6.012515667092785, -21.234375 54.77503133418557, -21.234375 85.5 M-21.234375 -42.75 C-21.234375 7.11907707644766, -21.234375 56.98815415289532, -21.234375 85.5"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M43.46875 -42.75 C43.46875 2.357683367377369, 43.46875 47.46536673475474, 43.46875 85.5 M43.46875 -42.75 C43.46875 -15.863574976662356, 43.46875 11.022850046675288, 43.46875 85.5"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-86.609375 0 C-18.1885231669535 0, 50.232328666093 0, 86.609375 0 M-86.609375 0 C-51.02364814586284 0, -15.437921291725687 0, 86.609375 0"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-86.609375 42.75 C-21.86948355494981 42.75, 42.87040789010038 42.75, 86.609375 42.75 M-86.609375 42.75 C-24.772382416374874 42.75, 37.06461016725025 42.75, 86.609375 42.75"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-86.609375 -42.75 C-42.432536893372905 -42.75, 1.7443012132541895 -42.75, 86.609375 -42.75 M-86.609375 -42.75 C-45.80617429268218 -42.75, -5.002973585364359 -42.75, 86.609375 -42.75"/></g></g><g transform="translate(837.6640625, 1321.5)" id="entity-STATUS-4" class="node default"><g style=""><path fill="#ECECFF" stroke-width="0" stroke="none" d="M-126.9921875 -171 L126.9921875 -171 L126.9921875 171 L-126.9921875 171"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.9921875 -171 C-60.42753959837967 -171, 6.137108303240666 -171, 126.9921875 -171 M-126.9921875 -171 C-64.38374674941281 -171, -1.7753059988256155 -171, 126.9921875 -171 M126.9921875 -171 C126.9921875 -43.52029464307378, 126.9921875 83.95941071385244, 126.9921875 171 M126.9921875 -171 C126.9921875 -100.65961121203304, 126.9921875 -30.319222424066083, 126.9921875 171 M126.9921875 171 C61.287278372103856 171, -4.417630755792288 171, -126.9921875 171 M126.9921875 171 C54.42761138792018 171, -18.13696472415964 171, -126.9921875 171 M-126.9921875 171 C-126.9921875 100.77456326904925, -126.9921875 30.549126538098506, -126.9921875 -171 M-126.9921875 171 C-126.9921875 59.259730618125374, -126.9921875 -52.48053876374925, -126.9921875 -171"/></g><g class="row-rect-odd" style=""><path fill="hsl(0, 0%, 100%)" stroke-width="0" stroke="none" d="M-126.9921875 -42.75 L126.9921875 -42.75 L126.9921875 0 L-126.9921875 0"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.9921875 -42.75 C-51.68635543825964 -42.75, 23.619476623480722 -42.75, 126.9921875 -42.75 M-126.9921875 -42.75 C-43.214568167765464 -42.75, 40.56305116446907 -42.75, 126.9921875 -42.75 M126.9921875 -42.75 C126.9921875 -31.705627819941178, 126.9921875 -20.66125563988236, 126.9921875 0 M126.9921875 -42.75 C126.9921875 -28.52252796055664, 126.9921875 -14.295055921113281, 126.9921875 0 M126.9921875 0 C73.17599866034507 0, 19.35980982069013 0, -126.9921875 0 M126.9921875 0 C47.178096471958526 0, -32.63599455608295 0, -126.9921875 0 M-126.9921875 0 C-126.9921875 -11.743909516719555, -126.9921875 -23.48781903343911, -126.9921875 -42.75 M-126.9921875 0 C-126.9921875 -14.240507310808983, -126.9921875 -28.481014621617966, -126.9921875 -42.75"/></g><g class="row-rect-even" style=""><path fill="hsl(0, 0%, 97.0784313725%)" stroke-width="0" stroke="none" d="M-126.9921875 0 L126.9921875 0 L126.9921875 42.75 L-126.9921875 42.75"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.9921875 0 C-36.99043790336533 0, 53.011311693269334 0, 126.9921875 0 M-126.9921875 0 C-36.59063267523801 0, 53.810922149523975 0, 126.9921875 0 M126.9921875 0 C126.9921875 9.691900920455556, 126.9921875 19.38380184091111, 126.9921875 42.75 M126.9921875 0 C126.9921875 16.88689035029288, 126.9921875 33.77378070058576, 126.9921875 42.75 M126.9921875 42.75 C33.55378605870443 42.75, -59.884615382591136 42.75, -126.9921875 42.75 M126.9921875 42.75 C67.1850195313616 42.75, 7.377851562723194 42.75, -126.9921875 42.75 M-126.9921875 42.75 C-126.9921875 30.262029129886628, -126.9921875 17.77405825977326, -126.9921875 0 M-126.9921875 42.75 C-126.9921875 28.892679040120772, -126.9921875 15.035358080241544, -126.9921875 0"/></g><g class="row-rect-odd" style=""><path fill="hsl(0, 0%, 100%)" stroke-width="0" stroke="none" d="M-126.9921875 42.75 L126.9921875 42.75 L126.9921875 85.5 L-126.9921875 85.5"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.9921875 42.75 C-74.77466064825595 42.75, -22.557133796511906 42.75, 126.9921875 42.75 M-126.9921875 42.75 C-57.19606057826721 42.75, 12.600066343465585 42.75, 126.9921875 42.75 M126.9921875 42.75 C126.9921875 53.58910609188079, 126.9921875 64.42821218376157, 126.9921875 85.5 M126.9921875 42.75 C126.9921875 59.68062595506255, 126.9921875 76.6112519101251, 126.9921875 85.5 M126.9921875 85.5 C63.74771418858752 85.5, 0.5032408771750454 85.5, -126.9921875 85.5 M126.9921875 85.5 C42.480268247825265 85.5, -42.03165100434947 85.5, -126.9921875 85.5 M-126.9921875 85.5 C-126.9921875 74.61870187358396, -126.9921875 63.73740374716792, -126.9921875 42.75 M-126.9921875 85.5 C-126.9921875 70.95759978216792, -126.9921875 56.41519956433583, -126.9921875 42.75"/></g><g class="row-rect-even" style=""><path fill="hsl(0, 0%, 97.0784313725%)" stroke-width="0" stroke="none" d="M-126.9921875 85.5 L126.9921875 85.5 L126.9921875 128.25 L-126.9921875 128.25"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.9921875 85.5 C-46.8402462653642 85.5, 33.3116949692716 85.5, 126.9921875 85.5 M-126.9921875 85.5 C-49.627205046143004 85.5, 27.737777407713992 85.5, 126.9921875 85.5 M126.9921875 85.5 C126.9921875 99.16351964048124, 126.9921875 112.8270392809625, 126.9921875 128.25 M126.9921875 85.5 C126.9921875 98.86669916563292, 126.9921875 112.23339833126585, 126.9921875 128.25 M126.9921875 128.25 C75.60334759715506 128.25, 24.21450769431013 128.25, -126.9921875 128.25 M126.9921875 128.25 C55.28943224847835 128.25, -16.413323003043303 128.25, -126.9921875 128.25 M-126.9921875 128.25 C-126.9921875 117.68153092064912, -126.9921875 107.11306184129825, -126.9921875 85.5 M-126.9921875 128.25 C-126.9921875 112.74227993970591, -126.9921875 97.23455987941182, -126.9921875 85.5"/></g><g class="row-rect-odd" style=""><path fill="hsl(0, 0%, 100%)" stroke-width="0" stroke="none" d="M-126.9921875 128.25 L126.9921875 128.25 L126.9921875 171 L-126.9921875 171"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.9921875 128.25 C-70.29052955853433 128.25, -13.588871617068662 128.25, 126.9921875 128.25 M-126.9921875 128.25 C-54.06558447636584 128.25, 18.861018547268316 128.25, 126.9921875 128.25 M126.9921875 128.25 C126.9921875 145.01650064736322, 126.9921875 161.78300129472643, 126.9921875 171 M126.9921875 128.25 C126.9921875 145.11008964787348, 126.9921875 161.97017929574696, 126.9921875 171 M126.9921875 171 C38.77437017703315 171, -49.4434471459337 171, -126.9921875 171 M126.9921875 171 C56.38268383624903 171, -14.226819827501942 171, -126.9921875 171 M-126.9921875 171 C-126.9921875 161.5202472096618, -126.9921875 152.0404944193236, -126.9921875 128.25 M-126.9921875 171 C-126.9921875 160.1126512713641, -126.9921875 149.2253025427282, -126.9921875 128.25"/></g><g class="row-rect-even" style=""><path fill="hsl(0, 0%, 100%)" stroke-width="0" stroke="none" d="M-126.9921875 -128.25 L126.9921875 -128.25 L126.9921875 -85.5 L-126.9921875 -85.5"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.9921875 -128.25 C-43.40345967169988 -128.25, 40.185268156600245 -128.25, 126.9921875 -128.25 M-126.9921875 -128.25 C-65.7831233981768 -128.25, -4.57405929635361 -128.25, 126.9921875 -128.25 M126.9921875 -128.25 C126.9921875 -111.3819151867331, 126.9921875 -94.5138303734662, 126.9921875 -85.5 M126.9921875 -128.25 C126.9921875 -112.0979696486707, 126.9921875 -95.94593929734141, 126.9921875 -85.5 M126.9921875 -85.5 C40.42403914812648 -85.5, -46.144109203747036 -85.5, -126.9921875 -85.5 M126.9921875 -85.5 C44.69296179988608 -85.5, -37.606263900227844 -85.5, -126.9921875 -85.5 M-126.9921875 -85.5 C-126.9921875 -99.1112778126507, -126.9921875 -112.7225556253014, -126.9921875 -128.25 M-126.9921875 -85.5 C-126.9921875 -97.67116323128566, -126.9921875 -109.84232646257132, -126.9921875 -128.25"/></g><g style="" transform="translate(-25.3359375, -161.625)" class="label name"><foreignObject height="24" width="50.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 157px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>STATUS</p></span></div></foreignObject></g><g style="" transform="translate(-114.4921875, -118.875)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-13.1484375, -118.875)" class="label attribute-name"><foreignObject height="24" width="13.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 112px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>id</p></span></div></foreignObject></g><g style="" transform="translate(96.3515625, -118.875)" class="label attribute-keys"><foreignObject height="24" width="18.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PK</p></span></div></foreignObject></g><g style="" transform="translate(139.4921875, -118.875)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.4921875, -76.125)" class="label attribute-type"><foreignObject height="24" width="40.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g style="" transform="translate(-13.1484375, -76.125)" class="label attribute-name"><foreignObject height="24" width="39.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 135px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>name</p></span></div></foreignObject></g><g style="" transform="translate(96.3515625, -76.125)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.4921875, -76.125)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.4921875, -33.375)" class="label attribute-type"><foreignObject height="24" width="40.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g style="" transform="translate(-13.1484375, -33.375)" class="label attribute-name"><foreignObject height="24" width="47.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 142px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>reason</p></span></div></foreignObject></g><g style="" transform="translate(96.3515625, -33.375)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.4921875, -33.375)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.4921875, 9.375)" class="label attribute-type"><foreignObject height="24" width="29.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>json</p></span></div></foreignObject></g><g style="" transform="translate(-13.1484375, 9.375)" class="label attribute-name"><foreignObject height="24" width="68.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 158px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>metadata</p></span></div></foreignObject></g><g style="" transform="translate(96.3515625, 9.375)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.4921875, 9.375)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.4921875, 52.125)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-13.1484375, 52.125)" class="label attribute-name"><foreignObject height="24" width="66.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 160px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>model_id</p></span></div></foreignObject></g><g style="" transform="translate(96.3515625, 52.125)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.4921875, 52.125)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.4921875, 94.875)" class="label attribute-type"><foreignObject height="24" width="40.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g style="" transform="translate(-13.1484375, 94.875)" class="label attribute-name"><foreignObject height="24" width="84.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 176px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>model_type</p></span></div></foreignObject></g><g style="" transform="translate(96.3515625, 94.875)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.4921875, 94.875)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.4921875, 137.625)" class="label attribute-type"><foreignObject height="24" width="76.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 167px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g style="" transform="translate(-13.1484375, 137.625)" class="label attribute-name"><foreignObject height="24" width="78.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 166px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>created_at</p></span></div></foreignObject></g><g style="" transform="translate(96.3515625, 137.625)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.4921875, 137.625)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.9921875 -128.25 C-37.95365095428271 -128.25, 51.084885591434585 -128.25, 126.9921875 -128.25 M-126.9921875 -128.25 C-36.36867857484813 -128.25, 54.254830350303735 -128.25, 126.9921875 -128.25"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-25.6484375 -128.25 C-25.6484375 -52.16294465803652, -25.6484375 23.924110683926955, -25.6484375 171 M-25.6484375 -128.25 C-25.6484375 -25.301478966645504, -25.6484375 77.64704206670899, -25.6484375 171"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M83.8515625 -128.25 C83.8515625 -52.50528182486981, 83.8515625 23.239436350260377, 83.8515625 171 M83.8515625 -128.25 C83.8515625 -20.316744096758356, 83.8515625 87.61651180648329, 83.8515625 171"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.9921875 -85.5 C-44.59377299987587 -85.5, 37.804641500248266 -85.5, 126.9921875 -85.5 M-126.9921875 -85.5 C-25.505676530889176 -85.5, 75.98083443822165 -85.5, 126.9921875 -85.5"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.9921875 -42.75 C-58.2846986549322 -42.75, 10.4227901901356 -42.75, 126.9921875 -42.75 M-126.9921875 -42.75 C-54.653390614198514 -42.75, 17.68540627160297 -42.75, 126.9921875 -42.75"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.9921875 0 C-66.16770168751134 0, -5.343215875022679 0, 126.9921875 0 M-126.9921875 0 C-30.999146155476737 0, 64.99389518904653 0, 126.9921875 0"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.9921875 42.75 C-49.69447317423757 42.75, 27.603241151524855 42.75, 126.9921875 42.75 M-126.9921875 42.75 C-65.94574066893955 42.75, -4.899293837879114 42.75, 126.9921875 42.75"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.9921875 85.5 C-64.99189268352464 85.5, -2.991597867049279 85.5, 126.9921875 85.5 M-126.9921875 85.5 C-41.37955395962969 85.5, 44.23307958074062 85.5, 126.9921875 85.5"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.9921875 128.25 C-73.0844702380315 128.25, -19.176752976063 128.25, 126.9921875 128.25 M-126.9921875 128.25 C-70.69637299932388 128.25, -14.400558498647769 128.25, 126.9921875 128.25"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-126.9921875 -128.25 C-43.38865914408157 -128.25, 40.214869211836856 -128.25, 126.9921875 -128.25 M-126.9921875 -128.25 C-40.223645999607385 -128.25, 46.54489550078523 -128.25, 126.9921875 -128.25"/></g></g><g transform="translate(660.86328125, 157.625)" id="entity-TEAM_USER-5" class="node default"><g style=""><path fill="#ECECFF" stroke-width="0" stroke="none" d="M-125.796875 -149.625 L125.796875 -149.625 L125.796875 149.625 L-125.796875 149.625"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-125.796875 -149.625 C-50.38451219901708 -149.625, 25.027850601965838 -149.625, 125.796875 -149.625 M-125.796875 -149.625 C-73.80952385715906 -149.625, -21.82217271431813 -149.625, 125.796875 -149.625 M125.796875 -149.625 C125.796875 -45.774072589593075, 125.796875 58.07685482081385, 125.796875 149.625 M125.796875 -149.625 C125.796875 -84.39116594233009, 125.796875 -19.15733188466018, 125.796875 149.625 M125.796875 149.625 C71.34349724474043 149.625, 16.890119489480853 149.625, -125.796875 149.625 M125.796875 149.625 C41.61859658679175 149.625, -42.559681826416494 149.625, -125.796875 149.625 M-125.796875 149.625 C-125.796875 85.27009154860494, -125.796875 20.915183097209876, -125.796875 -149.625 M-125.796875 149.625 C-125.796875 59.204542517516884, -125.796875 -31.21591496496623, -125.796875 -149.625"/></g><g class="row-rect-odd" style=""><path fill="hsl(0, 0%, 100%)" stroke-width="0" stroke="none" d="M-125.796875 -21.375 L125.796875 -21.375 L125.796875 21.375 L-125.796875 21.375"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-125.796875 -21.375 C-65.32456230553035 -21.375, -4.852249611060685 -21.375, 125.796875 -21.375 M-125.796875 -21.375 C-51.292475896488426 -21.375, 23.211923207023148 -21.375, 125.796875 -21.375 M125.796875 -21.375 C125.796875 -5.104364269459975, 125.796875 11.16627146108005, 125.796875 21.375 M125.796875 -21.375 C125.796875 -4.608787676907141, 125.796875 12.157424646185717, 125.796875 21.375 M125.796875 21.375 C25.47178957439968 21.375, -74.85329585120064 21.375, -125.796875 21.375 M125.796875 21.375 C74.52630806878145 21.375, 23.255741137562893 21.375, -125.796875 21.375 M-125.796875 21.375 C-125.796875 5.807544814466162, -125.796875 -9.759910371067676, -125.796875 -21.375 M-125.796875 21.375 C-125.796875 9.417029827505718, -125.796875 -2.540940344988563, -125.796875 -21.375"/></g><g class="row-rect-even" style=""><path fill="hsl(0, 0%, 97.0784313725%)" stroke-width="0" stroke="none" d="M-125.796875 21.375 L125.796875 21.375 L125.796875 64.125 L-125.796875 64.125"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-125.796875 21.375 C-27.72585733775884 21.375, 70.34516032448232 21.375, 125.796875 21.375 M-125.796875 21.375 C-43.303550712849216 21.375, 39.18977357430157 21.375, 125.796875 21.375 M125.796875 21.375 C125.796875 37.47560814253431, 125.796875 53.57621628506862, 125.796875 64.125 M125.796875 21.375 C125.796875 29.968210680924066, 125.796875 38.56142136184813, 125.796875 64.125 M125.796875 64.125 C64.63470025210069 64.125, 3.4725255042013714 64.125, -125.796875 64.125 M125.796875 64.125 C71.47509391297783 64.125, 17.153312825955666 64.125, -125.796875 64.125 M-125.796875 64.125 C-125.796875 49.35964522353377, -125.796875 34.594290447067536, -125.796875 21.375 M-125.796875 64.125 C-125.796875 52.85493209434359, -125.796875 41.58486418868718, -125.796875 21.375"/></g><g class="row-rect-odd" style=""><path fill="hsl(0, 0%, 100%)" stroke-width="0" stroke="none" d="M-125.796875 64.125 L125.796875 64.125 L125.796875 106.875 L-125.796875 106.875"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-125.796875 64.125 C-37.50057740349992 64.125, 50.795720193000164 64.125, 125.796875 64.125 M-125.796875 64.125 C-71.74868603871629 64.125, -17.700497077432573 64.125, 125.796875 64.125 M125.796875 64.125 C125.796875 73.64346451918105, 125.796875 83.1619290383621, 125.796875 106.875 M125.796875 64.125 C125.796875 73.53629891915153, 125.796875 82.94759783830305, 125.796875 106.875 M125.796875 106.875 C67.77050647139336 106.875, 9.744137942786708 106.875, -125.796875 106.875 M125.796875 106.875 C43.87541834990917 106.875, -38.046038300181664 106.875, -125.796875 106.875 M-125.796875 106.875 C-125.796875 92.25937518538097, -125.796875 77.64375037076196, -125.796875 64.125 M-125.796875 106.875 C-125.796875 91.69927069171385, -125.796875 76.5235413834277, -125.796875 64.125"/></g><g class="row-rect-even" style=""><path fill="hsl(0, 0%, 97.0784313725%)" stroke-width="0" stroke="none" d="M-125.796875 106.875 L125.796875 106.875 L125.796875 149.625 L-125.796875 149.625"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-125.796875 106.875 C-45.52919329641482 106.875, 34.738488407170365 106.875, 125.796875 106.875 M-125.796875 106.875 C-29.410003599546116 106.875, 66.97686780090777 106.875, 125.796875 106.875 M125.796875 106.875 C125.796875 116.27849592941573, 125.796875 125.68199185883145, 125.796875 149.625 M125.796875 106.875 C125.796875 118.4099359018492, 125.796875 129.9448718036984, 125.796875 149.625 M125.796875 149.625 C45.38160366635638 149.625, -35.03366766728723 149.625, -125.796875 149.625 M125.796875 149.625 C29.069529194343488 149.625, -67.65781661131302 149.625, -125.796875 149.625 M-125.796875 149.625 C-125.796875 139.29093045875254, -125.796875 128.95686091750505, -125.796875 106.875 M-125.796875 149.625 C-125.796875 141.06310058626173, -125.796875 132.50120117252345, -125.796875 106.875"/></g><g class="row-rect-odd" style=""><path fill="hsl(0, 0%, 100%)" stroke-width="0" stroke="none" d="M-125.796875 -106.875 L125.796875 -106.875 L125.796875 -64.125 L-125.796875 -64.125"/><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-125.796875 -106.875 C-72.47728306567637 -106.875, -19.157691131352735 -106.875, 125.796875 -106.875 M-125.796875 -106.875 C-34.73678130799294 -106.875, 56.323312384014116 -106.875, 125.796875 -106.875 M125.796875 -106.875 C125.796875 -94.44195073863287, 125.796875 -82.00890147726575, 125.796875 -64.125 M125.796875 -106.875 C125.796875 -91.7553471880112, 125.796875 -76.63569437602239, 125.796875 -64.125 M125.796875 -64.125 C33.64624083669459 -64.125, -58.504393326610824 -64.125, -125.796875 -64.125 M125.796875 -64.125 C28.37284317946188 -64.125, -69.05118864107624 -64.125, -125.796875 -64.125 M-125.796875 -64.125 C-125.796875 -77.23543792486879, -125.796875 -90.34587584973758, -125.796875 -106.875 M-125.796875 -64.125 C-125.796875 -78.11195045847562, -125.796875 -92.09890091695124, -125.796875 -106.875"/></g><g style="" transform="translate(-41.5, -140.25)" class="label name"><foreignObject height="24" width="83"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 194px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>TEAM_USER</p></span></div></foreignObject></g><g style="" transform="translate(-113.296875, -97.5)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-11.953125, -97.5)" class="label attribute-name"><foreignObject height="24" width="13.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 112px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>id</p></span></div></foreignObject></g><g style="" transform="translate(95.15625, -97.5)" class="label attribute-keys"><foreignObject height="24" width="18.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PK</p></span></div></foreignObject></g><g style="" transform="translate(138.296875, -97.5)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-113.296875, -54.75)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-11.953125, -54.75)" class="label attribute-name"><foreignObject height="24" width="58.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 152px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>team_id</p></span></div></foreignObject></g><g style="" transform="translate(95.15625, -54.75)" class="label attribute-keys"><foreignObject height="24" width="17.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g style="" transform="translate(138.296875, -54.75)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-113.296875, -12)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-11.953125, -12)" class="label attribute-name"><foreignObject height="24" width="52.03125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 147px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>user_id</p></span></div></foreignObject></g><g style="" transform="translate(95.15625, -12)" class="label attribute-keys"><foreignObject height="24" width="17.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g style="" transform="translate(138.296875, -12)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-113.296875, 30.75)" class="label attribute-type"><foreignObject height="24" width="40.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g style="" transform="translate(-11.953125, 30.75)" class="label attribute-name"><foreignObject height="24" width="28.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 125px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>role</p></span></div></foreignObject></g><g style="" transform="translate(95.15625, 30.75)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(138.296875, 30.75)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-113.296875, 73.5)" class="label attribute-type"><foreignObject height="24" width="76.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 167px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g style="" transform="translate(-11.953125, 73.5)" class="label attribute-name"><foreignObject height="24" width="78.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 166px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>created_at</p></span></div></foreignObject></g><g style="" transform="translate(95.15625, 73.5)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(138.296875, 73.5)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-113.296875, 116.25)" class="label attribute-type"><foreignObject height="24" width="76.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 167px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g style="" transform="translate(-11.953125, 116.25)" class="label attribute-name"><foreignObject height="24" width="82.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 170px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>updated_at</p></span></div></foreignObject></g><g style="" transform="translate(95.15625, 116.25)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(138.296875, 116.25)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-125.796875 -106.875 C-60.35491148910627 -106.875, 5.087052021787457 -106.875, 125.796875 -106.875 M-125.796875 -106.875 C-55.4507824670829 -106.875, 14.895310065834195 -106.875, 125.796875 -106.875"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-24.453125 -106.875 C-24.453125 -45.33470416455847, -24.453125 16.205591670883067, -24.453125 149.625 M-24.453125 -106.875 C-24.453125 -13.571782835357638, -24.453125 79.73143432928472, -24.453125 149.625"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M82.65625 -106.875 C82.65625 -53.63172403424245, 82.65625 -0.3884480684849052, 82.65625 149.625 M82.65625 -106.875 C82.65625 -32.65798375138522, 82.65625 41.559032497229566, 82.65625 149.625"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-125.796875 -64.125 C-35.277527881787066 -64.125, 55.24181923642587 -64.125, 125.796875 -64.125 M-125.796875 -64.125 C-29.91967796466905 -64.125, 65.9575190706619 -64.125, 125.796875 -64.125"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-125.796875 -21.375 C-59.44999357833245 -21.375, 6.896887843335094 -21.375, 125.796875 -21.375 M-125.796875 -21.375 C-70.83423151098725 -21.375, -15.871588021974489 -21.375, 125.796875 -21.375"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-125.796875 21.375 C-28.2518556073545 21.375, 69.293163785291 21.375, 125.796875 21.375 M-125.796875 21.375 C-71.6607689802398 21.375, -17.524662960479574 21.375, 125.796875 21.375"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-125.796875 64.125 C-26.92616006819408 64.125, 71.94455486361184 64.125, 125.796875 64.125 M-125.796875 64.125 C-44.32036122720518 64.125, 37.156152545589634 64.125, 125.796875 64.125"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-125.796875 106.875 C-63.53007943864847 106.875, -1.2632838772969421 106.875, 125.796875 106.875 M-125.796875 106.875 C-47.48544146006296 106.875, 30.825992079874084 106.875, 125.796875 106.875"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#9370DB" d="M-125.796875 -106.875 C-48.53229357792185 -106.875, 28.732287844156303 -106.875, 125.796875 -106.875 M-125.796875 -106.875 C-26.12153956153054 -106.875, 73.55379587693892 -106.875, 125.796875 -106.875"/></g></g><g transform="translate(1104.706250000745, 1321.5)" id="entity-TEAM-0---entity-TEAM-0---1" class="label edgeLabel"><rect height="0.1" width="0.1"/><g transform="translate(0, 0)" style="" class="label"><rect/><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 10px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g></g><g transform="translate(516.3531250003725, 1679.050000000745)" id="entity-TEAM-0---entity-TEAM-0---2" class="label edgeLabel"><rect height="0.1" width="0.1"/><g transform="translate(0, 0)" style="" class="label"><rect/><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 10px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g></g><g transform="translate(1411.6015625018626, 1679.050000000745)" id="entity-CATEGORY-1---entity-CATEGORY-1---1" class="label edgeLabel"><rect height="0.1" width="0.1"/><g transform="translate(0, 0)" style="" class="label"><rect/><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 10px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g></g><g transform="translate(1371.5765625014901, 1780.1500000022352)" id="entity-CATEGORY-1---entity-CATEGORY-1---2" class="label edgeLabel"><rect height="0.1" width="0.1"/><g transform="translate(0, 0)" style="" class="label"><rect/><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 10px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g></g></g></g></g></svg>