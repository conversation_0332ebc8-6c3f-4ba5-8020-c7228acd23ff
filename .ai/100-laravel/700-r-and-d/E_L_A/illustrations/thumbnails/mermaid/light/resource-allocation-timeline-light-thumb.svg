<svg aria-roledescription="gantt" role="graphics-document document" style="max-width: 284px; background-color: white;" viewBox="0 0 284 436" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#552222;}#my-svg .error-text{fill:#552222;stroke:#552222;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#666666;stroke:#666666;}#my-svg .marker.cross{stroke:#666666;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg .mermaid-main-font{font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg .exclude-range{fill:#eeeeee;}#my-svg .section{stroke:none;opacity:0.2;}#my-svg .section0{fill:rgba(102, 102, 255, 0.49);}#my-svg .section2{fill:#fff400;}#my-svg .section1,#my-svg .section3{fill:white;opacity:0.2;}#my-svg .sectionTitle0{fill:#333;}#my-svg .sectionTitle1{fill:#333;}#my-svg .sectionTitle2{fill:#333;}#my-svg .sectionTitle3{fill:#333;}#my-svg .sectionTitle{text-anchor:start;font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg .grid .tick{stroke:lightgrey;opacity:0.8;shape-rendering:crispEdges;}#my-svg .grid .tick text{font-family:"trebuchet ms",verdana,arial,sans-serif;fill:#333;}#my-svg .grid path{stroke-width:0;}#my-svg .today{fill:none;stroke:red;stroke-width:2px;}#my-svg .task{stroke-width:2;}#my-svg .taskText{text-anchor:middle;font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg .taskTextOutsideRight{fill:black;text-anchor:start;font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg .taskTextOutsideLeft{fill:black;text-anchor:end;}#my-svg .task.clickable{cursor:pointer;}#my-svg .taskText.clickable{cursor:pointer;fill:#003163!important;font-weight:bold;}#my-svg .taskTextOutsideLeft.clickable{cursor:pointer;fill:#003163!important;font-weight:bold;}#my-svg .taskTextOutsideRight.clickable{cursor:pointer;fill:#003163!important;font-weight:bold;}#my-svg .taskText0,#my-svg .taskText1,#my-svg .taskText2,#my-svg .taskText3{fill:white;}#my-svg .task0,#my-svg .task1,#my-svg .task2,#my-svg .task3{fill:#8a90dd;stroke:#534fbc;}#my-svg .taskTextOutside0,#my-svg .taskTextOutside2{fill:black;}#my-svg .taskTextOutside1,#my-svg .taskTextOutside3{fill:black;}#my-svg .active0,#my-svg .active1,#my-svg .active2,#my-svg .active3{fill:#bfc7ff;stroke:#534fbc;}#my-svg .activeText0,#my-svg .activeText1,#my-svg .activeText2,#my-svg .activeText3{fill:black!important;}#my-svg .done0,#my-svg .done1,#my-svg .done2,#my-svg .done3{stroke:grey;fill:lightgrey;stroke-width:2;}#my-svg .doneText0,#my-svg .doneText1,#my-svg .doneText2,#my-svg .doneText3{fill:black!important;}#my-svg .crit0,#my-svg .crit1,#my-svg .crit2,#my-svg .crit3{stroke:#ff8888;fill:red;stroke-width:2;}#my-svg .activeCrit0,#my-svg .activeCrit1,#my-svg .activeCrit2,#my-svg .activeCrit3{stroke:#ff8888;fill:#bfc7ff;stroke-width:2;}#my-svg .doneCrit0,#my-svg .doneCrit1,#my-svg .doneCrit2,#my-svg .doneCrit3{stroke:#ff8888;fill:lightgrey;stroke-width:2;cursor:pointer;shape-rendering:crispEdges;}#my-svg .milestone{transform:rotate(45deg) scale(0.8,0.8);}#my-svg .milestoneText{font-style:italic;}#my-svg .doneCritText0,#my-svg .doneCritText1,#my-svg .doneCritText2,#my-svg .doneCritText3{fill:black!important;}#my-svg .activeCritText0,#my-svg .activeCritText1,#my-svg .activeCritText2,#my-svg .activeCritText3{fill:black!important;}#my-svg .titleText{text-anchor:middle;font-size:18px;fill:#333;font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g/><g text-anchor="middle" font-family="sans-serif" font-size="10" fill="none" transform="translate(75, 386)" class="grid"><path d="M0.5,-351V0.5H134.5V-351" stroke="currentColor" class="domain"/><g transform="translate(0.5,0)" opacity="1" class="tick"><line y2="-351" stroke="currentColor"/><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">2025-01-01</text></g><g transform="translate(23.5,0)" opacity="1" class="tick"><line y2="-351" stroke="currentColor"/><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">2025-02-01</text></g><g transform="translate(44.5,0)" opacity="1" class="tick"><line y2="-351" stroke="currentColor"/><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">2025-03-01</text></g><g transform="translate(67.5,0)" opacity="1" class="tick"><line y2="-351" stroke="currentColor"/><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">2025-04-01</text></g><g transform="translate(89.5,0)" opacity="1" class="tick"><line y2="-351" stroke="currentColor"/><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">2025-05-01</text></g><g transform="translate(112.5,0)" opacity="1" class="tick"><line y2="-351" stroke="currentColor"/><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">2025-06-01</text></g><g transform="translate(134.5,0)" opacity="1" class="tick"><line y2="-351" stroke="currentColor"/><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">2025-07-01</text></g></g><g><rect class="section section0" height="24" width="246.5" y="48" x="0"/><rect class="section section1" height="24" width="246.5" y="72" x="0"/><rect class="section section2" height="24" width="246.5" y="168" x="0"/><rect class="section section3" height="24" width="246.5" y="216" x="0"/><rect class="section section1" height="24" width="246.5" y="96" x="0"/><rect class="section section1" height="24" width="246.5" y="120" x="0"/><rect class="section section0" height="24" width="246.5" y="288" x="0"/><rect class="section section0" height="24" width="246.5" y="312" x="0"/><rect class="section section2" height="24" width="246.5" y="144" x="0"/><rect class="section section3" height="24" width="246.5" y="240" x="0"/><rect class="section section0" height="24" width="246.5" y="336" x="0"/><rect class="section section0" height="24" width="246.5" y="360" x="0"/><rect class="section section2" height="24" width="246.5" y="192" x="0"/><rect class="section section3" height="24" width="246.5" y="264" x="0"/></g><g><rect class="task task0" transform-origin="141.5px 60px" height="20" width="133" y="50" x="75" ry="3" rx="3" id="pm"/><rect class="task task1" transform-origin="141.5px 84px" height="20" width="133" y="74" x="75" ry="3" rx="3" id="sd1"/><rect class="task task2" transform-origin="86px 180px" height="20" width="22" y="170" x="75" ry="3" rx="3" id="devops"/><rect class="task task3" transform-origin="141.5px 228px" height="20" width="133" y="218" x="75" ry="3" rx="3" id="dev"/><rect class="task task1" transform-origin="146.5px 108px" height="20" width="123" y="98" x="85" ry="3" rx="3" id="sd2"/><rect class="task task1" transform-origin="153.5px 132px" height="20" width="111" y="122" x="98" ry="3" rx="3" id="fd"/><rect class="task task0" transform-origin="113.5px 300px" height="20" width="11" y="290" x="108" ry="3" rx="3" id="s3"/><rect class="task task0" transform-origin="113.5px 324px" height="20" width="11" y="314" x="108" ry="3" rx="3" id="redis"/><rect class="task task2" transform-origin="163.5px 156px" height="20" width="89" y="146" x="119" ry="3" rx="3" id="qa"/><rect class="task task3" transform-origin="163.5px 252px" height="20" width="89" y="242" x="119" ry="3" rx="3" id="staging"/><rect class="task task0" transform-origin="121.5px 348px" height="20" width="5" y="338" x="119" ry="3" rx="3" id="smtp"/><rect class="task task0" transform-origin="134px 372px" height="20" width="10" y="362" x="129" ry="3" rx="3" id="cicd"/><rect class="task task2" transform-origin="186px 204px" height="20" width="44" y="194" x="164" ry="3" rx="3" id="devops2"/><rect class="task task3" transform-origin="198px 276px" height="20" width="22" y="266" x="187" ry="3" rx="3" id="prod"/><text class="taskText taskText0  width-80.125" y="63.5" x="141.5" font-size="11" id="pm-text">Project Manager       </text><text class="taskText taskText1  width-100.5" y="87.5" x="141.5" font-size="11" id="sd1-text">Senior Laravel Dev 1  </text><text class="taskTextOutsideRight taskTextOutside2  width-82.296875" y="183.5" x="102" font-size="11" id="devops-text">DevOps Engineer       </text><text class="taskText taskText3  width-130.34375" y="231.5" x="141.5" font-size="11" id="dev-text">Development Environment </text><text class="taskText taskText1  width-100.5" y="111.5" x="146.5" font-size="11" id="sd2-text">Senior Laravel Dev 2  </text><text class="taskText taskText1  width-97.46875" y="135.5" x="153.5" font-size="11" id="fd-text">Frontend Developer    </text><text class="taskTextOutsideRight taskTextOutside0  width-65.671875" y="303.5" x="124" font-size="11" id="s3-text">AWS S3 Setup           </text><text class="taskTextOutsideRight taskTextOutside0  width-87.96875" y="327.5" x="124" font-size="11" id="redis-text">Redis Cloud Setup      </text><text class="taskText taskText2  width-59.484375" y="159.5" x="163.5" font-size="11" id="qa-text">QA Engineer           </text><text class="taskTextOutsideLeft taskTextOutside3" y="255.5" x="114" font-size="11" id="staging-text">Staging Environment     </text><text class="taskTextOutsideRight taskTextOutside0  width-94.765625" y="351.5" x="129" font-size="11" id="smtp-text">SMTP Email Service     </text><text class="taskTextOutsideRight taskTextOutside0  width-102.4375" y="375.5" x="144" font-size="11" id="cicd-text">CI/CD Pipeline Setup   </text><text class="taskTextOutsideLeft taskTextOutside2" y="207.5" x="159" font-size="11" id="devops2-text">DevOps Engineer       </text><text class="taskTextOutsideLeft taskTextOutside3" y="279.5" x="182" font-size="11" id="prod-text">Production Environment  </text></g><g><text class="sectionTitle sectionTitle0" font-size="11" y="62" x="10" dy="0em"><tspan x="10" alignment-baseline="central">Project Management</tspan></text><text class="sectionTitle sectionTitle1" font-size="11" y="110" x="10" dy="0em"><tspan x="10" alignment-baseline="central">Development Team</tspan></text><text class="sectionTitle sectionTitle2" font-size="11" y="182" x="10" dy="0em"><tspan x="10" alignment-baseline="central">QA &amp; DevOps</tspan></text><text class="sectionTitle sectionTitle3" font-size="11" y="254" x="10" dy="0em"><tspan x="10" alignment-baseline="central">Infrastructure</tspan></text><text class="sectionTitle sectionTitle0" font-size="11" y="338" x="10" dy="0em"><tspan x="10" alignment-baseline="central">Third-party Services</tspan></text></g><g class="today"><line class="today" y2="411" y1="25" x2="175" x1="175"/></g><text class="titleText" y="25" x="142">Resource Allocation Timeline</text></svg>