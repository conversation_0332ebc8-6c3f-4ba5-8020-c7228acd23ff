<svg aria-roledescription="stateDiagram" role="graphics-document document" viewBox="0 0 1777.41015625 1154" style="max-width: 1777.41px; background-color: white;" class="statediagram" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#552222;}#my-svg .error-text{fill:#552222;stroke:#552222;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#666666;stroke:#666666;}#my-svg .marker.cross{stroke:#666666;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg defs #statediagram-barbEnd{fill:#333333;stroke:#333333;}#my-svg g.stateGroup text{fill:#9370DB;stroke:none;font-size:10px;}#my-svg g.stateGroup text{fill:#333;stroke:none;font-size:10px;}#my-svg g.stateGroup .state-title{font-weight:bolder;fill:#131300;}#my-svg g.stateGroup rect{fill:#ECECFF;stroke:#9370DB;}#my-svg g.stateGroup line{stroke:#666666;stroke-width:1;}#my-svg .transition{stroke:#333333;stroke-width:1;fill:none;}#my-svg .stateGroup .composit{fill:white;border-bottom:1px;}#my-svg .stateGroup .alt-composit{fill:#e0e0e0;border-bottom:1px;}#my-svg .state-note{stroke:#aaaa33;fill:#fff5ad;}#my-svg .state-note text{fill:black;stroke:none;font-size:10px;}#my-svg .stateLabel .box{stroke:none;stroke-width:0;fill:#ECECFF;opacity:0.5;}#my-svg .edgeLabel .label rect{fill:#ECECFF;opacity:0.5;}#my-svg .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#my-svg .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg .edgeLabel .label text{fill:#333;}#my-svg .label div .edgeLabel{color:#333;}#my-svg .stateLabel text{fill:#131300;font-size:10px;font-weight:bold;}#my-svg .node circle.state-start{fill:#666666;stroke:#666666;}#my-svg .node .fork-join{fill:#666666;stroke:#666666;}#my-svg .node circle.state-end{fill:#9370DB;stroke:white;stroke-width:1.5;}#my-svg .end-state-inner{fill:white;stroke-width:1.5;}#my-svg .node rect{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#my-svg .node polygon{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#my-svg #statediagram-barbEnd{fill:#666666;}#my-svg .statediagram-cluster rect{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#my-svg .cluster-label,#my-svg .nodeLabel{color:#131300;}#my-svg .statediagram-cluster rect.outer{rx:5px;ry:5px;}#my-svg .statediagram-state .divider{stroke:#9370DB;}#my-svg .statediagram-state .title-state{rx:5px;ry:5px;}#my-svg .statediagram-cluster.statediagram-cluster .inner{fill:white;}#my-svg .statediagram-cluster.statediagram-cluster-alt .inner{fill:#f0f0f0;}#my-svg .statediagram-cluster .inner{rx:0;ry:0;}#my-svg .statediagram-state rect.basic{rx:5px;ry:5px;}#my-svg .statediagram-state rect.divider{stroke-dasharray:10,10;fill:#f0f0f0;}#my-svg .note-edge{stroke-dasharray:5;}#my-svg .statediagram-note rect{fill:#fff5ad;stroke:#aaaa33;stroke-width:1px;rx:0;ry:0;}#my-svg .statediagram-note rect{fill:#fff5ad;stroke:#aaaa33;stroke-width:1px;rx:0;ry:0;}#my-svg .statediagram-note text{fill:black;}#my-svg .statediagram-note .nodeLabel{color:black;}#my-svg .statediagram .edgeLabel{color:red;}#my-svg #dependencyStart,#my-svg #dependencyEnd{fill:#666666;stroke:#666666;stroke-width:1;}#my-svg .statediagramTitleText{text-anchor:middle;font-size:18px;fill:#333;}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg .invitedState&gt;*{fill:#7F8C8D!important;stroke:#333!important;color:white!important;}#my-svg .invitedState span{fill:#7F8C8D!important;stroke:#333!important;color:white!important;}#my-svg .invitedState tspan{fill:white!important;}#my-svg .pendingState&gt;*{fill:#F39C12!important;stroke:#333!important;color:white!important;}#my-svg .pendingState span{fill:#F39C12!important;stroke:#333!important;color:white!important;}#my-svg .pendingState tspan{fill:white!important;}#my-svg .activeState&gt;*{fill:#27AE60!important;stroke:#333!important;color:white!important;}#my-svg .activeState span{fill:#27AE60!important;stroke:#333!important;color:white!important;}#my-svg .activeState tspan{fill:white!important;}#my-svg .suspendedState&gt;*{fill:#D35400!important;stroke:#333!important;color:white!important;}#my-svg .suspendedState span{fill:#D35400!important;stroke:#333!important;color:white!important;}#my-svg .suspendedState tspan{fill:white!important;}#my-svg .deactivatedState&gt;*{fill:#7F8C8D!important;stroke:#333!important;color:white!important;}#my-svg .deactivatedState span{fill:#7F8C8D!important;stroke:#333!important;color:white!important;}#my-svg .deactivatedState tspan{fill:white!important;}#my-svg .archivedState&gt;*{fill:#34495E!important;stroke:#333!important;color:white!important;}#my-svg .archivedState span{fill:#34495E!important;stroke:#333!important;color:white!important;}#my-svg .archivedState tspan{fill:white!important;}</style><g><defs><marker orient="auto" markerUnits="userSpaceOnUse" markerHeight="14" markerWidth="20" refY="7" refX="19" id="my-svg_stateDiagram-barbEnd"><path d="M 19,7 L9,13 L14,7 L9,1 Z"/></marker></defs><g class="root"><g class="clusters"><g id="Invited----parent" class="note-cluster"><rect fill="none" height="128" width="300" y="210" x="8"/></g><g id="PendingActivation----parent" class="note-cluster"><rect fill="none" height="128" width="300" y="412" x="221.66015625"/></g><g id="Active----parent" class="note-cluster"><rect fill="none" height="128" width="300" y="614" x="391.66015625"/></g><g id="Suspended----parent" class="note-cluster"><rect fill="none" height="152" width="300" y="816" x="680.53515625"/></g><g id="Deactivated----parent" class="note-cluster"><rect fill="none" height="152" width="300" y="816" x="1000.53515625"/></g><g id="Archived----parent" class="note-cluster"><rect fill="none" height="128" width="300" y="1018" x="1290.0625"/></g></g><g class="edgePaths"><path marker-end="url(#my-svg_stateDiagram-barbEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid transition" id="edge0" d="M654.437,15.61L571.697,22.841C488.958,30.073,323.479,44.537,240.739,57.935C158,71.333,158,83.667,158,89.833L158,96"/><path marker-end="url(#my-svg_stateDiagram-barbEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid transition" id="edge1" d="M190.789,124.747L220.934,132.79C251.079,140.832,311.37,156.916,341.515,171.125C371.66,185.333,371.66,197.667,429.342,212.656C487.023,227.646,602.387,245.292,660.068,254.115L717.75,262.938"/><path marker-end="url(#my-svg_stateDiagram-barbEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid transition" id="edge2" d="M668.2,16.702L696.32,23.752C724.441,30.801,780.681,44.901,808.802,61.45C836.922,78,836.922,97,836.922,116C836.922,135,836.922,154,836.922,169.667C836.922,185.333,836.922,197.667,831.553,211.167C826.185,224.667,815.448,239.333,810.08,246.667L804.711,254"/><path marker-end="url(#my-svg_stateDiagram-barbEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid transition" id="edge3" d="M862.391,284.991L920.523,293.826C978.655,302.661,1094.919,320.33,1153.051,335.332C1211.184,350.333,1211.184,362.667,1211.184,375C1211.184,387.333,1211.184,399.667,1211.184,413.167C1211.184,426.667,1211.184,441.333,1211.184,448.667L1211.184,456"/><path marker-end="url(#my-svg_stateDiagram-barbEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid transition" id="edge4" d="M1180.77,480.15L1107.662,490.125C1034.555,500.1,888.34,520.05,815.232,536.192C742.125,552.333,742.125,564.667,742.125,577C742.125,589.333,742.125,601.667,752.255,615.167C762.386,628.667,782.646,643.333,792.777,650.667L802.907,658"/><path marker-end="url(#my-svg_stateDiagram-barbEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid transition" id="edge5" d="M876.465,664.408L904.854,656.007C933.243,647.605,990.022,630.803,1018.411,616.235C1046.801,601.667,1046.801,589.333,1046.801,577C1046.801,564.667,1046.801,552.333,1069.129,537.474C1091.457,522.614,1136.113,505.228,1158.441,496.534L1180.77,487.841"/><path marker-end="url(#my-svg_stateDiagram-barbEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid transition" id="edge6" d="M1241.598,482.438L1286.919,492.032C1332.241,501.625,1422.884,520.813,1468.206,536.573C1513.527,552.333,1513.527,564.667,1513.527,577C1513.527,589.333,1513.527,601.667,1523.162,615.167C1532.796,628.667,1552.065,643.333,1561.699,650.667L1571.334,658"/><path marker-end="url(#my-svg_stateDiagram-barbEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid transition" id="edge7" d="M1626.785,658L1637.483,650.667C1648.181,643.333,1669.577,628.667,1680.275,615.167C1690.973,601.667,1690.973,589.333,1690.973,577C1690.973,564.667,1690.973,552.333,1616.077,536.176C1541.181,520.019,1391.389,500.038,1316.493,490.047L1241.598,480.057"/><path marker-end="url(#my-svg_stateDiagram-barbEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid transition" id="edge8" d="M1241.598,490.74L1258.538,498.95C1275.479,507.16,1309.361,523.58,1326.301,537.957C1343.242,552.333,1343.242,564.667,1343.242,577C1343.242,589.333,1343.242,601.667,1343.242,618.5C1343.242,635.333,1343.242,656.667,1343.242,678C1343.242,699.333,1343.242,720.667,1343.242,737.5C1343.242,754.333,1343.242,766.667,1343.242,779C1343.242,791.333,1343.242,803.667,1355.132,819.167C1367.023,834.667,1390.803,853.333,1402.693,862.667L1414.583,872"/><path marker-end="url(#my-svg_stateDiagram-barbEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid transition" id="edge9" d="M876.465,682.823L970.398,692.685C1064.331,702.548,1252.197,722.274,1346.13,738.304C1440.063,754.333,1440.063,766.667,1440.063,779C1440.063,791.333,1440.063,803.667,1440.063,819.167C1440.063,834.667,1440.063,853.333,1440.063,862.667L1440.063,872"/><path marker-end="url(#my-svg_stateDiagram-barbEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid transition" id="edge10" d="M1597.609,698L1597.609,705.333C1597.609,712.667,1597.609,727.333,1597.609,740.833C1597.609,754.333,1597.609,766.667,1597.609,779C1597.609,791.333,1597.609,803.667,1577.882,819.35C1558.154,835.033,1518.698,854.067,1498.97,863.583L1479.242,873.1"/><path style="fill:none;" class="edge-thickness-normal edge-pattern-solid transition note-edge" id="Invited-Invited----note-11" d="M158,136L158,142.167C158,148.333,158,160.667,158,173C158,185.333,158,197.667,158,208C158,218.333,158,226.667,158,230.833L158,235"/><path style="fill:none;" class="edge-thickness-normal edge-pattern-solid transition note-edge" id="PendingActivation-PendingActivation----note-12" d="M717.75,285.062L660.068,293.885C602.387,302.708,487.023,320.354,429.342,335.344C371.66,350.333,371.66,362.667,371.66,375C371.66,387.333,371.66,399.667,371.66,410C371.66,420.333,371.66,428.667,371.66,432.833L371.66,437"/><path style="fill:none;" class="edge-thickness-normal edge-pattern-solid transition note-edge" id="Active-Active----note-13" d="M1180.77,478.907L1074.251,489.089C967.733,499.272,754.697,519.636,648.178,535.985C541.66,552.333,541.66,564.667,541.66,577C541.66,589.333,541.66,601.667,541.66,612C541.66,622.333,541.66,630.667,541.66,634.833L541.66,639"/><path style="fill:none;" class="edge-thickness-normal edge-pattern-solid transition note-edge" id="Suspended-Suspended----note-14" d="M830.535,698L830.535,705.333C830.535,712.667,830.535,727.333,830.535,740.833C830.535,754.333,830.535,766.667,830.535,779C830.535,791.333,830.535,803.667,830.535,814C830.535,824.333,830.535,832.667,830.535,836.833L830.535,841"/><path style="fill:none;" class="edge-thickness-normal edge-pattern-solid transition note-edge" id="Deactivated-Deactivated----note-15" d="M1546.609,685.301L1480.597,694.751C1414.585,704.201,1282.56,723.1,1216.548,738.717C1150.535,754.333,1150.535,766.667,1150.535,779C1150.535,791.333,1150.535,803.667,1150.535,816C1150.535,828.333,1150.535,840.667,1150.535,846.833L1150.535,853"/><path style="fill:none;" class="edge-thickness-normal edge-pattern-solid transition note-edge" id="Archived-Archived----note-16" d="M1440.063,912L1440.063,921.333C1440.063,930.667,1440.063,949.333,1440.063,962.833C1440.063,976.333,1440.063,984.667,1440.063,993C1440.063,1001.333,1440.063,1009.667,1440.063,1018C1440.063,1026.333,1440.063,1034.667,1440.063,1038.833L1440.063,1043"/></g><g class="edgeLabels"><g transform="translate(158, 59)" class="edgeLabel"><g transform="translate(-60.796875, -12)" class="label"><foreignObject height="24" width="121.59375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>UserInvitedEvent</p></span></div></foreignObject></g></g><g transform="translate(371.66015625, 173)" class="edgeLabel"><g transform="translate(-73.703125, -12)" class="label"><foreignObject height="24" width="147.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>UserRegisteredEvent</p></span></div></foreignObject></g></g><g transform="translate(836.921875, 116)" class="edgeLabel"><g transform="translate(-73.703125, -12)" class="label"><foreignObject height="24" width="147.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>UserRegisteredEvent</p></span></div></foreignObject></g></g><g transform="translate(1211.18359375, 375)" class="edgeLabel"><g transform="translate(-70.2578125, -12)" class="label"><foreignObject height="24" width="140.515625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>UserActivatedEvent</p></span></div></foreignObject></g></g><g transform="translate(742.125, 577)" class="edgeLabel"><g transform="translate(-73.9375, -12)" class="label"><foreignObject height="24" width="147.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>UserSuspendedEvent</p></span></div></foreignObject></g></g><g transform="translate(1046.80078125, 577)" class="edgeLabel"><g transform="translate(-82.8828125, -12)" class="label"><foreignObject height="24" width="165.765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>UserUnsuspendedEvent</p></span></div></foreignObject></g></g><g transform="translate(1513.52734375, 577)" class="edgeLabel"><g transform="translate(-79.0078125, -12)" class="label"><foreignObject height="24" width="158.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>UserDeactivatedEvent</p></span></div></foreignObject></g></g><g transform="translate(1690.97265625, 577)" class="edgeLabel"><g transform="translate(-78.4375, -12)" class="label"><foreignObject height="24" width="156.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>UserReactivatedEvent</p></span></div></foreignObject></g></g><g transform="translate(1343.2421875, 678)" class="edgeLabel"><g transform="translate(-67.1875, -12)" class="label"><foreignObject height="24" width="134.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>UserArchivedEvent</p></span></div></foreignObject></g></g><g transform="translate(1440.0625, 779)" class="edgeLabel"><g transform="translate(-67.1875, -12)" class="label"><foreignObject height="24" width="134.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>UserArchivedEvent</p></span></div></foreignObject></g></g><g transform="translate(1597.609375, 779)" class="edgeLabel"><g transform="translate(-67.1875, -12)" class="label"><foreignObject height="24" width="134.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>UserArchivedEvent</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(661.41015625, 15)" id="state-root_start-2" class="node default"><circle height="14" width="14" r="7" class="state-start"/></g><g transform="translate(158, 116)" id="state-Invited-11" class="node invitedState statediagram-state"><rect height="40" width="65.578125" y="-20" x="-32.7890625" ry="5" rx="5" style="fill:#7F8C8D !important;stroke:#333 !important" class="basic label-container"/><g transform="translate(-24.7890625, -12)" style="color:white !important" class="label"><rect/><foreignObject height="24" width="49.578125"><div xmlns="http://www.w3.org/1999/xhtml" style="color: white !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:white !important"><p>Invited</p></span></div></foreignObject></g></g><g transform="translate(790.0703125, 274)" id="state-PendingActivation-12" class="node pendingState statediagram-state"><rect height="40" width="144.640625" y="-20" x="-72.3203125" ry="5" rx="5" style="fill:#F39C12 !important;stroke:#333 !important" class="basic label-container"/><g transform="translate(-64.3203125, -12)" style="color:white !important" class="label"><rect/><foreignObject height="24" width="128.640625"><div xmlns="http://www.w3.org/1999/xhtml" style="color: white !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:white !important"><p>PendingActivation</p></span></div></foreignObject></g></g><g transform="translate(1211.18359375, 476)" id="state-Active-13" class="node activeState statediagram-state"><rect height="40" width="60.828125" y="-20" x="-30.4140625" ry="5" rx="5" style="fill:#27AE60 !important;stroke:#333 !important" class="basic label-container"/><g transform="translate(-22.4140625, -12)" style="color:white !important" class="label"><rect/><foreignObject height="24" width="44.828125"><div xmlns="http://www.w3.org/1999/xhtml" style="color: white !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:white !important"><p>Active</p></span></div></foreignObject></g></g><g transform="translate(830.53515625, 678)" id="state-Suspended-14" class="node suspendedState statediagram-state"><rect height="40" width="91.859375" y="-20" x="-45.9296875" ry="5" rx="5" style="fill:#D35400 !important;stroke:#333 !important" class="basic label-container"/><g transform="translate(-37.9296875, -12)" style="color:white !important" class="label"><rect/><foreignObject height="24" width="75.859375"><div xmlns="http://www.w3.org/1999/xhtml" style="color: white !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:white !important"><p>Suspended</p></span></div></foreignObject></g></g><g transform="translate(1597.609375, 678)" id="state-Deactivated-15" class="node deactivatedState statediagram-state"><rect height="40" width="102" y="-20" x="-51" ry="5" rx="5" style="fill:#7F8C8D !important;stroke:#333 !important" class="basic label-container"/><g transform="translate(-43, -12)" style="color:white !important" class="label"><rect/><foreignObject height="24" width="86"><div xmlns="http://www.w3.org/1999/xhtml" style="color: white !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:white !important"><p>Deactivated</p></span></div></foreignObject></g></g><g transform="translate(1440.0625, 892)" id="state-Archived-16" class="node archivedState statediagram-state"><rect height="40" width="78.359375" y="-20" x="-39.1796875" ry="5" rx="5" style="fill:#34495E !important;stroke:#333 !important" class="basic label-container"/><g transform="translate(-31.1796875, -12)" style="color:white !important" class="label"><rect/><foreignObject height="24" width="62.359375"><div xmlns="http://www.w3.org/1999/xhtml" style="color: white !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:white !important"><p>Archived</p></span></div></foreignObject></g></g><g transform="translate(158, 274)" id="state-Invited----note-11" class="node statediagram-note"><g class="basic label-container"><path style="" fill="#fff5ad" stroke-width="0" stroke="none" d="M-115 -39 L115 -39 L115 39 L-115 39"/><path style="" fill="none" stroke-width="1.3" stroke="#aaaa33" d="M-115 -39 C-64.88562070683768 -39, -14.77124141367537 -39, 115 -39 M-115 -39 C-37.426738143019804 -39, 40.14652371396039 -39, 115 -39 M115 -39 C115 -22.678948011916663, 115 -6.357896023833327, 115 39 M115 -39 C115 -22.729858863205678, 115 -6.459717726411355, 115 39 M115 39 C25.875351202542916 39, -63.24929759491417 39, -115 39 M115 39 C31.729457217476835 39, -51.54108556504633 39, -115 39 M-115 39 C-115 22.259848229049275, -115 5.519696458098551, -115 -39 M-115 39 C-115 21.595235297891676, -115 4.190470595783353, -115 -39"/></g><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User has been invited but has not registered</p></span></div></foreignObject></g></g><g transform="translate(371.66015625, 476)" id="state-PendingActivation----note-12" class="node statediagram-note"><g class="basic label-container"><path style="" fill="#fff5ad" stroke-width="0" stroke="none" d="M-115 -39 L115 -39 L115 39 L-115 39"/><path style="" fill="none" stroke-width="1.3" stroke="#aaaa33" d="M-115 -39 C-51.12551301847751 -39, 12.748973963044975 -39, 115 -39 M-115 -39 C-31.181024409143333 -39, 52.63795118171333 -39, 115 -39 M115 -39 C115 -16.542416191321045, 115 5.915167617357909, 115 39 M115 -39 C115 -20.749846784337176, 115 -2.4996935686743527, 115 39 M115 39 C25.674857167884184 39, -63.65028566423163 39, -115 39 M115 39 C65.5511246642651 39, 16.10224932853022 39, -115 39 M-115 39 C-115 8.882644149318274, -115 -21.23471170136345, -115 -39 M-115 39 C-115 14.350610896714112, -115 -10.298778206571775, -115 -39"/></g><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User has registered but not activated their account</p></span></div></foreignObject></g></g><g transform="translate(541.66015625, 678)" id="state-Active----note-13" class="node statediagram-note"><g class="basic label-container"><path style="" fill="#fff5ad" stroke-width="0" stroke="none" d="M-115 -39 L115 -39 L115 39 L-115 39"/><path style="" fill="none" stroke-width="1.3" stroke="#aaaa33" d="M-115 -39 C-48.31296282736878 -39, 18.374074345262443 -39, 115 -39 M-115 -39 C-49.32028509793801 -39, 16.359429804123977 -39, 115 -39 M115 -39 C115 -17.547100066416824, 115 3.905799867166351, 115 39 M115 -39 C115 -17.48893139784684, 115 4.022137204306318, 115 39 M115 39 C46.25248270949689 39, -22.495034581006223 39, -115 39 M115 39 C48.90650268239432 39, -17.186994635211363 39, -115 39 M-115 39 C-115 17.95399828383418, -115 -3.092003432331637, -115 -39 M-115 39 C-115 12.708860815721025, -115 -13.582278368557951, -115 -39"/></g><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User has an active account and can access all features</p></span></div></foreignObject></g></g><g transform="translate(830.53515625, 892)" id="state-Suspended----note-14" class="node statediagram-note"><g class="basic label-container"><path style="" fill="#fff5ad" stroke-width="0" stroke="none" d="M-115 -51 L115 -51 L115 51 L-115 51"/><path style="" fill="none" stroke-width="1.3" stroke="#aaaa33" d="M-115 -51 C-53.7557249957698 -51, 7.488550008460393 -51, 115 -51 M-115 -51 C-61.05052272781626 -51, -7.101045455632516 -51, 115 -51 M115 -51 C115 -25.329523473917895, 115 0.3409530521642097, 115 51 M115 -51 C115 -12.773673813537357, 115 25.452652372925286, 115 51 M115 51 C58.74221496351017 51, 2.4844299270203436 51, -115 51 M115 51 C63.6654727561943 51, 12.330945512388595 51, -115 51 M-115 51 C-115 21.719404767600064, -115 -7.561190464799871, -115 -51 M-115 51 C-115 14.055684704234885, -115 -22.88863059153023, -115 -51"/></g><g transform="translate(-100, -36)" style="" class="label"><rect/><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User account has been temporarily suspended<br />by an administrator</p></span></div></foreignObject></g></g><g transform="translate(1150.53515625, 892)" id="state-Deactivated----note-15" class="node statediagram-note"><g class="basic label-container"><path style="" fill="#fff5ad" stroke-width="0" stroke="none" d="M-115 -39 L115 -39 L115 39 L-115 39"/><path style="" fill="none" stroke-width="1.3" stroke="#aaaa33" d="M-115 -39 C-64.30583563106782 -39, -13.61167126213563 -39, 115 -39 M-115 -39 C-41.73901872358782 -39, 31.521962552824363 -39, 115 -39 M115 -39 C115 -17.213544502940074, 115 4.572910994119852, 115 39 M115 -39 C115 -23.359394127698543, 115 -7.718788255397087, 115 39 M115 39 C27.75083047635566 39, -59.49833904728868 39, -115 39 M115 39 C29.477069911162502 39, -56.045860177674996 39, -115 39 M-115 39 C-115 21.567729631695478, -115 4.135459263390956, -115 -39 M-115 39 C-115 17.619683296441217, -115 -3.7606334071175667, -115 -39"/></g><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User has voluntarily deactivated their account</p></span></div></foreignObject></g></g><g transform="translate(1440.0625, 1082)" id="state-Archived----note-16" class="node statediagram-note"><g class="basic label-container"><path style="" fill="#fff5ad" stroke-width="0" stroke="none" d="M-115 -39 L115 -39 L115 39 L-115 39"/><path style="" fill="none" stroke-width="1.3" stroke="#aaaa33" d="M-115 -39 C-29.10820676455306 -39, 56.78358647089388 -39, 115 -39 M-115 -39 C-26.870281376879873 -39, 61.25943724624025 -39, 115 -39 M115 -39 C115 -13.365951260488778, 115 12.268097479022444, 115 39 M115 -39 C115 -8.598511394921324, 115 21.80297721015735, 115 39 M115 39 C25.9194742246612 39, -63.1610515506776 39, -115 39 M115 39 C55.93532423324532 39, -3.1293515335093645 39, -115 39 M-115 39 C-115 13.289854048090167, -115 -12.420291903819667, -115 -39 M-115 39 C-115 18.89148368854094, -115 -1.2170326229181185, -115 -39"/></g><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User account has been archived (soft deleted)</p></span></div></foreignObject></g></g></g></g></g></svg>