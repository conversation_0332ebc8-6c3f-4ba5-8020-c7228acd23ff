<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 2383.8671875 1677.5338134765625" style="max-width: 2383.87px; background-color: #2d333b;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#a44141;}#my-svg .error-text{fill:#ddd;stroke:#ddd;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#ecf0f1;stroke:#ecf0f1;}#my-svg .marker.cross{stroke:#ecf0f1;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#my-svg .cluster-label text{fill:#F9FFFE;}#my-svg .cluster-label span{color:#F9FFFE;}#my-svg .cluster-label span p{background-color:transparent;}#my-svg .label text,#my-svg span{fill:#ccc;color:#ccc;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#my-svg .rough-node .label text,#my-svg .node .label text,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-anchor:middle;}#my-svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#my-svg .rough-node .label,#my-svg .node .label,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-align:center;}#my-svg .node.clickable{cursor:pointer;}#my-svg .root .anchor path{fill:#ecf0f1!important;stroke-width:0;stroke:#ecf0f1;}#my-svg .arrowheadPath{fill:lightgrey;}#my-svg .edgePath .path{stroke:#ecf0f1;stroke-width:2.0px;}#my-svg .flowchart-link{stroke:#ecf0f1;fill:none;}#my-svg .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#my-svg .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#my-svg .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#my-svg .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#my-svg .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#my-svg .cluster text{fill:#F9FFFE;}#my-svg .cluster span{color:#F9FFFE;}#my-svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:#282c34;border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#my-svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#my-svg rect.text{fill:none;stroke-width:0;}#my-svg .icon-shape,#my-svg .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#my-svg .icon-shape p,#my-svg .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#my-svg .icon-shape rect,#my-svg .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"/></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><g class="root"><g class="clusters"><g data-look="classic" id="subGraph3" class="cluster"><rect height="499.296875" width="860.6953125" y="1170.2369537353516" x="208.58203125" style=""/><g transform="translate(538.9296875, 1170.2369537353516)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Side Effects - Async Preferred</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph2" class="cluster"><rect height="637.9400787353516" width="618.0859375" y="240" x="8" style=""/><g transform="translate(282.32421875, 240)" class="cluster-label"><foreignObject height="24" width="69.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Read Side</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph1" class="cluster"><rect height="637.9400787353516" width="330" y="240" x="956.0859375" style=""/><g transform="translate(1021.0859375, 240)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Write Side - Simple CRUD - Optional</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph0" class="cluster"><rect height="1172.5338287353516" width="1069.78125" y="240" x="1306.0859375" style=""/><g transform="translate(1740.9765625, 240)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Write Side - CQRS - Core Logic via hirethunk/verbs</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M1121.086,62L1121.086,66.167C1121.086,70.333,1121.086,78.667,1121.086,86.333C1121.086,94,1121.086,101,1121.086,104.5L1121.086,108"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_CMD_C_0" d="M1799.234,343L1799.234,351.167C1799.234,359.333,1799.234,375.667,1799.311,391.417C1799.388,407.167,1799.541,422.333,1799.617,429.917L1799.694,437.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_D_0" d="M1799.734,719.5L1799.651,723.583C1799.568,727.667,1799.401,735.833,1799.318,745.912C1799.234,755.99,1799.234,767.98,1799.234,773.975L1799.234,779.97"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_E_0" d="M1851.303,837.97L1864.149,844.632C1876.996,851.293,1902.689,864.617,1915.536,875.445C1928.383,886.273,1928.383,894.607,1928.383,913.798C1928.383,932.99,1928.383,963.039,1928.383,978.064L1928.383,993.089"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_F_0" d="M1747.166,837.97L1734.319,844.632C1721.473,851.293,1695.779,864.617,1682.933,875.445C1670.086,886.273,1670.086,894.607,1670.086,913.798C1670.086,932.99,1670.086,963.039,1670.086,978.064L1670.086,993.089"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_G_0" d="M1928.383,1051.089L1928.383,1066.78C1928.383,1082.471,1928.383,1113.854,1928.383,1133.712C1928.383,1153.57,1928.383,1161.904,1928.383,1178.6C1928.383,1195.296,1928.383,1220.356,1928.383,1232.886L1928.383,1245.415"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_H_0" d="M1913.656,829.163L1964.784,837.293C2015.911,845.422,2118.167,861.681,2169.294,873.977C2220.422,886.273,2220.422,894.607,2220.422,913.798C2220.422,932.99,2220.422,963.039,2220.422,978.064L2220.422,993.089"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_G_HIST_0" d="M2220.422,1051.089L2220.422,1066.78C2220.422,1082.471,2220.422,1113.854,2220.422,1133.712C2220.422,1153.57,2220.422,1161.904,2220.422,1178.571C2220.422,1195.239,2220.422,1220.241,2220.422,1232.742L2220.422,1245.243"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_I_0" d="M1684.813,832.138L1643.549,839.772C1602.286,847.405,1519.76,862.673,1478.497,874.473C1437.234,886.273,1437.234,894.607,1437.305,902.357C1437.375,910.107,1437.515,917.274,1437.586,920.857L1437.656,924.441"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_SCRUD_E_SCRUD_0" d="M1121.086,343L1121.086,351.167C1121.086,359.333,1121.086,375.667,1121.086,410C1121.086,444.333,1121.086,496.667,1121.086,522.833L1121.086,549"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_SCRUD_G_SCRUD_0" d="M1121.086,607L1121.086,629.833C1121.086,652.667,1121.086,698.333,1121.086,724.667C1121.086,751,1121.086,758,1121.086,761.5L1121.086,765"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_B_CMD_0" d="M1251.086,163.269L1342.444,171.891C1433.802,180.512,1616.518,197.756,1707.876,210.545C1799.234,223.333,1799.234,231.667,1799.234,239.333C1799.234,247,1799.234,254,1799.234,257.5L1799.234,261"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_B_SCRUD_0" d="M1121.086,190L1121.086,194.167C1121.086,198.333,1121.086,206.667,1121.086,215C1121.086,223.333,1121.086,231.667,1121.086,239.333C1121.086,247,1121.086,254,1121.086,257.5L1121.086,261"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_QUERY_J_0" d="M299.494,343L295.592,351.167C291.69,359.333,283.886,375.667,279.984,408C276.082,440.333,276.082,488.667,276.082,512.833L276.082,537"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_G_READ_0" d="M246.499,619L230.695,639.833C214.892,660.667,183.286,702.333,167.483,726.768C151.68,751.203,151.68,758.406,151.68,762.008L151.68,765.61"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_K_0" d="M310.078,619L328.238,639.833C346.398,660.667,382.719,702.333,400.879,727.162C419.039,751.99,419.039,759.98,419.039,763.975L419.039,767.97"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_B_QUERY_0" d="M991.086,161.362L878.926,170.301C766.767,179.241,542.448,197.121,430.288,210.227C318.129,223.333,318.129,231.667,318.129,239.333C318.129,247,318.129,254,318.129,257.5L318.129,261"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_QUERY_K_OUT_0" d="M413.616,343L433.611,351.167C453.606,359.333,493.596,375.667,547.066,408.274C600.537,440.88,667.487,489.761,700.962,514.201L734.438,538.641"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_SE_L_0" d="M696.297,1388.034L696.214,1392.117C696.13,1396.2,695.964,1404.367,695.88,1412.617C695.797,1420.867,695.797,1429.2,695.797,1436.867C695.797,1444.534,695.797,1451.534,695.797,1455.034L695.797,1458.534"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_M_0" d="M565.797,1525.345L529.229,1532.043C492.66,1538.742,419.523,1552.138,382.955,1562.336C346.387,1572.534,346.387,1579.534,346.387,1583.034L346.387,1586.534"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_N_0" d="M612.044,1540.534L603.096,1544.7C594.148,1548.867,576.251,1557.2,567.303,1564.867C558.355,1572.534,558.355,1579.534,558.355,1583.034L558.355,1586.534"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_O_0" d="M745.82,1540.534L751.165,1544.7C756.509,1548.867,767.198,1557.2,772.542,1564.867C777.887,1572.534,777.887,1579.534,777.887,1583.034L777.887,1586.534"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_P_0" d="M825.797,1534.28L846.476,1539.489C867.155,1544.698,908.513,1555.116,929.192,1563.825C949.871,1572.534,949.871,1579.534,949.871,1583.034L949.871,1586.534"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_I_SE_0" d="M1437.734,1120.737L1437.651,1124.82C1437.568,1128.904,1437.401,1137.07,1437.318,1145.32C1437.234,1153.57,1437.234,1161.904,1328.177,1183.986C1219.119,1206.069,1001.004,1241.901,891.946,1259.817L782.889,1277.733"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1799.234375, 392)" class="edgeLabel"><g transform="translate(-100, -24)" class="label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Sends Verbs Command Object</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(276.08203125, 392)" class="edgeLabel"><g transform="translate(-64.09375, -12)" class="label"><foreignObject height="24" width="128.1875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Query Parameters</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(1121.0859375, 35)" id="flowchart-A-0" class="node default"><rect height="54" width="215.953125" y="-27" x="-107.9765625" style="" class="basic label-container"/><g transform="translate(-77.9765625, -12)" style="" class="label"><rect/><foreignObject height="24" width="155.953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Request / User Action</p></span></div></foreignObject></g></g><g transform="translate(1121.0859375, 151)" id="flowchart-B-1" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Controller / Livewire Component</p></span></div></foreignObject></g></g><g transform="translate(1799.234375, 304)" id="flowchart-B_CMD-2" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Controller / Livewire Component</p></span></div></foreignObject></g></g><g transform="translate(1799.234375, 580)" id="flowchart-C-3" class="node default"><polygon transform="translate(-139,139)" class="label-container" points="139,0 278,-139 139,-278 0,-139"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Command Bus (hirethunk/verbs)</p></span></div></foreignObject></g></g><g transform="translate(1799.234375, 810.9700393676758)" id="flowchart-D-5" class="node default"><rect height="54" width="228.84375" y="-27" x="-114.421875" style="" class="basic label-container"/><g transform="translate(-84.421875, -12)" style="" class="label"><rect/><foreignObject height="24" width="168.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Verb Command Handler</p></span></div></foreignObject></g></g><g transform="translate(1928.3828125, 1024.0885162353516)" id="flowchart-E-7" class="node default"><rect height="54" width="243.1875" y="-27" x="-121.59375" style="" class="basic label-container"/><g transform="translate(-91.59375, -12)" style="" class="label"><rect/><foreignObject height="24" width="183.1875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Domain Services / Models</p></span></div></foreignObject></g></g><g transform="translate(1670.0859375, 1024.0885162353516)" id="flowchart-F-9" class="node default"><rect height="54" width="173.40625" y="-27" x="-86.703125" style="" class="basic label-container"/><g transform="translate(-56.703125, -12)" style="" class="label"><rect/><foreignObject height="24" width="113.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Validation Logic</p></span></div></foreignObject></g></g><g transform="translate(1928.3828125, 1291.3853912353516)" id="flowchart-G-11" class="node default"><path transform="translate(-93.4921875, -41.97520909500676)" style="" class="basic label-container" d="M0,14.983472730004506 a93.4921875,14.983472730004506 0,0,0 186.984375,0 a93.4921875,14.983472730004506 0,0,0 -186.984375,0 l0,53.98347273000451 a93.4921875,14.983472730004506 0,0,0 186.984375,0 l0,-53.98347273000451"/><g transform="translate(-85.9921875, -2)" style="" class="label"><rect/><foreignObject height="24" width="171.984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Database: Persists State</p></span></div></foreignObject></g></g><g transform="translate(2220.421875, 1024.0885162353516)" id="flowchart-H-13" class="node default"><rect height="54" width="240.890625" y="-27" x="-120.4453125" style="" class="basic label-container"/><g transform="translate(-90.4453125, -12)" style="" class="label"><rect/><foreignObject height="24" width="180.890625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Command History (verbs)</p></span></div></foreignObject></g></g><g transform="translate(2220.421875, 1291.3853912353516)" id="flowchart-G_HIST-15" class="node default"><path transform="translate(-95.3046875, -42.147779593049165)" style="" class="basic label-container" d="M0,15.09851972869944 a95.3046875,15.09851972869944 0,0,0 190.609375,0 a95.3046875,15.09851972869944 0,0,0 -190.609375,0 l0,54.09851972869944 a95.3046875,15.09851972869944 0,0,0 190.609375,0 l0,-54.09851972869944"/><g transform="translate(-87.8046875, -2)" style="" class="label"><rect/><foreignObject height="24" width="175.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Database: Stores History</p></span></div></foreignObject></g></g><g transform="translate(1437.234375, 1024.0885162353516)" id="flowchart-I-17" class="node default"><polygon transform="translate(-96.1484375,96.1484375)" class="label-container" points="96.1484375,0 192.296875,-96.1484375 96.1484375,-192.296875 0,-96.1484375"/><g transform="translate(-69.1484375, -12)" style="" class="label"><rect/><foreignObject height="24" width="138.296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Event Bus (Laravel)</p></span></div></foreignObject></g></g><g transform="translate(1121.0859375, 304)" id="flowchart-B_SCRUD-18" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Controller / Livewire Component</p></span></div></foreignObject></g></g><g transform="translate(1121.0859375, 580)" id="flowchart-E_SCRUD-19" class="node default"><rect height="54" width="243.1875" y="-27" x="-121.59375" style="" class="basic label-container"/><g transform="translate(-91.59375, -12)" style="" class="label"><rect/><foreignObject height="24" width="183.1875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Domain Services / Models</p></span></div></foreignObject></g></g><g transform="translate(1121.0859375, 810.9700393676758)" id="flowchart-G_SCRUD-21" class="node default"><path transform="translate(-93.4921875, -41.97520909500676)" style="" class="basic label-container" d="M0,14.983472730004506 a93.4921875,14.983472730004506 0,0,0 186.984375,0 a93.4921875,14.983472730004506 0,0,0 -186.984375,0 l0,53.98347273000451 a93.4921875,14.983472730004506 0,0,0 186.984375,0 l0,-53.98347273000451"/><g transform="translate(-85.9921875, -2)" style="" class="label"><rect/><foreignObject height="24" width="171.984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Database: Persists State</p></span></div></foreignObject></g></g><g transform="translate(318.12890625, 304)" id="flowchart-B_QUERY-26" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Controller / Livewire Component</p></span></div></foreignObject></g></g><g transform="translate(276.08203125, 580)" id="flowchart-J-27" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Query Service / Eloquent Scopes</p></span></div></foreignObject></g></g><g transform="translate(151.6796875, 810.9700393676758)" id="flowchart-G_READ-29" class="node default"><path transform="translate(-87.359375, -41.36033781670316)" style="" class="basic label-container" d="M0,14.573558544468773 a87.359375,14.573558544468773 0,0,0 174.71875,0 a87.359375,14.573558544468773 0,0,0 -174.71875,0 l0,53.573558544468774 a87.359375,14.573558544468773 0,0,0 174.71875,0 l0,-53.573558544468774"/><g transform="translate(-79.859375, -2)" style="" class="label"><rect/><foreignObject height="24" width="159.71875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Database: Reads State</p></span></div></foreignObject></g></g><g transform="translate(419.0390625, 810.9700393676758)" id="flowchart-K-31" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Response Data / View Model</p></span></div></foreignObject></g></g><g transform="translate(791.0859375, 580)" id="flowchart-K_OUT-35" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Response Data / View Model</p></span></div></foreignObject></g></g><g transform="translate(695.796875, 1291.3853912353516)" id="flowchart-I_SE-36" class="node default"><polygon transform="translate(-96.1484375,96.1484375)" class="label-container" points="96.1484375,0 192.296875,-96.1484375 96.1484375,-192.296875 0,-96.1484375"/><g transform="translate(-69.1484375, -12)" style="" class="label"><rect/><foreignObject height="24" width="138.296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Event Bus (Laravel)</p></span></div></foreignObject></g></g><g transform="translate(695.796875, 1501.5338287353516)" id="flowchart-L-37" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Listeners / Subscribers (Queued)</p></span></div></foreignObject></g></g><g transform="translate(346.38671875, 1617.5338287353516)" id="flowchart-M-39" class="node default"><rect height="54" width="151.21875" y="-27" x="-75.609375" style="" class="basic label-container"/><g transform="translate(-45.609375, -12)" style="" class="label"><rect/><foreignObject height="24" width="91.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Notifications</p></span></div></foreignObject></g></g><g transform="translate(558.35546875, 1617.5338287353516)" id="flowchart-N-41" class="node default"><rect height="54" width="172.71875" y="-27" x="-86.359375" style="" class="basic label-container"/><g transform="translate(-56.359375, -12)" style="" class="label"><rect/><foreignObject height="24" width="112.71875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Search Indexing</p></span></div></foreignObject></g></g><g transform="translate(777.88671875, 1617.5338287353516)" id="flowchart-O-43" class="node default"><rect height="54" width="166.34375" y="-27" x="-83.171875" style="" class="basic label-container"/><g transform="translate(-53.171875, -12)" style="" class="label"><rect/><foreignObject height="24" width="106.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Cache Updates</p></span></div></foreignObject></g></g><g transform="translate(949.87109375, 1617.5338287353516)" id="flowchart-P-45" class="node default"><rect height="54" width="77.625" y="-27" x="-38.8125" style="" class="basic label-container"/><g transform="translate(-8.8125, -12)" style="" class="label"><rect/><foreignObject height="24" width="17.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>...</p></span></div></foreignObject></g></g></g></g></g></svg>