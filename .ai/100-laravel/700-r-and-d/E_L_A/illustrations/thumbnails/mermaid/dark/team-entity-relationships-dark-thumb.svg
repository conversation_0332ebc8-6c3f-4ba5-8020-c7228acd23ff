<svg aria-roledescription="er" role="graphics-document document" viewBox="-80 0 1586.3968505859375 1788.25" style="max-width: 1586.3968505859375px;" class="erDiagram" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#a44141;}#my-svg .error-text{fill:#ddd;stroke:#ddd;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#ecf0f1;stroke:#ecf0f1;}#my-svg .marker.cross{stroke:#ecf0f1;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg .entityBox{fill:#1f2020;stroke:#ccc;}#my-svg .relationshipLabelBox{fill:#282c34;opacity:0.7;background-color:#282c34;}#my-svg .relationshipLabelBox rect{opacity:0.5;}#my-svg .labelBkg{background-color:rgba(40, 44, 52, 0.5);}#my-svg .edgeLabel .label{fill:#ccc;font-size:14px;}#my-svg .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#my-svg .edge-pattern-dashed{stroke-dasharray:8,8;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#my-svg .relationshipLine{stroke:#ecf0f1;stroke-width:1;fill:none;}#my-svg .marker{fill:none!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><defs><marker orient="auto" markerHeight="18" markerWidth="18" refY="9" refX="0" class="marker onlyOne er" id="my-svg_er-onlyOneStart"><path d="M9,0 L9,18 M15,0 L15,18"/></marker></defs><defs><marker orient="auto" markerHeight="18" markerWidth="18" refY="9" refX="18" class="marker onlyOne er" id="my-svg_er-onlyOneEnd"><path d="M3,0 L3,18 M9,0 L9,18"/></marker></defs><defs><marker orient="auto" markerHeight="18" markerWidth="30" refY="9" refX="0" class="marker zeroOrOne er" id="my-svg_er-zeroOrOneStart"><circle r="6" cy="9" cx="21" fill="white"/><path d="M9,0 L9,18"/></marker></defs><defs><marker orient="auto" markerHeight="18" markerWidth="30" refY="9" refX="30" class="marker zeroOrOne er" id="my-svg_er-zeroOrOneEnd"><circle r="6" cy="9" cx="9" fill="white"/><path d="M21,0 L21,18"/></marker></defs><defs><marker orient="auto" markerHeight="36" markerWidth="45" refY="18" refX="18" class="marker oneOrMore er" id="my-svg_er-oneOrMoreStart"><path d="M0,18 Q 18,0 36,18 Q 18,36 0,18 M42,9 L42,27"/></marker></defs><defs><marker orient="auto" markerHeight="36" markerWidth="45" refY="18" refX="27" class="marker oneOrMore er" id="my-svg_er-oneOrMoreEnd"><path d="M3,9 L3,27 M9,18 Q27,0 45,18 Q27,36 9,18"/></marker></defs><defs><marker orient="auto" markerHeight="36" markerWidth="57" refY="18" refX="18" class="marker zeroOrMore er" id="my-svg_er-zeroOrMoreStart"><circle r="6" cy="18" cx="48" fill="white"/><path d="M0,18 Q18,0 36,18 Q18,36 0,18"/></marker></defs><defs><marker orient="auto" markerHeight="36" markerWidth="57" refY="18" refX="39" class="marker zeroOrMore er" id="my-svg_er-zeroOrMoreEnd"><circle r="6" cy="18" cx="9" fill="white"/><path d="M21,18 Q39,0 57,18 Q39,36 21,18"/></marker></defs><g class="root"><g class="clusters"/><g class="edgePaths"><path marker-start="url(#my-svg_er-onlyOneStart)" style="undefined;" class="edge-thickness-normal edge-pattern-solid relationshipLine" id="entity-TEAM-0-cyclic-special-1" d="M928.738,823.662L958.066,855.468C987.394,887.275,1046.05,950.887,1075.378,1033.852C1104.706,1116.817,1104.706,1219.133,1104.706,1270.292L1104.706,1321.45"/><path style="undefined;" class="edge-thickness-normal edge-pattern-solid relationshipLine" id="entity-TEAM-0-cyclic-special-mid" d="M1104.706,1321.55L1104.706,1372.708C1104.706,1423.867,1104.706,1526.183,1006.656,1585.766C908.605,1645.349,712.504,1662.197,614.454,1670.621L516.403,1679.046"/><path marker-end="url(#my-svg_er-zeroOrMoreEnd)" style="undefined;" class="edge-thickness-normal edge-pattern-solid relationshipLine" id="entity-TEAM-0-cyclic-special-2" d="M516.303,1679.046L418.253,1670.621C320.202,1662.197,124.101,1645.349,26.051,1585.758C-72,1526.167,-72,1423.833,-72,1321.5C-72,1219.167,-72,1116.833,52.516,1018.88C177.033,920.926,426.065,827.352,550.581,780.565L675.098,733.778"/><path marker-end="url(#my-svg_er-oneOrMoreEnd)" marker-start="url(#my-svg_er-onlyOneStart)" style="undefined;" class="edge-thickness-normal edge-pattern-solid relationshipLine" id="id_entity-TEAM-0_entity-CATEGORY-1_1" d="M928.738,759.23L1002.545,801.775C1076.351,844.32,1223.964,929.41,1297.77,980.372C1371.577,1031.333,1371.577,1048.167,1371.577,1056.583L1371.577,1065"/><path marker-end="url(#my-svg_er-zeroOrMoreEnd)" marker-start="url(#my-svg_er-onlyOneStart)" style="undefined;" class="edge-thickness-normal edge-pattern-solid relationshipLine" id="id_entity-TEAM-0_entity-TODO-2_2" d="M675.098,748.356L584.702,792.714C494.307,837.071,313.517,925.785,223.122,992.809C132.727,1059.833,132.727,1105.167,132.727,1127.833L132.727,1150.5"/><path marker-end="url(#my-svg_er-zeroOrMoreEnd)" marker-start="url(#my-svg_er-zeroOrMoreStart)" style="undefined;" class="edge-thickness-normal edge-pattern-solid relationshipLine" id="id_entity-TEAM-0_entity-USER-3_3" d="M675.098,921.67L666.768,937.142C658.438,952.614,641.777,983.557,616.485,1035.945C591.194,1088.333,557.27,1162.167,540.308,1199.083L523.346,1236"/><path marker-end="url(#my-svg_er-zeroOrMoreEnd)" marker-start="url(#my-svg_er-onlyOneStart)" style="undefined;" class="edge-thickness-normal edge-pattern-solid relationshipLine" id="id_entity-TEAM-0_entity-STATUS-4_4" d="M832.167,964L833.083,972.417C833.999,980.833,835.832,997.667,836.748,1028.75C837.664,1059.833,837.664,1105.167,837.664,1127.833L837.664,1150.5"/><path marker-end="url(#my-svg_er-onlyOneEnd)" marker-start="url(#my-svg_er-oneOrMoreStart)" style="undefined;" class="edge-thickness-normal edge-pattern-solid relationshipLine" id="id_entity-TEAM_USER-5_entity-USER-3_5" d="M535.066,234.67L501.573,255.183C468.079,275.697,401.092,316.723,367.599,391.966C334.105,467.208,334.105,576.667,334.105,686.125C334.105,795.583,334.105,905.042,352.138,996.688C370.17,1088.333,406.235,1162.167,424.267,1199.083L442.299,1236"/><path marker-end="url(#my-svg_er-onlyOneEnd)" marker-start="url(#my-svg_er-oneOrMoreStart)" style="undefined;" class="edge-thickness-normal edge-pattern-solid relationshipLine" id="id_entity-TEAM_USER-5_entity-TEAM-0_6" d="M766.324,307.25L772.256,315.667C778.189,324.083,790.053,340.917,795.986,357.75C801.918,374.583,801.918,391.417,801.918,399.833L801.918,408.25"/><path marker-start="url(#my-svg_er-onlyOneStart)" style="undefined;" class="edge-thickness-normal edge-pattern-solid relationshipLine" id="entity-CATEGORY-1-cyclic-special-1" d="M1405.018,1578L1406.115,1586.417C1407.212,1594.833,1409.407,1611.667,1410.504,1628.5C1411.602,1645.333,1411.602,1662.167,1411.602,1670.583L1411.602,1679"/><path style="undefined;" class="edge-thickness-normal edge-pattern-solid relationshipLine" id="entity-CATEGORY-1-cyclic-special-mid" d="M1411.602,1679.1L1411.602,1687.517C1411.602,1695.933,1411.602,1712.767,1404.937,1729.6C1398.273,1746.433,1384.945,1763.267,1378.28,1771.683L1371.616,1780.1"/><path marker-end="url(#my-svg_er-zeroOrMoreEnd)" style="undefined;" class="edge-thickness-normal edge-pattern-solid relationshipLine" id="entity-CATEGORY-1-cyclic-special-2" d="M1371.527,1780.123L1355.967,1771.702C1340.407,1763.282,1309.286,1746.441,1293.726,1729.595C1278.166,1712.75,1278.166,1695.9,1278.166,1679.05C1278.166,1662.2,1278.166,1645.35,1280.727,1628.508C1283.288,1611.667,1288.41,1594.833,1290.971,1586.417L1293.532,1578"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1104.706250000745, 1628.5)" class="edgeLabel"><g transform="translate(-29.171875, -10.5)" class="label"><foreignObject height="21" width="58.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>parent of</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1371.5765625014901, 1014.5)" class="edgeLabel"><g transform="translate(-10.3359375, -10.5)" class="label"><foreignObject height="21" width="20.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>has</p></span></div></foreignObject></g></g><g transform="translate(132.7265625, 1014.5)" class="edgeLabel"><g transform="translate(-31.421875, -10.5)" class="label"><foreignObject height="21" width="62.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>related to</p></span></div></foreignObject></g></g><g transform="translate(625.1171875, 1014.5)" class="edgeLabel"><g transform="translate(-41.15625, -10.5)" class="label"><foreignObject height="21" width="82.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>has members</p></span></div></foreignObject></g></g><g transform="translate(837.6640625, 1014.5)" class="edgeLabel"><g transform="translate(-10.3359375, -10.5)" class="label"><foreignObject height="21" width="20.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>has</p></span></div></foreignObject></g></g><g transform="translate(334.10546875, 686.125)" class="edgeLabel"><g transform="translate(-32.3515625, -10.5)" class="label"><foreignObject height="21" width="64.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>belongs to</p></span></div></foreignObject></g></g><g transform="translate(801.91796875, 357.75)" class="edgeLabel"><g transform="translate(-32.3515625, -10.5)" class="label"><foreignObject height="21" width="64.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>belongs to</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1411.6015625018626, 1729.6000000014901)" class="edgeLabel"><g transform="translate(-29.171875, -10.5)" class="label"><foreignObject height="21" width="58.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>parent of</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(801.91796875, 686.125)" id="entity-TEAM-0" class="node default"><g style=""><path fill="#1f2020" stroke-width="0" stroke="none" d="M-126.8203125 -277.875 L126.8203125 -277.875 L126.8203125 277.875 L-126.8203125 277.875"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 -277.875 C-70.14571398857791 -277.875, -13.471115477155834 -277.875, 126.8203125 -277.875 M-126.8203125 -277.875 C-35.06519424294217 -277.875, 56.68992401411566 -277.875, 126.8203125 -277.875 M126.8203125 -277.875 C126.8203125 -150.5643044134224, 126.8203125 -23.253608826844783, 126.8203125 277.875 M126.8203125 -277.875 C126.8203125 -118.52826672507337, 126.8203125 40.81846654985327, 126.8203125 277.875 M126.8203125 277.875 C31.397218671806584 277.875, -64.02587515638683 277.875, -126.8203125 277.875 M126.8203125 277.875 C57.58412343424666 277.875, -11.652065631506673 277.875, -126.8203125 277.875 M-126.8203125 277.875 C-126.8203125 154.75024062057042, -126.8203125 31.625481241140818, -126.8203125 -277.875 M-126.8203125 277.875 C-126.8203125 110.55120476929503, -126.8203125 -56.77259046140995, -126.8203125 -277.875"/></g><g class="row-rect-odd" style=""><path fill="hsl(180, 1.5873015873%, 17.3529411765%)" stroke-width="0" stroke="none" d="M-126.8203125 -149.625 L126.8203125 -149.625 L126.8203125 -106.875 L-126.8203125 -106.875"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 -149.625 C-51.54327324556563 -149.625, 23.73376600886874 -149.625, 126.8203125 -149.625 M-126.8203125 -149.625 C-58.9812023137755 -149.625, 8.857907872449005 -149.625, 126.8203125 -149.625 M126.8203125 -149.625 C126.8203125 -135.87605818435497, 126.8203125 -122.12711636870995, 126.8203125 -106.875 M126.8203125 -149.625 C126.8203125 -140.58657359533433, 126.8203125 -131.54814719066866, 126.8203125 -106.875 M126.8203125 -106.875 C45.748344371012664 -106.875, -35.32362375797467 -106.875, -126.8203125 -106.875 M126.8203125 -106.875 C43.097977996864614 -106.875, -40.62435650627077 -106.875, -126.8203125 -106.875 M-126.8203125 -106.875 C-126.8203125 -120.78662713564488, -126.8203125 -134.69825427128976, -126.8203125 -149.625 M-126.8203125 -106.875 C-126.8203125 -115.67105324256951, -126.8203125 -124.46710648513901, -126.8203125 -149.625"/></g><g class="row-rect-even" style=""><path fill="hsl(180, 1.5873015873%, 2.3529411765%)" stroke-width="0" stroke="none" d="M-126.8203125 -106.875 L126.8203125 -106.875 L126.8203125 -64.125 L-126.8203125 -64.125"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 -106.875 C-62.6637237410479 -106.875, 1.4928650179041938 -106.875, 126.8203125 -106.875 M-126.8203125 -106.875 C-69.18811855356799 -106.875, -11.555924607135978 -106.875, 126.8203125 -106.875 M126.8203125 -106.875 C126.8203125 -92.50328002052518, 126.8203125 -78.13156004105038, 126.8203125 -64.125 M126.8203125 -106.875 C126.8203125 -95.38743494821773, 126.8203125 -83.89986989643548, 126.8203125 -64.125 M126.8203125 -64.125 C40.20970902054802 -64.125, -46.40089445890396 -64.125, -126.8203125 -64.125 M126.8203125 -64.125 C66.83859212911943 -64.125, 6.856871758238881 -64.125, -126.8203125 -64.125 M-126.8203125 -64.125 C-126.8203125 -76.09367362001096, -126.8203125 -88.06234724002191, -126.8203125 -106.875 M-126.8203125 -64.125 C-126.8203125 -77.22830817056156, -126.8203125 -90.33161634112314, -126.8203125 -106.875"/></g><g class="row-rect-odd" style=""><path fill="hsl(180, 1.5873015873%, 17.3529411765%)" stroke-width="0" stroke="none" d="M-126.8203125 -64.125 L126.8203125 -64.125 L126.8203125 -21.375 L-126.8203125 -21.375"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 -64.125 C-66.90979362890407 -64.125, -6.999274757808138 -64.125, 126.8203125 -64.125 M-126.8203125 -64.125 C-41.33893252251096 -64.125, 44.14244745497808 -64.125, 126.8203125 -64.125 M126.8203125 -64.125 C126.8203125 -48.02251476962381, 126.8203125 -31.920029539247615, 126.8203125 -21.375 M126.8203125 -64.125 C126.8203125 -49.67399719916101, 126.8203125 -35.222994398322015, 126.8203125 -21.375 M126.8203125 -21.375 C47.9719588763719 -21.375, -30.8763947472562 -21.375, -126.8203125 -21.375 M126.8203125 -21.375 C31.488976239458523 -21.375, -63.84236002108295 -21.375, -126.8203125 -21.375 M-126.8203125 -21.375 C-126.8203125 -36.36190925625868, -126.8203125 -51.348818512517354, -126.8203125 -64.125 M-126.8203125 -21.375 C-126.8203125 -33.83849514053581, -126.8203125 -46.30199028107163, -126.8203125 -64.125"/></g><g class="row-rect-even" style=""><path fill="hsl(180, 1.5873015873%, 2.3529411765%)" stroke-width="0" stroke="none" d="M-126.8203125 -21.375 L126.8203125 -21.375 L126.8203125 21.375 L-126.8203125 21.375"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 -21.375 C-62.82347919733239 -21.375, 1.1733541053352212 -21.375, 126.8203125 -21.375 M-126.8203125 -21.375 C-66.2994046268821 -21.375, -5.778496753764188 -21.375, 126.8203125 -21.375 M126.8203125 -21.375 C126.8203125 -10.47212059722091, 126.8203125 0.430758805558181, 126.8203125 21.375 M126.8203125 -21.375 C126.8203125 -10.325469089822194, 126.8203125 0.7240618203556117, 126.8203125 21.375 M126.8203125 21.375 C66.94389115204777 21.375, 7.067469804095538 21.375, -126.8203125 21.375 M126.8203125 21.375 C62.0101595456604 21.375, -2.7999934086791995 21.375, -126.8203125 21.375 M-126.8203125 21.375 C-126.8203125 8.190681164043053, -126.8203125 -4.993637671913895, -126.8203125 -21.375 M-126.8203125 21.375 C-126.8203125 10.866859879054926, -126.8203125 0.3587197581098529, -126.8203125 -21.375"/></g><g class="row-rect-odd" style=""><path fill="hsl(180, 1.5873015873%, 17.3529411765%)" stroke-width="0" stroke="none" d="M-126.8203125 21.375 L126.8203125 21.375 L126.8203125 64.125 L-126.8203125 64.125"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 21.375 C-75.39542793707582 21.375, -23.970543374151646 21.375, 126.8203125 21.375 M-126.8203125 21.375 C-74.51149130128722 21.375, -22.202670102574444 21.375, 126.8203125 21.375 M126.8203125 21.375 C126.8203125 35.94367194259795, 126.8203125 50.512343885195904, 126.8203125 64.125 M126.8203125 21.375 C126.8203125 30.146959621010996, 126.8203125 38.91891924202199, 126.8203125 64.125 M126.8203125 64.125 C32.05681290453697 64.125, -62.70668669092606 64.125, -126.8203125 64.125 M126.8203125 64.125 C64.4779292448776 64.125, 2.135545989755201 64.125, -126.8203125 64.125 M-126.8203125 64.125 C-126.8203125 47.70156948417436, -126.8203125 31.278138968348706, -126.8203125 21.375 M-126.8203125 64.125 C-126.8203125 53.05382308514309, -126.8203125 41.98264617028617, -126.8203125 21.375"/></g><g class="row-rect-even" style=""><path fill="hsl(180, 1.5873015873%, 2.3529411765%)" stroke-width="0" stroke="none" d="M-126.8203125 64.125 L126.8203125 64.125 L126.8203125 106.875 L-126.8203125 106.875"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 64.125 C-53.04884328338598 64.125, 20.722625933228045 64.125, 126.8203125 64.125 M-126.8203125 64.125 C-43.48114962214217 64.125, 39.85801325571566 64.125, 126.8203125 64.125 M126.8203125 64.125 C126.8203125 79.7272203040922, 126.8203125 95.3294406081844, 126.8203125 106.875 M126.8203125 64.125 C126.8203125 73.6357267375873, 126.8203125 83.14645347517462, 126.8203125 106.875 M126.8203125 106.875 C74.2634766426427 106.875, 21.7066407852854 106.875, -126.8203125 106.875 M126.8203125 106.875 C73.94521956276688 106.875, 21.07012662553376 106.875, -126.8203125 106.875 M-126.8203125 106.875 C-126.8203125 91.13885526377874, -126.8203125 75.40271052755749, -126.8203125 64.125 M-126.8203125 106.875 C-126.8203125 92.90559943685105, -126.8203125 78.9361988737021, -126.8203125 64.125"/></g><g class="row-rect-odd" style=""><path fill="hsl(180, 1.5873015873%, 17.3529411765%)" stroke-width="0" stroke="none" d="M-126.8203125 106.875 L126.8203125 106.875 L126.8203125 149.625 L-126.8203125 149.625"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 106.875 C-36.35643814767819 106.875, 54.10743620464362 106.875, 126.8203125 106.875 M-126.8203125 106.875 C-27.51735536947841 106.875, 71.78560176104318 106.875, 126.8203125 106.875 M126.8203125 106.875 C126.8203125 115.87999742978096, 126.8203125 124.88499485956193, 126.8203125 149.625 M126.8203125 106.875 C126.8203125 123.90136025213249, 126.8203125 140.92772050426498, 126.8203125 149.625 M126.8203125 149.625 C55.3376842664685 149.625, -16.144943967063 149.625, -126.8203125 149.625 M126.8203125 149.625 C40.02835361082596 149.625, -46.76360527834808 149.625, -126.8203125 149.625 M-126.8203125 149.625 C-126.8203125 138.04066074429767, -126.8203125 126.45632148859535, -126.8203125 106.875 M-126.8203125 149.625 C-126.8203125 140.5094054843011, -126.8203125 131.3938109686022, -126.8203125 106.875"/></g><g class="row-rect-even" style=""><path fill="hsl(180, 1.5873015873%, 2.3529411765%)" stroke-width="0" stroke="none" d="M-126.8203125 149.625 L126.8203125 149.625 L126.8203125 192.375 L-126.8203125 192.375"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 149.625 C-44.232983415372686 149.625, 38.35434566925463 149.625, 126.8203125 149.625 M-126.8203125 149.625 C-32.476522915303974 149.625, 61.86726666939205 149.625, 126.8203125 149.625 M126.8203125 149.625 C126.8203125 163.39625185171298, 126.8203125 177.16750370342592, 126.8203125 192.375 M126.8203125 149.625 C126.8203125 164.95620000530553, 126.8203125 180.28740001061107, 126.8203125 192.375 M126.8203125 192.375 C57.707410018410926 192.375, -11.405492463178149 192.375, -126.8203125 192.375 M126.8203125 192.375 C48.988214566128974 192.375, -28.843883367742052 192.375, -126.8203125 192.375 M-126.8203125 192.375 C-126.8203125 175.32568193547604, -126.8203125 158.27636387095208, -126.8203125 149.625 M-126.8203125 192.375 C-126.8203125 183.32562078866275, -126.8203125 174.2762415773255, -126.8203125 149.625"/></g><g class="row-rect-odd" style=""><path fill="hsl(180, 1.5873015873%, 17.3529411765%)" stroke-width="0" stroke="none" d="M-126.8203125 192.375 L126.8203125 192.375 L126.8203125 235.125 L-126.8203125 235.125"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 192.375 C-43.08020486663956 192.375, 40.659902766720876 192.375, 126.8203125 192.375 M-126.8203125 192.375 C-60.729669180097375 192.375, 5.360974139805251 192.375, 126.8203125 192.375 M126.8203125 192.375 C126.8203125 204.09713240709738, 126.8203125 215.81926481419475, 126.8203125 235.125 M126.8203125 192.375 C126.8203125 203.67155001297508, 126.8203125 214.96810002595015, 126.8203125 235.125 M126.8203125 235.125 C57.53658776012885 235.125, -11.747136979742294 235.125, -126.8203125 235.125 M126.8203125 235.125 C47.6969963227522 235.125, -31.426319854495603 235.125, -126.8203125 235.125 M-126.8203125 235.125 C-126.8203125 225.59175072500963, -126.8203125 216.05850145001926, -126.8203125 192.375 M-126.8203125 235.125 C-126.8203125 222.3810695322175, -126.8203125 209.63713906443505, -126.8203125 192.375"/></g><g class="row-rect-even" style=""><path fill="hsl(180, 1.5873015873%, 2.3529411765%)" stroke-width="0" stroke="none" d="M-126.8203125 235.125 L126.8203125 235.125 L126.8203125 277.875 L-126.8203125 277.875"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 235.125 C-55.33764757812624 235.125, 16.145017343747526 235.125, 126.8203125 235.125 M-126.8203125 235.125 C-56.072738800628514 235.125, 14.674834898742972 235.125, 126.8203125 235.125 M126.8203125 235.125 C126.8203125 252.15926679376093, 126.8203125 269.19353358752187, 126.8203125 277.875 M126.8203125 235.125 C126.8203125 244.68947998117412, 126.8203125 254.25395996234826, 126.8203125 277.875 M126.8203125 277.875 C46.9663224537932 277.875, -32.88766759241361 277.875, -126.8203125 277.875 M126.8203125 277.875 C65.27864468185587 277.875, 3.7369768637117318 277.875, -126.8203125 277.875 M-126.8203125 277.875 C-126.8203125 264.799646857718, -126.8203125 251.72429371543598, -126.8203125 235.125 M-126.8203125 277.875 C-126.8203125 267.92022080917565, -126.8203125 257.96544161835135, -126.8203125 235.125"/></g><g class="row-rect-odd" style=""><path fill="hsl(180, 1.5873015873%, 17.3529411765%)" stroke-width="0" stroke="none" d="M-126.8203125 -235.125 L126.8203125 -235.125 L126.8203125 -192.375 L-126.8203125 -192.375"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 -235.125 C-46.8829471849979 -235.125, 33.054418130004194 -235.125, 126.8203125 -235.125 M-126.8203125 -235.125 C-50.95772236724274 -235.125, 24.904867765514524 -235.125, 126.8203125 -235.125 M126.8203125 -235.125 C126.8203125 -219.72849125466433, 126.8203125 -204.3319825093287, 126.8203125 -192.375 M126.8203125 -235.125 C126.8203125 -221.6805537617881, 126.8203125 -208.2361075235762, 126.8203125 -192.375 M126.8203125 -192.375 C49.42732028873263 -192.375, -27.96567192253474 -192.375, -126.8203125 -192.375 M126.8203125 -192.375 C63.79898444457604 -192.375, 0.7776563891520851 -192.375, -126.8203125 -192.375 M-126.8203125 -192.375 C-126.8203125 -208.42026288871202, -126.8203125 -224.46552577742403, -126.8203125 -235.125 M-126.8203125 -192.375 C-126.8203125 -207.17268637050788, -126.8203125 -221.97037274101578, -126.8203125 -235.125"/></g><g style="" transform="translate(-19.328125, -268.5)" class="label name"><foreignObject height="24" width="38.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 145px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>TEAM</p></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, -225.75)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, -225.75)" class="label attribute-name"><foreignObject height="24" width="13.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 112px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>id</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, -225.75)" class="label attribute-keys"><foreignObject height="24" width="18.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PK</p></span></div></foreignObject></g><g style="" transform="translate(139.3203125, -225.75)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, -183)" class="label attribute-type"><foreignObject height="24" width="40.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, -183)" class="label attribute-name"><foreignObject height="24" width="39.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 135px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>name</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, -183)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.3203125, -183)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, -140.25)" class="label attribute-type"><foreignObject height="24" width="40.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, -140.25)" class="label attribute-name"><foreignObject height="24" width="27.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 127px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>slug</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, -140.25)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.3203125, -140.25)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, -97.5)" class="label attribute-type"><foreignObject height="24" width="29.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 124px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>text</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, -97.5)" class="label attribute-name"><foreignObject height="24" width="79.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 171px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>description</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, -97.5)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.3203125, -97.5)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, -54.75)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, -54.75)" class="label attribute-name"><foreignObject height="24" width="69.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 161px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>parent_id</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, -54.75)" class="label attribute-keys"><foreignObject height="24" width="17.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g style="" transform="translate(139.3203125, -54.75)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, -12)" class="label attribute-type"><foreignObject height="24" width="40.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, -12)" class="label attribute-name"><foreignObject height="24" width="42.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>status</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, -12)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.3203125, -12)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, 30.75)" class="label attribute-type"><foreignObject height="24" width="76.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 167px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, 30.75)" class="label attribute-name"><foreignObject height="24" width="78.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 166px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>created_at</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, 30.75)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.3203125, 30.75)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, 73.5)" class="label attribute-type"><foreignObject height="24" width="76.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 167px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, 73.5)" class="label attribute-name"><foreignObject height="24" width="82.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 170px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>updated_at</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, 73.5)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.3203125, 73.5)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, 116.25)" class="label attribute-type"><foreignObject height="24" width="76.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 167px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, 116.25)" class="label attribute-name"><foreignObject height="24" width="78.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 166px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>deleted_at</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, 116.25)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.3203125, 116.25)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, 159)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, 159)" class="label attribute-name"><foreignObject height="24" width="80.453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 170px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>created_by</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, 159)" class="label attribute-keys"><foreignObject height="24" width="17.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g style="" transform="translate(139.3203125, 159)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, 201.75)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, 201.75)" class="label attribute-name"><foreignObject height="24" width="84.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 175px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>updated_by</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, 201.75)" class="label attribute-keys"><foreignObject height="24" width="17.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g style="" transform="translate(139.3203125, 201.75)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, 244.5)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, 244.5)" class="label attribute-name"><foreignObject height="24" width="80.265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 170px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>deleted_by</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, 244.5)" class="label attribute-keys"><foreignObject height="24" width="17.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g style="" transform="translate(139.3203125, 244.5)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 -235.125 C-32.473284552262186 -235.125, 61.87374339547563 -235.125, 126.8203125 -235.125 M-126.8203125 -235.125 C-58.05051266985147 -235.125, 10.719287160297057 -235.125, 126.8203125 -235.125"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-25.4765625 -235.125 C-25.4765625 -87.57498817589689, -25.4765625 59.97502364820622, -25.4765625 277.875 M-25.4765625 -235.125 C-25.4765625 -45.83882452728733, -25.4765625 143.44735094542534, -25.4765625 277.875"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M83.6796875 -235.125 C83.6796875 -108.1721454799778, 83.6796875 18.7807090400444, 83.6796875 277.875 M83.6796875 -235.125 C83.6796875 -83.98919446755161, 83.6796875 67.14661106489677, 83.6796875 277.875"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 -192.375 C-57.26598023071473 -192.375, 12.288352038570537 -192.375, 126.8203125 -192.375 M-126.8203125 -192.375 C-69.9003081341929 -192.375, -12.980303768385795 -192.375, 126.8203125 -192.375"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 -149.625 C-44.29110826454057 -149.625, 38.23809597091886 -149.625, 126.8203125 -149.625 M-126.8203125 -149.625 C-34.32529884131121 -149.625, 58.16971481737758 -149.625, 126.8203125 -149.625"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 -106.875 C-51.41020688057087 -106.875, 23.999898738858263 -106.875, 126.8203125 -106.875 M-126.8203125 -106.875 C-33.2545145974629 -106.875, 60.3112833050742 -106.875, 126.8203125 -106.875"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 -64.125 C-68.94912003942474 -64.125, -11.077927578849483 -64.125, 126.8203125 -64.125 M-126.8203125 -64.125 C-64.94191324099344 -64.125, -3.063513981986901 -64.125, 126.8203125 -64.125"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 -21.375 C-57.137799985119756 -21.375, 12.544712529760488 -21.375, 126.8203125 -21.375 M-126.8203125 -21.375 C-61.11376739086309 -21.375, 4.5927777182738225 -21.375, 126.8203125 -21.375"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 21.375 C-75.37639695539048 21.375, -23.932481410780966 21.375, 126.8203125 21.375 M-126.8203125 21.375 C-29.13403550359874 21.375, 68.55224149280252 21.375, 126.8203125 21.375"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 64.125 C-46.770910330183185 64.125, 33.27849183963363 64.125, 126.8203125 64.125 M-126.8203125 64.125 C-72.63029321886361 64.125, -18.44027393772724 64.125, 126.8203125 64.125"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 106.875 C-69.91724575355254 106.875, -13.01417900710507 106.875, 126.8203125 106.875 M-126.8203125 106.875 C-61.76023856989481 106.875, 3.2998353602103805 106.875, 126.8203125 106.875"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 149.625 C-72.23016809351802 149.625, -17.64002368703605 149.625, 126.8203125 149.625 M-126.8203125 149.625 C-34.60281156857657 149.625, 57.61468936284686 149.625, 126.8203125 149.625"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 192.375 C-46.557386711392994 192.375, 33.70553907721401 192.375, 126.8203125 192.375 M-126.8203125 192.375 C-71.61817517781223 192.375, -16.416037855624467 192.375, 126.8203125 192.375"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 235.125 C-54.65603397759223 235.125, 17.50824454481554 235.125, 126.8203125 235.125 M-126.8203125 235.125 C-60.76199071147242 235.125, 5.296331077055157 235.125, 126.8203125 235.125"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 -235.125 C-72.29536901144306 -235.125, -17.7704255228861 -235.125, 126.8203125 -235.125 M-126.8203125 -235.125 C-73.95566639216366 -235.125, -21.091020284327342 -235.125, 126.8203125 -235.125"/></g></g><g transform="translate(1371.5765625014901, 1321.5)" id="entity-CATEGORY-1" class="node default"><g style=""><path fill="#1f2020" stroke-width="0" stroke="none" d="M-126.8203125 -256.5 L126.8203125 -256.5 L126.8203125 256.5 L-126.8203125 256.5"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 -256.5 C-37.74545022147619 -256.5, 51.32941205704762 -256.5, 126.8203125 -256.5 M-126.8203125 -256.5 C-53.53819695259722 -256.5, 19.743918594805564 -256.5, 126.8203125 -256.5 M126.8203125 -256.5 C126.8203125 -126.16900773662971, 126.8203125 4.161984526740582, 126.8203125 256.5 M126.8203125 -256.5 C126.8203125 -86.7378320571838, 126.8203125 83.02433588563241, 126.8203125 256.5 M126.8203125 256.5 C57.7662649600819 256.5, -11.287782579836204 256.5, -126.8203125 256.5 M126.8203125 256.5 C73.07533378165958 256.5, 19.33035506331916 256.5, -126.8203125 256.5 M-126.8203125 256.5 C-126.8203125 150.79026405240597, -126.8203125 45.080528104811975, -126.8203125 -256.5 M-126.8203125 256.5 C-126.8203125 76.8434454450101, -126.8203125 -102.81310910997979, -126.8203125 -256.5"/></g><g class="row-rect-odd" style=""><path fill="hsl(180, 1.5873015873%, 17.3529411765%)" stroke-width="0" stroke="none" d="M-126.8203125 -128.25 L126.8203125 -128.25 L126.8203125 -85.5 L-126.8203125 -85.5"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 -128.25 C-33.37965613096277 -128.25, 60.061000238074456 -128.25, 126.8203125 -128.25 M-126.8203125 -128.25 C-44.16147623330568 -128.25, 38.497360033388645 -128.25, 126.8203125 -128.25 M126.8203125 -128.25 C126.8203125 -114.5020262434282, 126.8203125 -100.75405248685641, 126.8203125 -85.5 M126.8203125 -128.25 C126.8203125 -116.16147528777752, 126.8203125 -104.07295057555504, 126.8203125 -85.5 M126.8203125 -85.5 C73.4566688323464 -85.5, 20.093025164692804 -85.5, -126.8203125 -85.5 M126.8203125 -85.5 C71.60633378095713 -85.5, 16.392355061914273 -85.5, -126.8203125 -85.5 M-126.8203125 -85.5 C-126.8203125 -95.81087773875056, -126.8203125 -106.12175547750113, -126.8203125 -128.25 M-126.8203125 -85.5 C-126.8203125 -101.02857150771851, -126.8203125 -116.55714301543702, -126.8203125 -128.25"/></g><g class="row-rect-even" style=""><path fill="hsl(180, 1.5873015873%, 2.3529411765%)" stroke-width="0" stroke="none" d="M-126.8203125 -85.5 L126.8203125 -85.5 L126.8203125 -42.75 L-126.8203125 -42.75"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 -85.5 C-68.51751384940692 -85.5, -10.214715198813849 -85.5, 126.8203125 -85.5 M-126.8203125 -85.5 C-73.53212249235654 -85.5, -20.243932484713085 -85.5, 126.8203125 -85.5 M126.8203125 -85.5 C126.8203125 -70.33575223448634, 126.8203125 -55.17150446897268, 126.8203125 -42.75 M126.8203125 -85.5 C126.8203125 -72.20896067069744, 126.8203125 -58.91792134139487, 126.8203125 -42.75 M126.8203125 -42.75 C47.22318488919785 -42.75, -32.373942721604294 -42.75, -126.8203125 -42.75 M126.8203125 -42.75 C49.404754083334666 -42.75, -28.010804333330668 -42.75, -126.8203125 -42.75 M-126.8203125 -42.75 C-126.8203125 -55.095091519786294, -126.8203125 -67.44018303957259, -126.8203125 -85.5 M-126.8203125 -42.75 C-126.8203125 -55.40871513803509, -126.8203125 -68.06743027607018, -126.8203125 -85.5"/></g><g class="row-rect-odd" style=""><path fill="hsl(180, 1.5873015873%, 17.3529411765%)" stroke-width="0" stroke="none" d="M-126.8203125 -42.75 L126.8203125 -42.75 L126.8203125 0 L-126.8203125 0"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 -42.75 C-70.38661585676667 -42.75, -13.952919213533349 -42.75, 126.8203125 -42.75 M-126.8203125 -42.75 C-38.51179588886862 -42.75, 49.79672072226276 -42.75, 126.8203125 -42.75 M126.8203125 -42.75 C126.8203125 -33.8018616997972, 126.8203125 -24.8537233995944, 126.8203125 0 M126.8203125 -42.75 C126.8203125 -33.68367791833082, 126.8203125 -24.61735583666164, 126.8203125 0 M126.8203125 0 C40.04760067980298 0, -46.725111140394034 0, -126.8203125 0 M126.8203125 0 C46.36303935218305 0, -34.0942337956339 0, -126.8203125 0 M-126.8203125 0 C-126.8203125 -12.063409919323252, -126.8203125 -24.126819838646504, -126.8203125 -42.75 M-126.8203125 0 C-126.8203125 -9.514031057918839, -126.8203125 -19.028062115837677, -126.8203125 -42.75"/></g><g class="row-rect-even" style=""><path fill="hsl(180, 1.5873015873%, 2.3529411765%)" stroke-width="0" stroke="none" d="M-126.8203125 0 L126.8203125 0 L126.8203125 42.75 L-126.8203125 42.75"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 0 C-67.57015404500275 0, -8.319995590005504 0, 126.8203125 0 M-126.8203125 0 C-67.1852541493618 0, -7.550195798723593 0, 126.8203125 0 M126.8203125 0 C126.8203125 16.448241013801926, 126.8203125 32.89648202760385, 126.8203125 42.75 M126.8203125 0 C126.8203125 9.699911237646202, 126.8203125 19.399822475292403, 126.8203125 42.75 M126.8203125 42.75 C35.40051944339143 42.75, -56.01927361321714 42.75, -126.8203125 42.75 M126.8203125 42.75 C72.89570472218696 42.75, 18.97109694437391 42.75, -126.8203125 42.75 M-126.8203125 42.75 C-126.8203125 33.91493088161765, -126.8203125 25.079861763235304, -126.8203125 0 M-126.8203125 42.75 C-126.8203125 30.39439845872367, -126.8203125 18.03879691744734, -126.8203125 0"/></g><g class="row-rect-odd" style=""><path fill="hsl(180, 1.5873015873%, 17.3529411765%)" stroke-width="0" stroke="none" d="M-126.8203125 42.75 L126.8203125 42.75 L126.8203125 85.5 L-126.8203125 85.5"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 42.75 C-25.506332933623526 42.75, 75.80764663275295 42.75, 126.8203125 42.75 M-126.8203125 42.75 C-46.77728175962106 42.75, 33.26574898075788 42.75, 126.8203125 42.75 M126.8203125 42.75 C126.8203125 51.53265849838449, 126.8203125 60.31531699676899, 126.8203125 85.5 M126.8203125 42.75 C126.8203125 56.31710299006456, 126.8203125 69.88420598012912, 126.8203125 85.5 M126.8203125 85.5 C49.30390131077243 85.5, -28.212509878455137 85.5, -126.8203125 85.5 M126.8203125 85.5 C48.73959881276538 85.5, -29.34111487446924 85.5, -126.8203125 85.5 M-126.8203125 85.5 C-126.8203125 73.51596734309685, -126.8203125 61.53193468619369, -126.8203125 42.75 M-126.8203125 85.5 C-126.8203125 72.9461265928079, -126.8203125 60.392253185615814, -126.8203125 42.75"/></g><g class="row-rect-even" style=""><path fill="hsl(180, 1.5873015873%, 2.3529411765%)" stroke-width="0" stroke="none" d="M-126.8203125 85.5 L126.8203125 85.5 L126.8203125 128.25 L-126.8203125 128.25"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 85.5 C-41.62834924351513 85.5, 43.56361401296974 85.5, 126.8203125 85.5 M-126.8203125 85.5 C-45.81494670294427 85.5, 35.19041909411146 85.5, 126.8203125 85.5 M126.8203125 85.5 C126.8203125 96.66219499174636, 126.8203125 107.82438998349272, 126.8203125 128.25 M126.8203125 85.5 C126.8203125 97.24013159065507, 126.8203125 108.98026318131015, 126.8203125 128.25 M126.8203125 128.25 C67.53004517376812 128.25, 8.239777847536232 128.25, -126.8203125 128.25 M126.8203125 128.25 C73.03199722132825 128.25, 19.243681942656522 128.25, -126.8203125 128.25 M-126.8203125 128.25 C-126.8203125 113.39813953170848, -126.8203125 98.54627906341696, -126.8203125 85.5 M-126.8203125 128.25 C-126.8203125 118.89973848019025, -126.8203125 109.54947696038049, -126.8203125 85.5"/></g><g class="row-rect-odd" style=""><path fill="hsl(180, 1.5873015873%, 17.3529411765%)" stroke-width="0" stroke="none" d="M-126.8203125 128.25 L126.8203125 128.25 L126.8203125 171 L-126.8203125 171"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 128.25 C-49.78787574523794 128.25, 27.24456100952412 128.25, 126.8203125 128.25 M-126.8203125 128.25 C-54.887199910723155 128.25, 17.04591267855369 128.25, 126.8203125 128.25 M126.8203125 128.25 C126.8203125 140.62869258967447, 126.8203125 153.00738517934897, 126.8203125 171 M126.8203125 128.25 C126.8203125 144.1377462846708, 126.8203125 160.02549256934162, 126.8203125 171 M126.8203125 171 C64.05814451185087 171, 1.2959765237017393 171, -126.8203125 171 M126.8203125 171 C49.87077380952407 171, -27.078764880951866 171, -126.8203125 171 M-126.8203125 171 C-126.8203125 156.56126174297603, -126.8203125 142.12252348595206, -126.8203125 128.25 M-126.8203125 171 C-126.8203125 158.12018549844277, -126.8203125 145.24037099688553, -126.8203125 128.25"/></g><g class="row-rect-even" style=""><path fill="hsl(180, 1.5873015873%, 2.3529411765%)" stroke-width="0" stroke="none" d="M-126.8203125 171 L126.8203125 171 L126.8203125 213.75 L-126.8203125 213.75"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 171 C-59.0517786561835 171, 8.716755187632998 171, 126.8203125 171 M-126.8203125 171 C-70.02447970413469 171, -13.228646908269383 171, 126.8203125 171 M126.8203125 171 C126.8203125 185.59575077637155, 126.8203125 200.19150155274306, 126.8203125 213.75 M126.8203125 171 C126.8203125 185.27118701534047, 126.8203125 199.54237403068092, 126.8203125 213.75 M126.8203125 213.75 C49.67734420284944 213.75, -27.46562409430112 213.75, -126.8203125 213.75 M126.8203125 213.75 C68.76958276494393 213.75, 10.718853029887867 213.75, -126.8203125 213.75 M-126.8203125 213.75 C-126.8203125 199.88870542316585, -126.8203125 186.02741084633172, -126.8203125 171 M-126.8203125 213.75 C-126.8203125 197.89273435381597, -126.8203125 182.0354687076319, -126.8203125 171"/></g><g class="row-rect-odd" style=""><path fill="hsl(180, 1.5873015873%, 17.3529411765%)" stroke-width="0" stroke="none" d="M-126.8203125 213.75 L126.8203125 213.75 L126.8203125 256.5 L-126.8203125 256.5"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 213.75 C-30.482178575036016 213.75, 65.85595534992797 213.75, 126.8203125 213.75 M-126.8203125 213.75 C-52.974804826831374 213.75, 20.870702846337252 213.75, 126.8203125 213.75 M126.8203125 213.75 C126.8203125 222.7557554239638, 126.8203125 231.76151084792758, 126.8203125 256.5 M126.8203125 213.75 C126.8203125 228.1619307356868, 126.8203125 242.57386147137365, 126.8203125 256.5 M126.8203125 256.5 C33.18806189985838 256.5, -60.444188700283235 256.5, -126.8203125 256.5 M126.8203125 256.5 C52.528627745738234 256.5, -21.763057008523532 256.5, -126.8203125 256.5 M-126.8203125 256.5 C-126.8203125 241.72186437443165, -126.8203125 226.9437287488633, -126.8203125 213.75 M-126.8203125 256.5 C-126.8203125 247.20000348163336, -126.8203125 237.9000069632667, -126.8203125 213.75"/></g><g class="row-rect-even" style=""><path fill="hsl(180, 1.5873015873%, 17.3529411765%)" stroke-width="0" stroke="none" d="M-126.8203125 -213.75 L126.8203125 -213.75 L126.8203125 -171 L-126.8203125 -171"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 -213.75 C-28.686225305064525 -213.75, 69.44786188987095 -213.75, 126.8203125 -213.75 M-126.8203125 -213.75 C-59.49653831591563 -213.75, 7.827235868168742 -213.75, 126.8203125 -213.75 M126.8203125 -213.75 C126.8203125 -204.17298618219476, 126.8203125 -194.5959723643895, 126.8203125 -171 M126.8203125 -213.75 C126.8203125 -198.42938612143953, 126.8203125 -183.10877224287907, 126.8203125 -171 M126.8203125 -171 C64.14281057070708 -171, 1.4653086414141683 -171, -126.8203125 -171 M126.8203125 -171 C61.90462250453898 -171, -3.011067490922045 -171, -126.8203125 -171 M-126.8203125 -171 C-126.8203125 -186.8694236908287, -126.8203125 -202.7388473816574, -126.8203125 -213.75 M-126.8203125 -171 C-126.8203125 -182.85823796651678, -126.8203125 -194.71647593303356, -126.8203125 -213.75"/></g><g style="" transform="translate(-37.1640625, -247.125)" class="label name"><foreignObject height="24" width="74.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 184px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>CATEGORY</p></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, -204.375)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, -204.375)" class="label attribute-name"><foreignObject height="24" width="13.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 112px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>id</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, -204.375)" class="label attribute-keys"><foreignObject height="24" width="18.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PK</p></span></div></foreignObject></g><g style="" transform="translate(139.3203125, -204.375)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, -161.625)" class="label attribute-type"><foreignObject height="24" width="40.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, -161.625)" class="label attribute-name"><foreignObject height="24" width="39.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 135px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>name</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, -161.625)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.3203125, -161.625)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, -118.875)" class="label attribute-type"><foreignObject height="24" width="29.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 124px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>text</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, -118.875)" class="label attribute-name"><foreignObject height="24" width="79.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 171px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>description</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, -118.875)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.3203125, -118.875)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, -76.125)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, -76.125)" class="label attribute-name"><foreignObject height="24" width="58.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 152px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>team_id</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, -76.125)" class="label attribute-keys"><foreignObject height="24" width="17.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g style="" transform="translate(139.3203125, -76.125)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, -33.375)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, -33.375)" class="label attribute-name"><foreignObject height="24" width="69.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 161px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>parent_id</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, -33.375)" class="label attribute-keys"><foreignObject height="24" width="17.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g style="" transform="translate(139.3203125, -33.375)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, 9.375)" class="label attribute-type"><foreignObject height="24" width="76.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 167px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, 9.375)" class="label attribute-name"><foreignObject height="24" width="78.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 166px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>created_at</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, 9.375)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.3203125, 9.375)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, 52.125)" class="label attribute-type"><foreignObject height="24" width="76.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 167px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, 52.125)" class="label attribute-name"><foreignObject height="24" width="82.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 170px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>updated_at</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, 52.125)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.3203125, 52.125)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, 94.875)" class="label attribute-type"><foreignObject height="24" width="76.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 167px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, 94.875)" class="label attribute-name"><foreignObject height="24" width="78.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 166px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>deleted_at</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, 94.875)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.3203125, 94.875)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, 137.625)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, 137.625)" class="label attribute-name"><foreignObject height="24" width="80.453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 170px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>created_by</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, 137.625)" class="label attribute-keys"><foreignObject height="24" width="17.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g style="" transform="translate(139.3203125, 137.625)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, 180.375)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, 180.375)" class="label attribute-name"><foreignObject height="24" width="84.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 175px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>updated_by</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, 180.375)" class="label attribute-keys"><foreignObject height="24" width="17.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g style="" transform="translate(139.3203125, 180.375)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.3203125, 223.125)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-12.9765625, 223.125)" class="label attribute-name"><foreignObject height="24" width="80.265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 170px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>deleted_by</p></span></div></foreignObject></g><g style="" transform="translate(96.1796875, 223.125)" class="label attribute-keys"><foreignObject height="24" width="17.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g style="" transform="translate(139.3203125, 223.125)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 -213.75 C-54.67462006513772 -213.75, 17.471072369724567 -213.75, 126.8203125 -213.75 M-126.8203125 -213.75 C-49.97200393370518 -213.75, 26.876304632589637 -213.75, 126.8203125 -213.75"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-25.4765625 -213.75 C-25.4765625 -62.311249735290545, -25.4765625 89.12750052941891, -25.4765625 256.5 M-25.4765625 -213.75 C-25.4765625 -53.44859481314265, -25.4765625 106.8528103737147, -25.4765625 256.5"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M83.6796875 -213.75 C83.6796875 -91.78767418706369, 83.6796875 30.174651625872627, 83.6796875 256.5 M83.6796875 -213.75 C83.6796875 -55.30644837866555, 83.6796875 103.1371032426689, 83.6796875 256.5"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 -171 C-60.112156588117244 -171, 6.595999323765511 -171, 126.8203125 -171 M-126.8203125 -171 C-70.17325346998555 -171, -13.526194439971107 -171, 126.8203125 -171"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 -128.25 C-38.86568137769545 -128.25, 49.088949744609096 -128.25, 126.8203125 -128.25 M-126.8203125 -128.25 C-33.820513178238 -128.25, 59.179286143523996 -128.25, 126.8203125 -128.25"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 -85.5 C-57.61623466822644 -85.5, 11.58784316354712 -85.5, 126.8203125 -85.5 M-126.8203125 -85.5 C-65.53549422979125 -85.5, -4.250675959582495 -85.5, 126.8203125 -85.5"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 -42.75 C-72.18140011743998 -42.75, -17.542487734879955 -42.75, 126.8203125 -42.75 M-126.8203125 -42.75 C-39.21027622908562 -42.75, 48.399760041828756 -42.75, 126.8203125 -42.75"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 0 C-46.12613229816918 0, 34.568047903661636 0, 126.8203125 0 M-126.8203125 0 C-74.56794483894592 0, -22.31557717789184 0, 126.8203125 0"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 42.75 C-52.90038993612397 42.75, 21.019532627752056 42.75, 126.8203125 42.75 M-126.8203125 42.75 C-54.95444758383782 42.75, 16.911417332324362 42.75, 126.8203125 42.75"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 85.5 C-32.762017041668784 85.5, 61.29627841666243 85.5, 126.8203125 85.5 M-126.8203125 85.5 C-70.18101520646502 85.5, -13.541717912930054 85.5, 126.8203125 85.5"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 128.25 C-61.05959444880288 128.25, 4.701123602394233 128.25, 126.8203125 128.25 M-126.8203125 128.25 C-31.93478296639192 128.25, 62.95074656721616 128.25, 126.8203125 128.25"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 171 C-41.04014948266449 171, 44.74001353467102 171, 126.8203125 171 M-126.8203125 171 C-57.160384428923365 171, 12.49954364215327 171, 126.8203125 171"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 213.75 C-44.91734588432671 213.75, 36.985620731346586 213.75, 126.8203125 213.75 M-126.8203125 213.75 C-59.939460085140354 213.75, 6.941392329719292 213.75, 126.8203125 213.75"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.8203125 -213.75 C-35.55946980870979 -213.75, 55.701372882580415 -213.75, 126.8203125 -213.75 M-126.8203125 -213.75 C-25.617062814017416 -213.75, 75.58618687196517 -213.75, 126.8203125 -213.75"/></g></g><g transform="translate(132.7265625, 1321.5)" id="entity-TODO-2" class="node default"><g style=""><path fill="#1f2020" stroke-width="0" stroke="none" d="M-124.7265625 -171 L124.7265625 -171 L124.7265625 171 L-124.7265625 171"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-124.7265625 -171 C-34.54891178198051 -171, 55.628738936038985 -171, 124.7265625 -171 M-124.7265625 -171 C-72.6514490329069 -171, -20.576335565813793 -171, 124.7265625 -171 M124.7265625 -171 C124.7265625 -38.909281295146855, 124.7265625 93.18143740970629, 124.7265625 171 M124.7265625 -171 C124.7265625 -73.87638783234698, 124.7265625 23.247224335306043, 124.7265625 171 M124.7265625 171 C40.80846495700692 171, -43.10963258598616 171, -124.7265625 171 M124.7265625 171 C57.050718017833034 171, -10.625126464333931 171, -124.7265625 171 M-124.7265625 171 C-124.7265625 90.1347973657579, -124.7265625 9.26959473151581, -124.7265625 -171 M-124.7265625 171 C-124.7265625 94.95896141554792, -124.7265625 18.917922831095837, -124.7265625 -171"/></g><g class="row-rect-odd" style=""><path fill="hsl(180, 1.5873015873%, 17.3529411765%)" stroke-width="0" stroke="none" d="M-124.7265625 -42.75 L124.7265625 -42.75 L124.7265625 0 L-124.7265625 0"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-124.7265625 -42.75 C-26.956730094250688 -42.75, 70.81310231149862 -42.75, 124.7265625 -42.75 M-124.7265625 -42.75 C-63.86367310190123 -42.75, -3.0007837038024547 -42.75, 124.7265625 -42.75 M124.7265625 -42.75 C124.7265625 -28.61890868026738, 124.7265625 -14.487817360534763, 124.7265625 0 M124.7265625 -42.75 C124.7265625 -31.230959140275676, 124.7265625 -19.71191828055135, 124.7265625 0 M124.7265625 0 C31.67928939919331 0, -61.36798370161338 0, -124.7265625 0 M124.7265625 0 C69.92778019498331 0, 15.12899788996664 0, -124.7265625 0 M-124.7265625 0 C-124.7265625 -11.61314532626737, -124.7265625 -23.22629065253474, -124.7265625 -42.75 M-124.7265625 0 C-124.7265625 -10.67298205597442, -124.7265625 -21.34596411194884, -124.7265625 -42.75"/></g><g class="row-rect-even" style=""><path fill="hsl(180, 1.5873015873%, 2.3529411765%)" stroke-width="0" stroke="none" d="M-124.7265625 0 L124.7265625 0 L124.7265625 42.75 L-124.7265625 42.75"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-124.7265625 0 C-44.0576969356226 0, 36.6111686287548 0, 124.7265625 0 M-124.7265625 0 C-72.34579350555249 0, -19.965024511104986 0, 124.7265625 0 M124.7265625 0 C124.7265625 11.89987060788566, 124.7265625 23.79974121577132, 124.7265625 42.75 M124.7265625 0 C124.7265625 16.364395208922073, 124.7265625 32.728790417844145, 124.7265625 42.75 M124.7265625 42.75 C73.97936546500296 42.75, 23.2321684300059 42.75, -124.7265625 42.75 M124.7265625 42.75 C32.582876528093465 42.75, -59.56080944381307 42.75, -124.7265625 42.75 M-124.7265625 42.75 C-124.7265625 33.00122320431786, -124.7265625 23.252446408635716, -124.7265625 0 M-124.7265625 42.75 C-124.7265625 26.066095809490182, -124.7265625 9.382191618980364, -124.7265625 0"/></g><g class="row-rect-odd" style=""><path fill="hsl(180, 1.5873015873%, 17.3529411765%)" stroke-width="0" stroke="none" d="M-124.7265625 42.75 L124.7265625 42.75 L124.7265625 85.5 L-124.7265625 85.5"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-124.7265625 42.75 C-34.386628078365746 42.75, 55.95330634326851 42.75, 124.7265625 42.75 M-124.7265625 42.75 C-38.579115111177074 42.75, 47.56833227764585 42.75, 124.7265625 42.75 M124.7265625 42.75 C124.7265625 54.60972641209606, 124.7265625 66.46945282419212, 124.7265625 85.5 M124.7265625 42.75 C124.7265625 51.33955960377011, 124.7265625 59.929119207540225, 124.7265625 85.5 M124.7265625 85.5 C60.720525360752305 85.5, -3.2855117784953904 85.5, -124.7265625 85.5 M124.7265625 85.5 C73.05526436148874 85.5, 21.383966222977477 85.5, -124.7265625 85.5 M-124.7265625 85.5 C-124.7265625 70.108367650257, -124.7265625 54.716735300514, -124.7265625 42.75 M-124.7265625 85.5 C-124.7265625 69.363285075291, -124.7265625 53.22657015058199, -124.7265625 42.75"/></g><g class="row-rect-even" style=""><path fill="hsl(180, 1.5873015873%, 2.3529411765%)" stroke-width="0" stroke="none" d="M-124.7265625 85.5 L124.7265625 85.5 L124.7265625 128.25 L-124.7265625 128.25"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-124.7265625 85.5 C-44.19079875908021 85.5, 36.34496498183958 85.5, 124.7265625 85.5 M-124.7265625 85.5 C-55.07778467866852 85.5, 14.570993142662957 85.5, 124.7265625 85.5 M124.7265625 85.5 C124.7265625 95.74905777967764, 124.7265625 105.99811555935528, 124.7265625 128.25 M124.7265625 85.5 C124.7265625 98.29855623279201, 124.7265625 111.09711246558402, 124.7265625 128.25 M124.7265625 128.25 C51.829583807051506 128.25, -21.067394885896988 128.25, -124.7265625 128.25 M124.7265625 128.25 C40.76904477762055 128.25, -43.1884729447589 128.25, -124.7265625 128.25 M-124.7265625 128.25 C-124.7265625 118.678471164278, -124.7265625 109.106942328556, -124.7265625 85.5 M-124.7265625 128.25 C-124.7265625 111.40784081550098, -124.7265625 94.56568163100195, -124.7265625 85.5"/></g><g class="row-rect-odd" style=""><path fill="hsl(180, 1.5873015873%, 17.3529411765%)" stroke-width="0" stroke="none" d="M-124.7265625 128.25 L124.7265625 128.25 L124.7265625 171 L-124.7265625 171"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-124.7265625 128.25 C-58.37767607781811 128.25, 7.971210344363783 128.25, 124.7265625 128.25 M-124.7265625 128.25 C-42.56806494379612 128.25, 39.59043261240777 128.25, 124.7265625 128.25 M124.7265625 128.25 C124.7265625 140.1862826034571, 124.7265625 152.12256520691423, 124.7265625 171 M124.7265625 128.25 C124.7265625 139.9915534785476, 124.7265625 151.73310695709517, 124.7265625 171 M124.7265625 171 C61.57332764936287 171, -1.579907201274267 171, -124.7265625 171 M124.7265625 171 C50.32797634867545 171, -24.070609802649102 171, -124.7265625 171 M-124.7265625 171 C-124.7265625 154.1937412618561, -124.7265625 137.3874825237122, -124.7265625 128.25 M-124.7265625 171 C-124.7265625 157.03264777225863, -124.7265625 143.06529554451728, -124.7265625 128.25"/></g><g class="row-rect-even" style=""><path fill="hsl(180, 1.5873015873%, 17.3529411765%)" stroke-width="0" stroke="none" d="M-124.7265625 -128.25 L124.7265625 -128.25 L124.7265625 -85.5 L-124.7265625 -85.5"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-124.7265625 -128.25 C-63.92527527629142 -128.25, -3.1239880525828454 -128.25, 124.7265625 -128.25 M-124.7265625 -128.25 C-35.13729779598599 -128.25, 54.45196690802803 -128.25, 124.7265625 -128.25 M124.7265625 -128.25 C124.7265625 -111.5720454525437, 124.7265625 -94.89409090508741, 124.7265625 -85.5 M124.7265625 -128.25 C124.7265625 -116.97811288014131, 124.7265625 -105.70622576028262, 124.7265625 -85.5 M124.7265625 -85.5 C71.84640038434408 -85.5, 18.96623826868816 -85.5, -124.7265625 -85.5 M124.7265625 -85.5 C47.663692026171574 -85.5, -29.39917844765685 -85.5, -124.7265625 -85.5 M-124.7265625 -85.5 C-124.7265625 -94.39945707007615, -124.7265625 -103.2989141401523, -124.7265625 -128.25 M-124.7265625 -85.5 C-124.7265625 -96.93017796996082, -124.7265625 -108.36035593992166, -124.7265625 -128.25"/></g><g style="" transform="translate(-19.890625, -161.625)" class="label name"><foreignObject height="24" width="39.78125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 144px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>TODO</p></span></div></foreignObject></g><g style="" transform="translate(-112.2265625, -118.875)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-10.8828125, -118.875)" class="label attribute-name"><foreignObject height="24" width="13.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 112px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>id</p></span></div></foreignObject></g><g style="" transform="translate(94.0859375, -118.875)" class="label attribute-keys"><foreignObject height="24" width="18.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PK</p></span></div></foreignObject></g><g style="" transform="translate(137.2265625, -118.875)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-112.2265625, -76.125)" class="label attribute-type"><foreignObject height="24" width="40.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g style="" transform="translate(-10.8828125, -76.125)" class="label attribute-name"><foreignObject height="24" width="30.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 125px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>title</p></span></div></foreignObject></g><g style="" transform="translate(94.0859375, -76.125)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(137.2265625, -76.125)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-112.2265625, -33.375)" class="label attribute-type"><foreignObject height="24" width="29.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 124px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>text</p></span></div></foreignObject></g><g style="" transform="translate(-10.8828125, -33.375)" class="label attribute-name"><foreignObject height="24" width="79.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 171px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>description</p></span></div></foreignObject></g><g style="" transform="translate(94.0859375, -33.375)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(137.2265625, -33.375)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-112.2265625, 9.375)" class="label attribute-type"><foreignObject height="24" width="40.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g style="" transform="translate(-10.8828125, 9.375)" class="label attribute-name"><foreignObject height="24" width="42.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>status</p></span></div></foreignObject></g><g style="" transform="translate(94.0859375, 9.375)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(137.2265625, 9.375)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-112.2265625, 52.125)" class="label attribute-type"><foreignObject height="24" width="76.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 167px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g style="" transform="translate(-10.8828125, 52.125)" class="label attribute-name"><foreignObject height="24" width="67.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 158px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>due_date</p></span></div></foreignObject></g><g style="" transform="translate(94.0859375, 52.125)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(137.2265625, 52.125)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-112.2265625, 94.875)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-10.8828125, 94.875)" class="label attribute-name"><foreignObject height="24" width="52.03125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 147px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>user_id</p></span></div></foreignObject></g><g style="" transform="translate(94.0859375, 94.875)" class="label attribute-keys"><foreignObject height="24" width="17.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g style="" transform="translate(137.2265625, 94.875)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-112.2265625, 137.625)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-10.8828125, 137.625)" class="label attribute-name"><foreignObject height="24" width="58.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 152px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>team_id</p></span></div></foreignObject></g><g style="" transform="translate(94.0859375, 137.625)" class="label attribute-keys"><foreignObject height="24" width="17.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g style="" transform="translate(137.2265625, 137.625)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-124.7265625 -128.25 C-51.88493532323072 -128.25, 20.95669185353856 -128.25, 124.7265625 -128.25 M-124.7265625 -128.25 C-40.98127435682527 -128.25, 42.764013786349466 -128.25, 124.7265625 -128.25"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-23.3828125 -128.25 C-23.3828125 -23.7022847448238, -23.3828125 80.8454305103524, -23.3828125 171 M-23.3828125 -128.25 C-23.3828125 -19.919178252451786, -23.3828125 88.41164349509643, -23.3828125 171"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M81.5859375 -128.25 C81.5859375 -38.394662410706346, 81.5859375 51.46067517858731, 81.5859375 171 M81.5859375 -128.25 C81.5859375 -16.30095545195138, 81.5859375 95.64808909609724, 81.5859375 171"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-124.7265625 -85.5 C-44.599559276858045 -85.5, 35.52744394628391 -85.5, 124.7265625 -85.5 M-124.7265625 -85.5 C-41.411915903573984 -85.5, 41.90273069285203 -85.5, 124.7265625 -85.5"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-124.7265625 -42.75 C-64.00360332466524 -42.75, -3.2806441493304703 -42.75, 124.7265625 -42.75 M-124.7265625 -42.75 C-25.130137404770082 -42.75, 74.46628769045984 -42.75, 124.7265625 -42.75"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-124.7265625 0 C-27.398560568469136 0, 69.92944136306173 0, 124.7265625 0 M-124.7265625 0 C-57.04742000914604 0, 10.631722481707925 0, 124.7265625 0"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-124.7265625 42.75 C-32.2632794079597 42.75, 60.2000036840806 42.75, 124.7265625 42.75 M-124.7265625 42.75 C-43.9882051103193 42.75, 36.7501522793614 42.75, 124.7265625 42.75"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-124.7265625 85.5 C-52.903663146281716 85.5, 18.91923620743657 85.5, 124.7265625 85.5 M-124.7265625 85.5 C-52.63958465220506 85.5, 19.447393195589882 85.5, 124.7265625 85.5"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-124.7265625 128.25 C-33.086519822554166 128.25, 58.55352285489167 128.25, 124.7265625 128.25 M-124.7265625 128.25 C-41.24795324560371 128.25, 42.23065600879258 128.25, 124.7265625 128.25"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-124.7265625 -128.25 C-26.328717939472682 -128.25, 72.06912662105464 -128.25, 124.7265625 -128.25 M-124.7265625 -128.25 C-52.93319418664397 -128.25, 18.860174126712053 -128.25, 124.7265625 -128.25"/></g></g><g transform="translate(484.0625, 1321.5)" id="entity-USER-3" class="node default"><g style=""><path fill="#1f2020" stroke-width="0" stroke="none" d="M-86.609375 -85.5 L86.609375 -85.5 L86.609375 85.5 L-86.609375 85.5"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-86.609375 -85.5 C-46.68731018283952 -85.5, -6.765245365679036 -85.5, 86.609375 -85.5 M-86.609375 -85.5 C-48.96899069832927 -85.5, -11.328606396658543 -85.5, 86.609375 -85.5 M86.609375 -85.5 C86.609375 -25.96026226958186, 86.609375 33.57947546083628, 86.609375 85.5 M86.609375 -85.5 C86.609375 -23.915765953889398, 86.609375 37.668468092221204, 86.609375 85.5 M86.609375 85.5 C24.33186164821536 85.5, -37.94565170356928 85.5, -86.609375 85.5 M86.609375 85.5 C21.75362834382082 85.5, -43.10211831235836 85.5, -86.609375 85.5 M-86.609375 85.5 C-86.609375 37.085667042042324, -86.609375 -11.328665915915352, -86.609375 -85.5 M-86.609375 85.5 C-86.609375 27.646094392390623, -86.609375 -30.207811215218754, -86.609375 -85.5"/></g><g class="row-rect-odd" style=""><path fill="hsl(180, 1.5873015873%, 17.3529411765%)" stroke-width="0" stroke="none" d="M-86.609375 42.75 L86.609375 42.75 L86.609375 85.5 L-86.609375 85.5"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-86.609375 42.75 C-38.87370737886353 42.75, 8.861960242272943 42.75, 86.609375 42.75 M-86.609375 42.75 C-43.73852106507867 42.75, -0.8676671301573435 42.75, 86.609375 42.75 M86.609375 42.75 C86.609375 59.174219266530315, 86.609375 75.59843853306063, 86.609375 85.5 M86.609375 42.75 C86.609375 51.936408082690804, 86.609375 61.1228161653816, 86.609375 85.5 M86.609375 85.5 C42.99952446250458 85.5, -0.6103260749908372 85.5, -86.609375 85.5 M86.609375 85.5 C27.450281715240884 85.5, -31.708811569518232 85.5, -86.609375 85.5 M-86.609375 85.5 C-86.609375 68.99402816071549, -86.609375 52.48805632143097, -86.609375 42.75 M-86.609375 85.5 C-86.609375 70.95895296673297, -86.609375 56.41790593346592, -86.609375 42.75"/></g><g class="row-rect-even" style=""><path fill="hsl(180, 1.5873015873%, 17.3529411765%)" stroke-width="0" stroke="none" d="M-86.609375 -42.75 L86.609375 -42.75 L86.609375 0 L-86.609375 0"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-86.609375 -42.75 C-31.827879827375206 -42.75, 22.953615345249588 -42.75, 86.609375 -42.75 M-86.609375 -42.75 C-44.32297621569938 -42.75, -2.0365774313987544 -42.75, 86.609375 -42.75 M86.609375 -42.75 C86.609375 -33.12524017802967, 86.609375 -23.50048035605934, 86.609375 0 M86.609375 -42.75 C86.609375 -29.3899747611246, 86.609375 -16.029949522249197, 86.609375 0 M86.609375 0 C30.064592698546235 0, -26.48018960290753 0, -86.609375 0 M86.609375 0 C27.120029327141253 0, -32.36931634571749 0, -86.609375 0 M-86.609375 0 C-86.609375 -12.864075854814892, -86.609375 -25.728151709629785, -86.609375 -42.75 M-86.609375 0 C-86.609375 -12.515007023786795, -86.609375 -25.03001404757359, -86.609375 -42.75"/></g><g style="" transform="translate(-17.9765625, -76.125)" class="label name"><foreignObject height="24" width="35.953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 141px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>USER</p></span></div></foreignObject></g><g style="" transform="translate(-74.109375, -33.375)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-8.734375, -33.375)" class="label attribute-name"><foreignObject height="24" width="13.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 112px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>id</p></span></div></foreignObject></g><g style="" transform="translate(55.96875, -33.375)" class="label attribute-keys"><foreignObject height="24" width="18.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PK</p></span></div></foreignObject></g><g style="" transform="translate(99.109375, -33.375)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-74.109375, 9.375)" class="label attribute-type"><foreignObject height="24" width="40.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g style="" transform="translate(-8.734375, 9.375)" class="label attribute-name"><foreignObject height="24" width="39.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 135px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>name</p></span></div></foreignObject></g><g style="" transform="translate(55.96875, 9.375)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(99.109375, 9.375)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-74.109375, 52.125)" class="label attribute-type"><foreignObject height="24" width="40.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g style="" transform="translate(-8.734375, 52.125)" class="label attribute-name"><foreignObject height="24" width="39.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>email</p></span></div></foreignObject></g><g style="" transform="translate(55.96875, 52.125)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(99.109375, 52.125)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-86.609375 -42.75 C-19.381144829138123 -42.75, 47.847085341723755 -42.75, 86.609375 -42.75 M-86.609375 -42.75 C-25.912185457450448 -42.75, 34.785004085099104 -42.75, 86.609375 -42.75"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-21.234375 -42.75 C-21.234375 -7.858166255254957, -21.234375 27.033667489490085, -21.234375 85.5 M-21.234375 -42.75 C-21.234375 2.872177604939104, -21.234375 48.49435520987821, -21.234375 85.5"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M43.46875 -42.75 C43.46875 3.467746682757948, 43.46875 49.685493365515896, 43.46875 85.5 M43.46875 -42.75 C43.46875 0.439228705893278, 43.46875 43.628457411786556, 43.46875 85.5"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-86.609375 0 C-20.462868162345373 0, 45.683638675309254 0, 86.609375 0 M-86.609375 0 C-38.94038743808946 0, 8.728600123821082 0, 86.609375 0"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-86.609375 42.75 C-33.006246854280704 42.75, 20.596881291438592 42.75, 86.609375 42.75 M-86.609375 42.75 C-24.92290418138215 42.75, 36.7635666372357 42.75, 86.609375 42.75"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-86.609375 -42.75 C-38.00078640824084 -42.75, 10.607802183518316 -42.75, 86.609375 -42.75 M-86.609375 -42.75 C-31.482803893101 -42.75, 23.643767213798 -42.75, 86.609375 -42.75"/></g></g><g transform="translate(837.6640625, 1321.5)" id="entity-STATUS-4" class="node default"><g style=""><path fill="#1f2020" stroke-width="0" stroke="none" d="M-126.9921875 -171 L126.9921875 -171 L126.9921875 171 L-126.9921875 171"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.9921875 -171 C-59.534986245609815 -171, 7.92221500878037 -171, 126.9921875 -171 M-126.9921875 -171 C-59.598506359687946 -171, 7.795174780624109 -171, 126.9921875 -171 M126.9921875 -171 C126.9921875 -95.51739220425496, 126.9921875 -20.03478440850992, 126.9921875 171 M126.9921875 -171 C126.9921875 -41.20423883378675, 126.9921875 88.5915223324265, 126.9921875 171 M126.9921875 171 C56.434031814651576 171, -14.124123870696849 171, -126.9921875 171 M126.9921875 171 C66.32991933087187 171, 5.667651161743748 171, -126.9921875 171 M-126.9921875 171 C-126.9921875 78.54198518581536, -126.9921875 -13.916029628369273, -126.9921875 -171 M-126.9921875 171 C-126.9921875 93.91830694267199, -126.9921875 16.83661388534398, -126.9921875 -171"/></g><g class="row-rect-odd" style=""><path fill="hsl(180, 1.5873015873%, 17.3529411765%)" stroke-width="0" stroke="none" d="M-126.9921875 -42.75 L126.9921875 -42.75 L126.9921875 0 L-126.9921875 0"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.9921875 -42.75 C-54.47763945859775 -42.75, 18.0369085828045 -42.75, 126.9921875 -42.75 M-126.9921875 -42.75 C-70.32077810555398 -42.75, -13.649368711107982 -42.75, 126.9921875 -42.75 M126.9921875 -42.75 C126.9921875 -32.63493775616703, 126.9921875 -22.519875512334053, 126.9921875 0 M126.9921875 -42.75 C126.9921875 -29.17744273718135, 126.9921875 -15.604885474362707, 126.9921875 0 M126.9921875 0 C36.56894206043208 0, -53.85430337913584 0, -126.9921875 0 M126.9921875 0 C42.47904564374028 0, -42.034096212519444 0, -126.9921875 0 M-126.9921875 0 C-126.9921875 -15.65943544770045, -126.9921875 -31.3188708954009, -126.9921875 -42.75 M-126.9921875 0 C-126.9921875 -12.008341743859027, -126.9921875 -24.016683487718055, -126.9921875 -42.75"/></g><g class="row-rect-even" style=""><path fill="hsl(180, 1.5873015873%, 2.3529411765%)" stroke-width="0" stroke="none" d="M-126.9921875 0 L126.9921875 0 L126.9921875 42.75 L-126.9921875 42.75"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.9921875 0 C-72.31876615690328 0, -17.64534481380656 0, 126.9921875 0 M-126.9921875 0 C-48.81699640367637 0, 29.35819469264726 0, 126.9921875 0 M126.9921875 0 C126.9921875 15.841593163594009, 126.9921875 31.683186327188018, 126.9921875 42.75 M126.9921875 0 C126.9921875 9.202875852170102, 126.9921875 18.405751704340204, 126.9921875 42.75 M126.9921875 42.75 C75.2639500414289 42.75, 23.535712582857784 42.75, -126.9921875 42.75 M126.9921875 42.75 C33.30278724826812 42.75, -60.386613003463765 42.75, -126.9921875 42.75 M-126.9921875 42.75 C-126.9921875 26.373181349719538, -126.9921875 9.996362699439075, -126.9921875 0 M-126.9921875 42.75 C-126.9921875 28.10382231319148, -126.9921875 13.457644626382962, -126.9921875 0"/></g><g class="row-rect-odd" style=""><path fill="hsl(180, 1.5873015873%, 17.3529411765%)" stroke-width="0" stroke="none" d="M-126.9921875 42.75 L126.9921875 42.75 L126.9921875 85.5 L-126.9921875 85.5"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.9921875 42.75 C-55.88924051613644 42.75, 15.213706467727121 42.75, 126.9921875 42.75 M-126.9921875 42.75 C-69.86621413062917 42.75, -12.740240761258335 42.75, 126.9921875 42.75 M126.9921875 42.75 C126.9921875 58.019062759804854, 126.9921875 73.28812551960971, 126.9921875 85.5 M126.9921875 42.75 C126.9921875 55.54173623863089, 126.9921875 68.33347247726178, 126.9921875 85.5 M126.9921875 85.5 C58.20558525065215 85.5, -10.581016998695702 85.5, -126.9921875 85.5 M126.9921875 85.5 C71.85376074150741 85.5, 16.71533398301483 85.5, -126.9921875 85.5 M-126.9921875 85.5 C-126.9921875 69.20195599413707, -126.9921875 52.903911988274125, -126.9921875 42.75 M-126.9921875 85.5 C-126.9921875 68.43374597411338, -126.9921875 51.36749194822674, -126.9921875 42.75"/></g><g class="row-rect-even" style=""><path fill="hsl(180, 1.5873015873%, 2.3529411765%)" stroke-width="0" stroke="none" d="M-126.9921875 85.5 L126.9921875 85.5 L126.9921875 128.25 L-126.9921875 128.25"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.9921875 85.5 C-49.205040052021374 85.5, 28.58210739595725 85.5, 126.9921875 85.5 M-126.9921875 85.5 C-48.66611715212291 85.5, 29.659953195754184 85.5, 126.9921875 85.5 M126.9921875 85.5 C126.9921875 96.67292540173568, 126.9921875 107.84585080347138, 126.9921875 128.25 M126.9921875 85.5 C126.9921875 102.4422993739723, 126.9921875 119.38459874794461, 126.9921875 128.25 M126.9921875 128.25 C42.28194492510798 128.25, -42.42829764978404 128.25, -126.9921875 128.25 M126.9921875 128.25 C54.11126426049918 128.25, -18.769658979001633 128.25, -126.9921875 128.25 M-126.9921875 128.25 C-126.9921875 113.94651151086364, -126.9921875 99.64302302172727, -126.9921875 85.5 M-126.9921875 128.25 C-126.9921875 118.53019911694425, -126.9921875 108.81039823388849, -126.9921875 85.5"/></g><g class="row-rect-odd" style=""><path fill="hsl(180, 1.5873015873%, 17.3529411765%)" stroke-width="0" stroke="none" d="M-126.9921875 128.25 L126.9921875 128.25 L126.9921875 171 L-126.9921875 171"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.9921875 128.25 C-40.644992574693646 128.25, 45.70220235061271 128.25, 126.9921875 128.25 M-126.9921875 128.25 C-38.70422823601268 128.25, 49.583731027974636 128.25, 126.9921875 128.25 M126.9921875 128.25 C126.9921875 143.69088568808928, 126.9921875 159.13177137617853, 126.9921875 171 M126.9921875 128.25 C126.9921875 139.43034811508656, 126.9921875 150.61069623017312, 126.9921875 171 M126.9921875 171 C61.40543876459948 171, -4.181309970801038 171, -126.9921875 171 M126.9921875 171 C44.554318068447955 171, -37.88355136310409 171, -126.9921875 171 M-126.9921875 171 C-126.9921875 155.5835612006344, -126.9921875 140.1671224012688, -126.9921875 128.25 M-126.9921875 171 C-126.9921875 161.07512839241159, -126.9921875 151.15025678482317, -126.9921875 128.25"/></g><g class="row-rect-even" style=""><path fill="hsl(180, 1.5873015873%, 17.3529411765%)" stroke-width="0" stroke="none" d="M-126.9921875 -128.25 L126.9921875 -128.25 L126.9921875 -85.5 L-126.9921875 -85.5"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.9921875 -128.25 C-61.272735522997465 -128.25, 4.44671645400507 -128.25, 126.9921875 -128.25 M-126.9921875 -128.25 C-56.46922236380645 -128.25, 14.053742772387096 -128.25, 126.9921875 -128.25 M126.9921875 -128.25 C126.9921875 -117.25077767235825, 126.9921875 -106.25155534471651, 126.9921875 -85.5 M126.9921875 -128.25 C126.9921875 -117.25807225399944, 126.9921875 -106.26614450799889, 126.9921875 -85.5 M126.9921875 -85.5 C66.68217822266459 -85.5, 6.372168945329179 -85.5, -126.9921875 -85.5 M126.9921875 -85.5 C27.398442026252113 -85.5, -72.19530344749577 -85.5, -126.9921875 -85.5 M-126.9921875 -85.5 C-126.9921875 -101.97719369845228, -126.9921875 -118.45438739690456, -126.9921875 -128.25 M-126.9921875 -85.5 C-126.9921875 -95.76964017468126, -126.9921875 -106.03928034936253, -126.9921875 -128.25"/></g><g style="" transform="translate(-25.3359375, -161.625)" class="label name"><foreignObject height="24" width="50.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 157px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>STATUS</p></span></div></foreignObject></g><g style="" transform="translate(-114.4921875, -118.875)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-13.1484375, -118.875)" class="label attribute-name"><foreignObject height="24" width="13.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 112px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>id</p></span></div></foreignObject></g><g style="" transform="translate(96.3515625, -118.875)" class="label attribute-keys"><foreignObject height="24" width="18.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PK</p></span></div></foreignObject></g><g style="" transform="translate(139.4921875, -118.875)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.4921875, -76.125)" class="label attribute-type"><foreignObject height="24" width="40.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g style="" transform="translate(-13.1484375, -76.125)" class="label attribute-name"><foreignObject height="24" width="39.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 135px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>name</p></span></div></foreignObject></g><g style="" transform="translate(96.3515625, -76.125)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.4921875, -76.125)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.4921875, -33.375)" class="label attribute-type"><foreignObject height="24" width="40.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g style="" transform="translate(-13.1484375, -33.375)" class="label attribute-name"><foreignObject height="24" width="47.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 142px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>reason</p></span></div></foreignObject></g><g style="" transform="translate(96.3515625, -33.375)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.4921875, -33.375)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.4921875, 9.375)" class="label attribute-type"><foreignObject height="24" width="29.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>json</p></span></div></foreignObject></g><g style="" transform="translate(-13.1484375, 9.375)" class="label attribute-name"><foreignObject height="24" width="68.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 158px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>metadata</p></span></div></foreignObject></g><g style="" transform="translate(96.3515625, 9.375)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.4921875, 9.375)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.4921875, 52.125)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-13.1484375, 52.125)" class="label attribute-name"><foreignObject height="24" width="66.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 160px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>model_id</p></span></div></foreignObject></g><g style="" transform="translate(96.3515625, 52.125)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.4921875, 52.125)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.4921875, 94.875)" class="label attribute-type"><foreignObject height="24" width="40.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g style="" transform="translate(-13.1484375, 94.875)" class="label attribute-name"><foreignObject height="24" width="84.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 176px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>model_type</p></span></div></foreignObject></g><g style="" transform="translate(96.3515625, 94.875)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.4921875, 94.875)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-114.4921875, 137.625)" class="label attribute-type"><foreignObject height="24" width="76.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 167px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g style="" transform="translate(-13.1484375, 137.625)" class="label attribute-name"><foreignObject height="24" width="78.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 166px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>created_at</p></span></div></foreignObject></g><g style="" transform="translate(96.3515625, 137.625)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(139.4921875, 137.625)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.9921875 -128.25 C-58.28431497868459 -128.25, 10.423557542630817 -128.25, 126.9921875 -128.25 M-126.9921875 -128.25 C-75.72316346368369 -128.25, -24.45413942736738 -128.25, 126.9921875 -128.25"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-25.6484375 -128.25 C-25.6484375 -16.78109425434296, -25.6484375 94.68781149131408, -25.6484375 171 M-25.6484375 -128.25 C-25.6484375 -47.89193003704764, -25.6484375 32.46613992590471, -25.6484375 171"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M83.8515625 -128.25 C83.8515625 -20.98364730964441, 83.8515625 86.28270538071118, 83.8515625 171 M83.8515625 -128.25 C83.8515625 -42.534098943724445, 83.8515625 43.18180211255111, 83.8515625 171"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.9921875 -85.5 C-72.62547427457959 -85.5, -18.258761049159176 -85.5, 126.9921875 -85.5 M-126.9921875 -85.5 C-46.811743717783386 -85.5, 33.36870006443323 -85.5, 126.9921875 -85.5"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.9921875 -42.75 C-75.1062029659845 -42.75, -23.220218431968988 -42.75, 126.9921875 -42.75 M-126.9921875 -42.75 C-45.47322805267828 -42.75, 36.04573139464344 -42.75, 126.9921875 -42.75"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.9921875 0 C-50.86097655683943 0, 25.270234386321135 0, 126.9921875 0 M-126.9921875 0 C-35.704105073102454 0, 55.58397735379509 0, 126.9921875 0"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.9921875 42.75 C-27.877295846303767 42.75, 71.23759580739247 42.75, 126.9921875 42.75 M-126.9921875 42.75 C-64.91429611471658 42.75, -2.8364047294331556 42.75, 126.9921875 42.75"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.9921875 85.5 C-64.32153745367131 85.5, -1.650887407342637 85.5, 126.9921875 85.5 M-126.9921875 85.5 C-39.57085745657413 85.5, 47.85047258685174 85.5, 126.9921875 85.5"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.9921875 128.25 C-39.10244356854196 128.25, 48.787300362916085 128.25, 126.9921875 128.25 M-126.9921875 128.25 C-40.66642606963528 128.25, 45.65933536072944 128.25, 126.9921875 128.25"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.9921875 -128.25 C-40.86506305735945 -128.25, 45.2620613852811 -128.25, 126.9921875 -128.25 M-126.9921875 -128.25 C-45.26801721980456 -128.25, 36.456153060390875 -128.25, 126.9921875 -128.25"/></g></g><g transform="translate(660.86328125, 157.625)" id="entity-TEAM_USER-5" class="node default"><g style=""><path fill="#1f2020" stroke-width="0" stroke="none" d="M-125.796875 -149.625 L125.796875 -149.625 L125.796875 149.625 L-125.796875 149.625"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-125.796875 -149.625 C-43.438518591750224 -149.625, 38.91983781649955 -149.625, 125.796875 -149.625 M-125.796875 -149.625 C-70.49400635789573 -149.625, -15.191137715791442 -149.625, 125.796875 -149.625 M125.796875 -149.625 C125.796875 -54.616711892175516, 125.796875 40.39157621564897, 125.796875 149.625 M125.796875 -149.625 C125.796875 -64.75797025540002, 125.796875 20.10905948919995, 125.796875 149.625 M125.796875 149.625 C57.77099312067058 149.625, -10.254888758658836 149.625, -125.796875 149.625 M125.796875 149.625 C38.65742191878141 149.625, -48.48203116243718 149.625, -125.796875 149.625 M-125.796875 149.625 C-125.796875 56.37698943873934, -125.796875 -36.87102112252131, -125.796875 -149.625 M-125.796875 149.625 C-125.796875 47.86878344632068, -125.796875 -53.887433107358646, -125.796875 -149.625"/></g><g class="row-rect-odd" style=""><path fill="hsl(180, 1.5873015873%, 17.3529411765%)" stroke-width="0" stroke="none" d="M-125.796875 -21.375 L125.796875 -21.375 L125.796875 21.375 L-125.796875 21.375"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-125.796875 -21.375 C-35.550484938878995 -21.375, 54.69590512224201 -21.375, 125.796875 -21.375 M-125.796875 -21.375 C-71.84253930044798 -21.375, -17.88820360089595 -21.375, 125.796875 -21.375 M125.796875 -21.375 C125.796875 -8.971727531997372, 125.796875 3.431544936005256, 125.796875 21.375 M125.796875 -21.375 C125.796875 -11.469295791283265, 125.796875 -1.5635915825665307, 125.796875 21.375 M125.796875 21.375 C42.32013917447986 21.375, -41.156596651040275 21.375, -125.796875 21.375 M125.796875 21.375 C74.6071255946896 21.375, 23.417376189379212 21.375, -125.796875 21.375 M-125.796875 21.375 C-125.796875 10.34511981859493, -125.796875 -0.6847603628101417, -125.796875 -21.375 M-125.796875 21.375 C-125.796875 9.694560706970753, -125.796875 -1.9858785860584938, -125.796875 -21.375"/></g><g class="row-rect-even" style=""><path fill="hsl(180, 1.5873015873%, 2.3529411765%)" stroke-width="0" stroke="none" d="M-125.796875 21.375 L125.796875 21.375 L125.796875 64.125 L-125.796875 64.125"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-125.796875 21.375 C-41.86448518937151 21.375, 42.067904621256986 21.375, 125.796875 21.375 M-125.796875 21.375 C-59.128536203561936 21.375, 7.5398025928761285 21.375, 125.796875 21.375 M125.796875 21.375 C125.796875 30.09814918795204, 125.796875 38.82129837590408, 125.796875 64.125 M125.796875 21.375 C125.796875 32.16210102395226, 125.796875 42.94920204790453, 125.796875 64.125 M125.796875 64.125 C67.73337617495218 64.125, 9.669877349904354 64.125, -125.796875 64.125 M125.796875 64.125 C36.64670302744713 64.125, -52.50346894510574 64.125, -125.796875 64.125 M-125.796875 64.125 C-125.796875 53.04125309261909, -125.796875 41.95750618523818, -125.796875 21.375 M-125.796875 64.125 C-125.796875 54.37829373656322, -125.796875 44.631587473126444, -125.796875 21.375"/></g><g class="row-rect-odd" style=""><path fill="hsl(180, 1.5873015873%, 17.3529411765%)" stroke-width="0" stroke="none" d="M-125.796875 64.125 L125.796875 64.125 L125.796875 106.875 L-125.796875 106.875"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-125.796875 64.125 C-70.38814611237123 64.125, -14.979417224742463 64.125, 125.796875 64.125 M-125.796875 64.125 C-42.2052907204159 64.125, 41.386293559168195 64.125, 125.796875 64.125 M125.796875 64.125 C125.796875 74.53082834547101, 125.796875 84.93665669094202, 125.796875 106.875 M125.796875 64.125 C125.796875 80.10580980864725, 125.796875 96.08661961729449, 125.796875 106.875 M125.796875 106.875 C55.36383595079769 106.875, -15.069203098404614 106.875, -125.796875 106.875 M125.796875 106.875 C31.48129984325611 106.875, -62.83427531348778 106.875, -125.796875 106.875 M-125.796875 106.875 C-125.796875 90.87112741172986, -125.796875 74.86725482345972, -125.796875 64.125 M-125.796875 106.875 C-125.796875 90.12089808388987, -125.796875 73.36679616777975, -125.796875 64.125"/></g><g class="row-rect-even" style=""><path fill="hsl(180, 1.5873015873%, 2.3529411765%)" stroke-width="0" stroke="none" d="M-125.796875 106.875 L125.796875 106.875 L125.796875 149.625 L-125.796875 149.625"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-125.796875 106.875 C-45.85729899066283 106.875, 34.08227701867435 106.875, 125.796875 106.875 M-125.796875 106.875 C-56.16421644215495 106.875, 13.468442115690095 106.875, 125.796875 106.875 M125.796875 106.875 C125.796875 120.85565708058172, 125.796875 134.83631416116344, 125.796875 149.625 M125.796875 106.875 C125.796875 115.84499988011027, 125.796875 124.81499976022053, 125.796875 149.625 M125.796875 149.625 C73.31795461430184 149.625, 20.83903422860368 149.625, -125.796875 149.625 M125.796875 149.625 C43.02208386961493 149.625, -39.75270726077014 149.625, -125.796875 149.625 M-125.796875 149.625 C-125.796875 139.93784456537247, -125.796875 130.25068913074495, -125.796875 106.875 M-125.796875 149.625 C-125.796875 138.65240085317757, -125.796875 127.67980170635515, -125.796875 106.875"/></g><g class="row-rect-odd" style=""><path fill="hsl(180, 1.5873015873%, 17.3529411765%)" stroke-width="0" stroke="none" d="M-125.796875 -106.875 L125.796875 -106.875 L125.796875 -64.125 L-125.796875 -64.125"/><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-125.796875 -106.875 C-73.83064969475873 -106.875, -21.864424389517467 -106.875, 125.796875 -106.875 M-125.796875 -106.875 C-62.167839661567044 -106.875, 1.4611956768659127 -106.875, 125.796875 -106.875 M125.796875 -106.875 C125.796875 -94.44279003699705, 125.796875 -82.01058007399409, 125.796875 -64.125 M125.796875 -106.875 C125.796875 -97.42531919660829, 125.796875 -87.97563839321658, 125.796875 -64.125 M125.796875 -64.125 C32.54134158302236 -64.125, -60.71419183395528 -64.125, -125.796875 -64.125 M125.796875 -64.125 C46.931637582756835 -64.125, -31.93359983448633 -64.125, -125.796875 -64.125 M-125.796875 -64.125 C-125.796875 -74.91106979581724, -125.796875 -85.69713959163448, -125.796875 -106.875 M-125.796875 -64.125 C-125.796875 -81.17344452698624, -125.796875 -98.22188905397249, -125.796875 -106.875"/></g><g style="" transform="translate(-41.5, -140.25)" class="label name"><foreignObject height="24" width="83"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 194px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>TEAM_USER</p></span></div></foreignObject></g><g style="" transform="translate(-113.296875, -97.5)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-11.953125, -97.5)" class="label attribute-name"><foreignObject height="24" width="13.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 112px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>id</p></span></div></foreignObject></g><g style="" transform="translate(95.15625, -97.5)" class="label attribute-keys"><foreignObject height="24" width="18.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PK</p></span></div></foreignObject></g><g style="" transform="translate(138.296875, -97.5)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-113.296875, -54.75)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-11.953125, -54.75)" class="label attribute-name"><foreignObject height="24" width="58.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 152px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>team_id</p></span></div></foreignObject></g><g style="" transform="translate(95.15625, -54.75)" class="label attribute-keys"><foreignObject height="24" width="17.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g style="" transform="translate(138.296875, -54.75)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-113.296875, -12)" class="label attribute-type"><foreignObject height="24" width="30.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 128px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g style="" transform="translate(-11.953125, -12)" class="label attribute-name"><foreignObject height="24" width="52.03125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 147px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>user_id</p></span></div></foreignObject></g><g style="" transform="translate(95.15625, -12)" class="label attribute-keys"><foreignObject height="24" width="17.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g style="" transform="translate(138.296875, -12)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-113.296875, 30.75)" class="label attribute-type"><foreignObject height="24" width="40.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g style="" transform="translate(-11.953125, 30.75)" class="label attribute-name"><foreignObject height="24" width="28.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 125px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>role</p></span></div></foreignObject></g><g style="" transform="translate(95.15625, 30.75)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(138.296875, 30.75)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-113.296875, 73.5)" class="label attribute-type"><foreignObject height="24" width="76.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 167px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g style="" transform="translate(-11.953125, 73.5)" class="label attribute-name"><foreignObject height="24" width="78.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 166px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>created_at</p></span></div></foreignObject></g><g style="" transform="translate(95.15625, 73.5)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(138.296875, 73.5)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(-113.296875, 116.25)" class="label attribute-type"><foreignObject height="24" width="76.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 167px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g style="" transform="translate(-11.953125, 116.25)" class="label attribute-name"><foreignObject height="24" width="82.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 170px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>updated_at</p></span></div></foreignObject></g><g style="" transform="translate(95.15625, 116.25)" class="label attribute-keys"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g style="" transform="translate(138.296875, 116.25)" class="label attribute-comment"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-125.796875 -106.875 C-36.120867453598365 -106.875, 53.55514009280327 -106.875, 125.796875 -106.875 M-125.796875 -106.875 C-49.338152773464046 -106.875, 27.12056945307191 -106.875, 125.796875 -106.875"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-24.453125 -106.875 C-24.453125 -42.88795486245013, -24.453125 21.09909027509974, -24.453125 149.625 M-24.453125 -106.875 C-24.453125 -39.00135428538259, -24.453125 28.87229142923482, -24.453125 149.625"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M82.65625 -106.875 C82.65625 -51.70126347237626, 82.65625 3.4724730552474767, 82.65625 149.625 M82.65625 -106.875 C82.65625 -21.33480345288872, 82.65625 64.20539309422256, 82.65625 149.625"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-125.796875 -64.125 C-61.5700452282593 -64.125, 2.6567845434813933 -64.125, 125.796875 -64.125 M-125.796875 -64.125 C-35.281117084843174 -64.125, 55.23464083031365 -64.125, 125.796875 -64.125"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-125.796875 -21.375 C-42.88518576662314 -21.375, 40.026503466753724 -21.375, 125.796875 -21.375 M-125.796875 -21.375 C-46.32053193666171 -21.375, 33.15581112667658 -21.375, 125.796875 -21.375"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-125.796875 21.375 C-35.01192777628759 21.375, 55.773019447424815 21.375, 125.796875 21.375 M-125.796875 21.375 C-45.96484236255627 21.375, 33.86719027488746 21.375, 125.796875 21.375"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-125.796875 64.125 C-53.933048676749664 64.125, 17.930777646500673 64.125, 125.796875 64.125 M-125.796875 64.125 C-62.925780172930324 64.125, -0.05468534586064777 64.125, 125.796875 64.125"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-125.796875 106.875 C-64.79456661902888 106.875, -3.7922582380577694 106.875, 125.796875 106.875 M-125.796875 106.875 C-25.604160197498373 106.875, 74.58855460500325 106.875, 125.796875 106.875"/></g><g class="divider"><path fill="none" stroke-width="1.3" stroke="#ccc" d="M-125.796875 -106.875 C-54.42329650794801 -106.875, 16.95028198410398 -106.875, 125.796875 -106.875 M-125.796875 -106.875 C-61.54847269919394 -106.875, 2.6999296016121264 -106.875, 125.796875 -106.875"/></g></g><g transform="translate(1104.706250000745, 1321.5)" id="entity-TEAM-0---entity-TEAM-0---1" class="label edgeLabel"><rect height="0.1" width="0.1"/><g transform="translate(0, 0)" style="" class="label"><rect/><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 10px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g></g><g transform="translate(516.3531250003725, 1679.050000000745)" id="entity-TEAM-0---entity-TEAM-0---2" class="label edgeLabel"><rect height="0.1" width="0.1"/><g transform="translate(0, 0)" style="" class="label"><rect/><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 10px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g></g><g transform="translate(1411.6015625018626, 1679.050000000745)" id="entity-CATEGORY-1---entity-CATEGORY-1---1" class="label edgeLabel"><rect height="0.1" width="0.1"/><g transform="translate(0, 0)" style="" class="label"><rect/><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 10px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g></g><g transform="translate(1371.5765625014901, 1780.1500000022352)" id="entity-CATEGORY-1---entity-CATEGORY-1---2" class="label edgeLabel"><rect height="0.1" width="0.1"/><g transform="translate(0, 0)" style="" class="label"><rect/><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 10px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g></g></g></g></g></svg>