<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 804.44140625 1298.5" style="max-width: 804.44140625px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#a44141;}#my-svg .error-text{fill:#ddd;stroke:#ddd;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#ecf0f1;stroke:#ecf0f1;}#my-svg .marker.cross{stroke:#ecf0f1;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#my-svg .cluster-label text{fill:#F9FFFE;}#my-svg .cluster-label span{color:#F9FFFE;}#my-svg .cluster-label span p{background-color:transparent;}#my-svg .label text,#my-svg span{fill:#ccc;color:#ccc;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#my-svg .rough-node .label text,#my-svg .node .label text,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-anchor:middle;}#my-svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#my-svg .rough-node .label,#my-svg .node .label,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-align:center;}#my-svg .node.clickable{cursor:pointer;}#my-svg .root .anchor path{fill:#ecf0f1!important;stroke-width:0;stroke:#ecf0f1;}#my-svg .arrowheadPath{fill:lightgrey;}#my-svg .edgePath .path{stroke:#ecf0f1;stroke-width:2.0px;}#my-svg .flowchart-link{stroke:#ecf0f1;fill:none;}#my-svg .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#my-svg .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#my-svg .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#my-svg .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#my-svg .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#my-svg .cluster text{fill:#F9FFFE;}#my-svg .cluster span{color:#F9FFFE;}#my-svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:#282c34;border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#my-svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#my-svg rect.text{fill:none;stroke-width:0;}#my-svg .icon-shape,#my-svg .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#my-svg .icon-shape p,#my-svg .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#my-svg .icon-shape rect,#my-svg .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg .commandSide&gt;*{fill:#2c3e50!important;stroke:#7f8c8d!important;color:#ecf0f1!important;}#my-svg .commandSide span{fill:#2c3e50!important;stroke:#7f8c8d!important;color:#ecf0f1!important;}#my-svg .commandSide tspan{fill:#ecf0f1!important;}#my-svg .querySide&gt;*{fill:#34495e!important;stroke:#7f8c8d!important;color:#ecf0f1!important;}#my-svg .querySide span{fill:#34495e!important;stroke:#7f8c8d!important;color:#ecf0f1!important;}#my-svg .querySide tspan{fill:#ecf0f1!important;}#my-svg .sync&gt;*{fill:#3c6382!important;stroke:#7f8c8d!important;color:#ecf0f1!important;}#my-svg .sync span{fill:#3c6382!important;stroke:#7f8c8d!important;color:#ecf0f1!important;}#my-svg .sync tspan{fill:#ecf0f1!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"/></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><g class="root"><g class="clusters"><g data-look="classic" id="Synchronization" class="cluster"><rect height="233" width="237.71875" y="824.5" x="558.72265625" style=""/><g transform="translate(620.91015625, 824.5)" class="cluster-label"><foreignObject height="24" width="113.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Synchronization</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph1" class="cluster"><rect height="466" width="244.625" y="824.5" x="8" style=""/><g transform="translate(91.7578125, 824.5)" class="cluster-label"><foreignObject height="24" width="77.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Query Side</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph0" class="cluster"><rect height="441" width="266.09765625" y="487.5" x="272.625" style=""/><g transform="translate(352.923828125, 487.5)" class="cluster-label"><foreignObject height="24" width="105.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Command Side</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M273.617,62L273.617,66.167C273.617,70.333,273.617,78.667,273.617,86.333C273.617,94,273.617,101,273.617,104.5L273.617,108"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_0" d="M273.617,166L273.617,170.167C273.617,174.333,273.617,182.667,273.687,190.417C273.758,198.167,273.898,205.334,273.969,208.917L274.039,212.501"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_D_0" d="M323.21,364.907L337.312,379.173C351.415,393.438,379.619,421.969,393.722,442.401C407.824,462.833,407.824,475.167,407.824,484.833C407.824,494.5,407.824,501.5,407.824,505L407.824,508.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_E_0" d="M222.526,362.409L206.29,377.091C190.054,391.773,157.582,421.136,141.346,441.985C125.109,462.833,125.109,475.167,125.109,490C125.109,504.833,125.109,522.167,125.109,539.5C125.109,556.833,125.109,574.167,125.109,591.5C125.109,608.833,125.109,626.167,125.109,643.5C125.109,660.833,125.109,678.167,125.109,695.5C125.109,712.833,125.109,730.167,125.109,747.5C125.109,764.833,125.109,782.167,125.109,795C125.109,807.833,125.109,816.167,125.109,823.833C125.109,831.5,125.109,838.5,125.109,842L125.109,845.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_F_0" d="M407.824,566.5L407.824,570.667C407.824,574.833,407.824,583.167,407.824,590.833C407.824,598.5,407.824,605.5,407.824,609L407.824,612.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_G_0" d="M125.109,903.5L125.109,907.667C125.109,911.833,125.109,920.167,125.109,928.5C125.109,936.833,125.109,945.167,125.109,952.833C125.109,960.5,125.109,967.5,125.109,971L125.109,974.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_H_0" d="M407.824,670.5L407.824,674.667C407.824,678.833,407.824,687.167,407.824,694.833C407.824,702.5,407.824,709.5,407.824,713L407.824,716.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_I_0" d="M402.632,774.5L401.831,778.667C401.029,782.833,399.427,791.167,398.626,799.5C397.824,807.833,397.824,816.167,397.824,823.833C397.824,831.5,397.824,838.5,397.824,842L397.824,845.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_J_0" d="M125.109,1032.5L125.109,1036.667C125.109,1040.833,125.109,1049.167,125.109,1057.5C125.109,1065.833,125.109,1074.167,125.785,1081.845C126.46,1089.524,127.811,1096.548,128.486,1100.06L129.162,1103.572"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_K_0" d="M135.109,1161.5L135.109,1165.667C135.109,1169.833,135.109,1178.167,135.109,1185.833C135.109,1193.5,135.109,1200.5,135.109,1204L135.109,1207.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_L_0" d="M436.615,774.5L441.058,778.667C445.501,782.833,454.387,791.167,458.83,799.5C463.273,807.833,463.273,816.167,487.568,826.228C511.862,836.29,560.45,848.079,584.744,853.974L609.039,859.869"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_M_0" d="M677.582,903.5L677.582,907.667C677.582,911.833,677.582,920.167,677.582,928.5C677.582,936.833,677.582,945.167,677.582,952.833C677.582,960.5,677.582,967.5,677.582,971L677.582,974.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_M_J_0" d="M677.582,1032.5L677.582,1036.667C677.582,1040.833,677.582,1049.167,595.296,1057.5C513.01,1065.833,348.439,1074.167,262.702,1082.014C176.965,1089.861,170.064,1097.221,166.613,1100.902L163.162,1104.582"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(407.82421875, 450.5)" class="edgeLabel"><g transform="translate(-35.390625, -12)" class="label"><foreignObject height="24" width="70.78125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Command</p></span></div></foreignObject></g></g><g transform="translate(125.109375, 643.5)" class="edgeLabel"><g transform="translate(-21.1953125, -12)" class="label"><foreignObject height="24" width="42.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Query</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(273.6171875, 35)" id="flowchart-A-0" class="node default"><rect height="54" width="102.671875" y="-27" x="-51.3359375" style="" class="basic label-container"/><g transform="translate(-21.3359375, -12)" style="" class="label"><rect/><foreignObject height="24" width="42.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Client</p></span></div></foreignObject></g></g><g transform="translate(273.6171875, 139)" id="flowchart-B-1" class="node default"><rect height="54" width="160.0625" y="-27" x="-80.03125" style="" class="basic label-container"/><g transform="translate(-50.03125, -12)" style="" class="label"><rect/><foreignObject height="24" width="100.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>API Controller</p></span></div></foreignObject></g></g><g transform="translate(273.6171875, 314.75)" id="flowchart-C-3" class="node default"><polygon transform="translate(-98.75,98.75)" class="label-container" points="98.75,0 197.5,-98.75 98.75,-197.5 0,-98.75"/><g transform="translate(-71.75, -12)" style="" class="label"><rect/><foreignObject height="24" width="143.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Command or Query?</p></span></div></foreignObject></g></g><g transform="translate(407.82421875, 539.5)" id="flowchart-D-5" class="node default commandSide"><rect height="54" width="159.875" y="-27" x="-79.9375" style="fill:#2c3e50 !important;stroke:#7f8c8d !important" class="basic label-container"/><g transform="translate(-49.9375, -12)" style="color:#ecf0f1 !important" class="label"><rect/><foreignObject height="24" width="99.875"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(236, 240, 241) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#ecf0f1 !important"><p>Command Bus</p></span></div></foreignObject></g></g><g transform="translate(125.109375, 876.5)" id="flowchart-E-7" class="node default querySide"><rect height="54" width="131.484375" y="-27" x="-65.7421875" style="fill:#34495e !important;stroke:#7f8c8d !important" class="basic label-container"/><g transform="translate(-35.7421875, -12)" style="color:#ecf0f1 !important" class="label"><rect/><foreignObject height="24" width="71.484375"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(236, 240, 241) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#ecf0f1 !important"><p>Query Bus</p></span></div></foreignObject></g></g><g transform="translate(407.82421875, 643.5)" id="flowchart-F-9" class="node default commandSide"><rect height="54" width="191.796875" y="-27" x="-95.8984375" style="fill:#2c3e50 !important;stroke:#7f8c8d !important" class="basic label-container"/><g transform="translate(-65.8984375, -12)" style="color:#ecf0f1 !important" class="label"><rect/><foreignObject height="24" width="131.796875"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(236, 240, 241) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#ecf0f1 !important"><p>Command Handler</p></span></div></foreignObject></g></g><g transform="translate(125.109375, 1005.5)" id="flowchart-G-11" class="node default querySide"><rect height="54" width="163.40625" y="-27" x="-81.703125" style="fill:#34495e !important;stroke:#7f8c8d !important" class="basic label-container"/><g transform="translate(-51.703125, -12)" style="color:#ecf0f1 !important" class="label"><rect/><foreignObject height="24" width="103.40625"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(236, 240, 241) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#ecf0f1 !important"><p>Query Handler</p></span></div></foreignObject></g></g><g transform="translate(407.82421875, 747.5)" id="flowchart-H-13" class="node default commandSide"><rect height="54" width="160.515625" y="-27" x="-80.2578125" style="fill:#2c3e50 !important;stroke:#7f8c8d !important" class="basic label-container"/><g transform="translate(-50.2578125, -12)" style="color:#ecf0f1 !important" class="label"><rect/><foreignObject height="24" width="100.515625"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(236, 240, 241) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#ecf0f1 !important"><p>Domain Model</p></span></div></foreignObject></g></g><g transform="translate(397.82421875, 876.5)" id="flowchart-I-15" class="node default commandSide"><rect height="54" width="169" y="-27" x="-84.5" style="fill:#2c3e50 !important;stroke:#7f8c8d !important" class="basic label-container"/><g transform="translate(-54.5, -12)" style="color:#ecf0f1 !important" class="label"><rect/><foreignObject height="24" width="109"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(236, 240, 241) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#ecf0f1 !important"><p>Write Database</p></span></div></foreignObject></g></g><g transform="translate(135.109375, 1134.5)" id="flowchart-J-17" class="node default querySide"><rect height="54" width="141.828125" y="-27" x="-70.9140625" style="fill:#34495e !important;stroke:#7f8c8d !important" class="basic label-container"/><g transform="translate(-40.9140625, -12)" style="color:#ecf0f1 !important" class="label"><rect/><foreignObject height="24" width="81.828125"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(236, 240, 241) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#ecf0f1 !important"><p>Read Model</p></span></div></foreignObject></g></g><g transform="translate(135.109375, 1238.5)" id="flowchart-K-19" class="node default querySide"><rect height="54" width="165.03125" y="-27" x="-82.515625" style="fill:#34495e !important;stroke:#7f8c8d !important" class="basic label-container"/><g transform="translate(-52.515625, -12)" style="color:#ecf0f1 !important" class="label"><rect/><foreignObject height="24" width="105.03125"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(236, 240, 241) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#ecf0f1 !important"><p>Read Database</p></span></div></foreignObject></g></g><g transform="translate(677.58203125, 876.5)" id="flowchart-L-21" class="node default sync"><rect height="54" width="129.3125" y="-27" x="-64.65625" style="fill:#3c6382 !important;stroke:#7f8c8d !important" class="basic label-container"/><g transform="translate(-34.65625, -12)" style="color:#ecf0f1 !important" class="label"><rect/><foreignObject height="24" width="69.3125"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(236, 240, 241) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#ecf0f1 !important"><p>Event Bus</p></span></div></foreignObject></g></g><g transform="translate(677.58203125, 1005.5)" id="flowchart-M-23" class="node default sync"><rect height="54" width="167.71875" y="-27" x="-83.859375" style="fill:#3c6382 !important;stroke:#7f8c8d !important" class="basic label-container"/><g transform="translate(-53.859375, -12)" style="color:#ecf0f1 !important" class="label"><rect/><foreignObject height="24" width="107.71875"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(236, 240, 241) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#ecf0f1 !important"><p>Event Handlers</p></span></div></foreignObject></g></g></g></g></g></svg>