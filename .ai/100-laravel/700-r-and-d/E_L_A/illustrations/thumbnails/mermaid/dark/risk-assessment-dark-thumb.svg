<svg aria-roledescription="quadrantChart" role="graphics-document document" viewBox="0 0 500 500" style="max-width: 500px; background-color: #2d333b;" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#a44141;}#my-svg .error-text{fill:#ddd;stroke:#ddd;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#ecf0f1;stroke:#ecf0f1;}#my-svg .marker.cross{stroke:#ecf0f1;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g/><g class="main"><g class="quadrants"><g class="quadrant"><rect fill="#2c3e50" height="212" width="232" y="45" x="263"/><text transform="translate(379, 50) rotate(0)" text-anchor="middle" dominant-baseline="hanging" font-size="16" fill="#ecf0f1" y="0" x="0">Critical Risks</text></g><g class="quadrant"><rect fill="#314355" height="212" width="232" y="45" x="31"/><text transform="translate(147, 50) rotate(0)" text-anchor="middle" dominant-baseline="hanging" font-size="16" fill="#e7ebec" y="0" x="0">High Priority</text></g><g class="quadrant"><rect fill="#36485a" height="212" width="232" y="257" x="31"/><text transform="translate(147, 262) rotate(0)" text-anchor="middle" dominant-baseline="hanging" font-size="16" fill="#e2e6e7" y="0" x="0">Low Priority</text></g><g class="quadrant"><rect fill="#3b4d5f" height="212" width="232" y="257" x="263"/><text transform="translate(379, 262) rotate(0)" text-anchor="middle" dominant-baseline="hanging" font-size="16" fill="#dde1e2" y="0" x="0">Medium Priority</text></g></g><g class="border"><line style="stroke: rgb(127, 140, 141); stroke-width: 2;" y2="45" x2="496" y1="45" x1="30"/><line style="stroke: rgb(127, 140, 141); stroke-width: 2;" y2="468" x2="495" y1="46" x1="495"/><line style="stroke: rgb(127, 140, 141); stroke-width: 2;" y2="469" x2="496" y1="469" x1="30"/><line style="stroke: rgb(127, 140, 141); stroke-width: 2;" y2="468" x2="31" y1="46" x1="31"/><line style="stroke: rgb(127, 140, 141); stroke-width: 1;" y2="468" x2="263" y1="46" x1="263"/><line style="stroke: rgb(127, 140, 141); stroke-width: 1;" y2="257" x2="494" y1="257" x1="32"/></g><g class="data-points"><g class="data-point"><circle stroke-width="0px" stroke="hsl(210, 29.0322580645%, NaN%)" fill="hsl(210, 29.0322580645%, NaN%)" r="5" cy="172.20000000000002" cx="355.8"/><text transform="translate(355.8, 177.20000000000002) rotate(0)" text-anchor="middle" dominant-baseline="hanging" font-size="12" fill="#ecf0f1" y="0" x="0">User adoption challenges</text></g><g class="data-point"><circle stroke-width="0px" stroke="hsl(210, 29.0322580645%, NaN%)" fill="hsl(210, 29.0322580645%, NaN%)" r="5" cy="214.60000000000002" cx="216.6"/><text transform="translate(216.6, 219.60000000000002) rotate(0)" text-anchor="middle" dominant-baseline="hanging" font-size="12" fill="#ecf0f1" y="0" x="0">Deployment delays</text></g><g class="data-point"><circle stroke-width="0px" stroke="hsl(210, 29.0322580645%, NaN%)" fill="hsl(210, 29.0322580645%, NaN%)" r="5" cy="299.4" cx="309.4"/><text transform="translate(309.4, 304.4) rotate(0)" text-anchor="middle" dominant-baseline="hanging" font-size="12" fill="#ecf0f1" y="0" x="0">Integration issues</text></g><g class="data-point"><circle stroke-width="0px" stroke="hsl(210, 29.0322580645%, NaN%)" fill="hsl(210, 29.0322580645%, NaN%)" r="5" cy="257" cx="263"/><text transform="translate(263, 262) rotate(0)" text-anchor="middle" dominant-baseline="hanging" font-size="12" fill="#ecf0f1" y="0" x="0">Team resource constraints</text></g><g class="data-point"><circle stroke-width="0px" stroke="hsl(210, 29.0322580645%, NaN%)" fill="hsl(210, 29.0322580645%, NaN%)" r="5" cy="257" cx="402.2"/><text transform="translate(402.2, 262) rotate(0)" text-anchor="middle" dominant-baseline="hanging" font-size="12" fill="#ecf0f1" y="0" x="0">Scope creep</text></g><g class="data-point"><circle stroke-width="0px" stroke="hsl(210, 29.0322580645%, NaN%)" fill="hsl(210, 29.0322580645%, NaN%)" r="5" cy="87.39999999999999" cx="123.8"/><text transform="translate(123.8, 92.39999999999999) rotate(0)" text-anchor="middle" dominant-baseline="hanging" font-size="12" fill="#ecf0f1" y="0" x="0">Security vulnerabilities</text></g><g class="data-point"><circle stroke-width="0px" stroke="hsl(210, 29.0322580645%, NaN%)" fill="hsl(210, 29.0322580645%, NaN%)" r="5" cy="129.79999999999998" cx="170.2"/><text transform="translate(170.2, 134.79999999999998) rotate(0)" text-anchor="middle" dominant-baseline="hanging" font-size="12" fill="#ecf0f1" y="0" x="0">Performance bottlenecks</text></g><g class="data-point"><circle stroke-width="0px" stroke="hsl(210, 29.0322580645%, NaN%)" fill="hsl(210, 29.0322580645%, NaN%)" r="5" cy="129.79999999999998" cx="263"/><text transform="translate(263, 134.79999999999998) rotate(0)" text-anchor="middle" dominant-baseline="hanging" font-size="12" fill="#ecf0f1" y="0" x="0">Laravel 12 compatibility issues</text></g></g><g class="labels"><g class="label"><text transform="translate(147, 479) rotate(0)" text-anchor="middle" dominant-baseline="hanging" font-size="16" fill="#ecf0f1" y="0" x="0">Low</text></g><g class="label"><text transform="translate(379, 479) rotate(0)" text-anchor="middle" dominant-baseline="hanging" font-size="16" fill="#ecf0f1" y="0" x="0">High</text></g><g class="label"><text transform="translate(5, 363) rotate(-90)" text-anchor="middle" dominant-baseline="hanging" font-size="16" fill="#ecf0f1" y="0" x="0">Low</text></g><g class="label"><text transform="translate(5, 151) rotate(-90)" text-anchor="middle" dominant-baseline="hanging" font-size="16" fill="#ecf0f1" y="0" x="0">High</text></g></g><g class="title"><text transform="translate(250, 10) rotate(0)" text-anchor="middle" dominant-baseline="hanging" font-size="20" fill="#ecf0f1" y="0" x="0">Risk Assessment Matrix</text></g></g></svg>