<svg aria-roledescription="class" role="graphics-document document" viewBox="0 0 3479.275390625 3247.5078125" style="max-width: 3479.28px; background-color: #2d333b;" class="classDiagram" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#a44141;}#my-svg .error-text{fill:#ddd;stroke:#ddd;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#ecf0f1;stroke:#ecf0f1;}#my-svg .marker.cross{stroke:#ecf0f1;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg g.classGroup text{fill:#ccc;stroke:none;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:10px;}#my-svg g.classGroup text .title{font-weight:bolder;}#my-svg .nodeLabel,#my-svg .edgeLabel{color:#ecf0f1;}#my-svg .edgeLabel .label rect{fill:#1f2020;}#my-svg .label text{fill:#ecf0f1;}#my-svg .labelBkg{background:#1f2020;}#my-svg .edgeLabel .label span{background:#1f2020;}#my-svg .classTitle{font-weight:bolder;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#my-svg .divider{stroke:#ccc;stroke-width:1;}#my-svg g.clickable{cursor:pointer;}#my-svg g.classGroup rect{fill:#1f2020;stroke:#ccc;}#my-svg g.classGroup line{stroke:#ccc;stroke-width:1;}#my-svg .classLabel .box{stroke:none;stroke-width:0;fill:#1f2020;opacity:0.5;}#my-svg .classLabel .label{fill:#ccc;font-size:10px;}#my-svg .relation{stroke:#ecf0f1;stroke-width:1;fill:none;}#my-svg .dashed-line{stroke-dasharray:3;}#my-svg .dotted-line{stroke-dasharray:1 2;}#my-svg #compositionStart,#my-svg .composition{fill:#ecf0f1!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg #compositionEnd,#my-svg .composition{fill:#ecf0f1!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg #dependencyStart,#my-svg .dependency{fill:#ecf0f1!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg #dependencyStart,#my-svg .dependency{fill:#ecf0f1!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg #extensionStart,#my-svg .extension{fill:transparent!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg #extensionEnd,#my-svg .extension{fill:transparent!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg #aggregationStart,#my-svg .aggregation{fill:transparent!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg #aggregationEnd,#my-svg .aggregation{fill:transparent!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg #lollipopStart,#my-svg .lollipop{fill:#1f2020!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg #lollipopEnd,#my-svg .lollipop{fill:#1f2020!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg .edgeTerminals{font-size:11px;line-height:initial;}#my-svg .classTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="18" class="marker aggregation class" id="my-svg_class-aggregationStart"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="1" class="marker aggregation class" id="my-svg_class-aggregationEnd"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="18" class="marker extension class" id="my-svg_class-extensionStart"><path d="M 1,7 L18,13 V 1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="1" class="marker extension class" id="my-svg_class-extensionEnd"><path d="M 1,1 V 13 L18,7 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="18" class="marker composition class" id="my-svg_class-compositionStart"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="1" class="marker composition class" id="my-svg_class-compositionEnd"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="6" class="marker dependency class" id="my-svg_class-dependencyStart"><path d="M 5,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="13" class="marker dependency class" id="my-svg_class-dependencyEnd"><path d="M 18,7 L9,13 L14,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="13" class="marker lollipop class" id="my-svg_class-lollipopStart"><circle r="6" cy="7" cx="7" fill="transparent" stroke="black"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="1" class="marker lollipop class" id="my-svg_class-lollipopEnd"><circle r="6" cy="7" cx="7" fill="transparent" stroke="black"/></marker></defs><g class="root"><g class="clusters"/><g class="edgePaths"><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_User_TeamUser_1" d="M741.932,635.996L734.805,649.497C727.678,662.998,713.424,689.999,706.297,757.666C699.17,825.333,699.17,933.667,699.17,1042C699.17,1150.333,699.17,1258.667,690.072,1343C680.973,1427.333,662.777,1487.667,653.679,1517.833L644.581,1548"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Team_TeamUser_2" d="M1472.197,1079.213L1320.743,1127.177C1169.288,1175.142,866.378,1271.071,719.99,1349.202C573.602,1427.333,583.735,1487.667,588.802,1517.833L593.869,1548"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="Team-cyclic-special-1" d="M1657.272,1330L1658.719,1336.167C1660.166,1342.333,1663.059,1354.667,1664.506,1408.992C1665.953,1463.317,1665.953,1559.633,1665.953,1607.792L1665.953,1655.95"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="Team-cyclic-special-mid" d="M1665.953,1656.05L1665.953,1704.208C1665.953,1752.367,1665.953,1848.683,1659.57,1957C1653.186,2065.317,1640.419,2185.633,1634.035,2245.792L1627.652,2305.95"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="Team-cyclic-special-2" d="M1627.641,2305.95L1621.318,2245.792C1614.995,2185.633,1602.348,2065.317,1596.025,1956.992C1589.701,1848.667,1589.701,1752.333,1589.701,1656C1589.701,1559.667,1589.701,1463.333,1589.701,1409C1589.701,1354.667,1589.701,1342.333,1589.701,1336.167L1589.701,1330"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_User_Team_4" d="M1050.221,426.892L1140.134,475.243C1230.048,523.595,1409.874,620.297,1499.788,674.815C1589.701,729.333,1589.701,741.667,1589.701,747.833L1589.701,754"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Team_Category_5" d="M1707.205,1111.139L1779.679,1153.783C1852.152,1196.426,1997.1,1281.713,2069.573,1330.523C2142.047,1379.333,2142.047,1391.667,2142.047,1397.833L2142.047,1404"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Team_Post_6" d="M1472.197,1137.166L1424.9,1175.472C1377.604,1213.777,1283.01,1290.389,1235.713,1376.861C1188.416,1463.333,1188.416,1559.667,1188.416,1656C1188.416,1752.333,1188.416,1848.667,1186.422,1907C1184.427,1965.333,1180.439,1985.667,1178.445,1995.833L1176.45,2006"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Team_Todo_7" d="M1707.205,1095.712L1806.119,1140.927C1905.033,1186.142,2102.86,1276.571,2201.774,1369.952C2300.688,1463.333,2300.688,1559.667,2300.688,1656C2300.688,1752.333,2300.688,1848.667,2294.001,1912.637C2287.315,1976.607,2273.943,2008.213,2267.257,2024.017L2260.57,2039.82"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="Category-cyclic-special-1" d="M2253.875,1739.425L2299.802,1773.688C2345.73,1807.95,2437.584,1876.475,2483.512,1970.896C2529.439,2065.317,2529.439,2185.633,2529.439,2245.792L2529.439,2305.95"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="Category-cyclic-special-mid" d="M2529.439,2306.05L2529.439,2366.208C2529.439,2426.367,2529.439,2546.683,2523.116,2651C2516.793,2755.317,2504.147,2843.633,2497.824,2887.792L2491.501,2931.95"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="Category-cyclic-special-2" d="M2491.481,2931.95L2479.987,2887.792C2468.492,2843.633,2445.504,2755.317,2434.01,2650.992C2422.516,2546.667,2422.516,2426.333,2422.516,2306C2422.516,2185.667,2422.516,2065.333,2394.409,1976.205C2366.302,1887.077,2310.089,1829.153,2281.982,1800.191L2253.875,1771.23"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Category_Post_9" d="M2030.219,1686.481L1872.146,1729.568C1714.073,1772.654,1397.927,1858.827,1240.863,1912.08C1083.799,1965.333,1085.817,1985.667,1086.825,1995.833L1087.834,2006"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Category_Todo_10" d="M2142.047,1908L2142.047,1914.167C2142.047,1920.333,2142.047,1932.667,2142.148,1945C2142.249,1957.333,2142.45,1969.667,2142.551,1975.833L2142.652,1982"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_User_Post_11" d="M1034.703,680L1037.248,686.167C1039.792,692.333,1044.88,704.667,1047.425,765C1049.969,825.333,1049.969,933.667,1049.969,1042C1049.969,1150.333,1049.969,1258.667,1049.969,1361C1049.969,1463.333,1049.969,1559.667,1049.969,1656C1049.969,1752.333,1049.969,1848.667,1051.874,1907C1053.778,1965.333,1057.588,1985.667,1059.493,1995.833L1061.397,2006"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_User_Todo_12" d="M1050.221,396.31L1207.72,449.758C1365.22,503.207,1680.219,610.103,1837.719,717.718C1995.219,825.333,1995.219,933.667,1995.219,1042C1995.219,1150.333,1995.219,1258.667,1995.219,1361C1995.219,1463.333,1995.219,1559.667,1995.219,1656C1995.219,1752.333,1995.219,1848.667,2001.905,1912.637C2008.591,1976.607,2021.964,2008.213,2028.65,2024.017L2035.336,2039.82"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_User_Todo_13" d="M1050.221,400.776L1193.31,453.48C1336.4,506.184,1622.579,611.592,1765.668,718.463C1908.758,825.333,1908.758,933.667,1908.758,1042C1908.758,1150.333,1908.758,1258.667,1908.758,1361C1908.758,1463.333,1908.758,1559.667,1908.758,1656C1908.758,1752.333,1908.758,1848.667,1929.854,1928.673C1950.951,2008.678,1993.143,2072.357,2014.24,2104.196L2035.336,2136.035"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="Todo-cyclic-special-1" d="M2260.57,2382.237L2330.679,2429.698C2400.787,2477.158,2541.004,2572.079,2611.112,2663.698C2681.22,2755.317,2681.22,2843.633,2681.22,2887.792L2681.22,2931.95"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="Todo-cyclic-special-mid" d="M2681.22,2932.05L2681.22,2976.208C2681.22,3020.367,2681.22,3108.683,2674.904,3159.009C2668.589,3209.334,2655.957,3221.667,2649.641,3227.834L2643.325,3234.001"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="Todo-cyclic-special-2" d="M2643.225,3234.001L2636.909,3227.834C2630.593,3221.667,2617.961,3209.334,2611.646,3159C2605.33,3108.667,2605.33,3020.333,2605.33,2932C2605.33,2843.667,2605.33,2755.333,2547.87,2665.814C2490.41,2576.296,2375.49,2485.591,2318.03,2440.239L2260.57,2394.887"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Team_Conversation_15" d="M1472.197,1101.098L1384.082,1145.415C1295.967,1189.732,1119.736,1278.366,1029.59,1334.85C939.444,1391.333,935.383,1415.667,933.352,1427.833L931.322,1440"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_User_Conversation_16" d="M816.834,680L815.379,686.167C813.925,692.333,811.016,704.667,809.562,765C808.107,825.333,808.107,933.667,808.107,1042C808.107,1150.333,808.107,1258.667,811.777,1325C815.446,1391.333,822.785,1415.667,826.455,1427.833L830.124,1440"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Conversation_Message_17" d="M895.27,1872L895.27,1884.167C895.27,1896.333,895.27,1920.667,878.185,1961.398C861.101,2002.129,826.932,2059.258,809.848,2087.823L792.764,2116.387"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_User_Message_18" d="M741.932,476.898L695.517,516.915C649.103,556.932,556.274,636.966,509.86,731.15C463.445,825.333,463.445,933.667,463.445,1042C463.445,1150.333,463.445,1258.667,463.445,1361C463.445,1463.333,463.445,1559.667,463.445,1656C463.445,1752.333,463.445,1848.667,480.53,1925.398C497.614,2002.129,531.783,2059.258,548.867,2087.823L565.951,2116.387"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Tag_Taggable_19" d="M229.469,2426L229.469,2466.167C229.469,2506.333,229.469,2586.667,219.277,2657C209.086,2727.333,188.703,2787.667,178.511,2817.833L168.32,2848"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Post_Taggable_20" d="M991.041,2353.476L851.741,2405.73C712.441,2457.984,433.842,2562.492,292.8,2644.913C151.759,2727.333,148.275,2787.667,146.533,2817.833L144.791,2848"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Todo_Taggable_21" d="M2035.336,2325.382L1704.516,2382.318C1373.695,2439.255,712.055,2553.127,391.426,2640.23C70.797,2727.333,91.18,2787.667,101.371,2817.833L111.563,2848"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Post_Comment_22" d="M1117.604,2606L1117.604,2616.167C1117.604,2626.333,1117.604,2646.667,1077.779,2685.553C1037.955,2724.439,958.307,2781.878,918.482,2810.598L878.658,2839.317"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Todo_Comment_23" d="M2035.336,2334.619L1817.35,2390.016C1599.365,2445.413,1163.393,2556.206,946.451,2623.77C729.508,2691.333,731.594,2715.667,732.637,2727.833L733.68,2740"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_User_Comment_24" d="M741.932,452.439L679.254,496.533C616.576,540.626,491.219,628.813,428.541,727.073C365.863,825.333,365.863,933.667,365.863,1042C365.863,1150.333,365.863,1258.667,365.863,1361C365.863,1463.333,365.863,1559.667,365.863,1656C365.863,1752.333,365.863,1848.667,365.863,1957C365.863,2065.333,365.863,2185.667,365.863,2306C365.863,2426.333,365.863,2546.667,408.489,2636.229C451.115,2725.791,536.367,2784.581,578.993,2813.976L621.619,2843.372"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Post_Media_25" d="M1244.166,2371.388L1339.529,2420.656C1434.892,2469.925,1625.618,2568.463,1719.626,2623.898C1813.634,2679.333,1810.923,2691.667,1809.568,2697.833L1808.213,2704"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_User_Media_26" d="M1050.221,411.952L1165.551,462.793C1280.882,513.634,1511.544,615.317,1626.874,720.325C1742.205,825.333,1742.205,933.667,1742.205,1042C1742.205,1150.333,1742.205,1258.667,1742.205,1361C1742.205,1463.333,1742.205,1559.667,1742.205,1656C1742.205,1752.333,1742.205,1848.667,1742.205,1957C1742.205,2065.333,1742.205,2185.667,1742.205,2306C1742.205,2426.333,1742.205,2546.667,1742.575,2613C1742.945,2679.333,1743.686,2691.667,1744.056,2697.833L1744.426,2704"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_User_Activity_27" d="M1050.221,382.167L1275.603,437.972C1500.986,493.778,1951.751,605.389,2177.133,715.361C2402.516,825.333,2402.516,933.667,2402.516,1042C2402.516,1150.333,2402.516,1258.667,2402.516,1361C2402.516,1463.333,2402.516,1559.667,2402.516,1656C2402.516,1752.333,2402.516,1848.667,2402.516,1957C2402.516,2065.333,2402.516,2185.667,2402.516,2306C2402.516,2426.333,2402.516,2546.667,2387.794,2626.713C2373.073,2706.76,2343.63,2746.52,2328.909,2766.4L2314.188,2786.281"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Post_Activity_28" d="M1244.166,2343.183L1427.865,2397.153C1611.564,2451.122,1978.962,2559.061,2155.172,2627.197C2331.382,2695.333,2316.406,2723.667,2308.917,2737.833L2301.429,2752"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Todo_Activity_29" d="M2147.953,2630L2147.953,2636.167C2147.953,2642.333,2147.953,2654.667,2151.071,2675C2154.189,2695.333,2160.426,2723.667,2163.544,2737.833L2166.662,2752"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Team_Activity_30" d="M1707.205,1189.758L1730.697,1219.298C1754.189,1248.839,1801.173,1307.919,1824.664,1385.626C1848.156,1463.333,1848.156,1559.667,1848.156,1656C1848.156,1752.333,1848.156,1848.667,1848.156,1957C1848.156,2065.333,1848.156,2185.667,1848.156,2306C1848.156,2426.333,1848.156,2546.667,1889.859,2637.692C1931.563,2728.718,2014.969,2790.435,2056.672,2821.294L2098.375,2852.153"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Role_Permission_31" d="M1409.479,1788L1409.479,1814.167C1409.479,1840.333,1409.479,1892.667,1409.479,1961C1409.479,2029.333,1409.479,2113.667,1409.479,2155.833L1409.479,2198"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_User_Role_32" d="M1050.221,452.625L1112.748,496.687C1175.275,540.75,1300.33,628.875,1362.857,727.104C1425.385,825.333,1425.385,933.667,1425.385,1042C1425.385,1150.333,1425.385,1258.667,1423.945,1339C1422.504,1419.333,1419.624,1471.667,1418.184,1497.833L1416.744,1524"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Team_Role_33" d="M1472.197,1165.089L1440.072,1198.741C1407.947,1232.393,1343.697,1299.696,1323.346,1359.515C1302.994,1419.333,1326.54,1471.667,1338.314,1497.833L1350.087,1524"/></g><g class="edgeLabels"><g transform="translate(699.169921875, 1042)" class="edgeLabel"><g transform="translate(-36.9765625, -12)" class="label"><foreignObject height="24" width="73.953125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>belongs to</p></span></div></foreignObject></g></g><g transform="translate(563.46875, 1367)" class="edgeLabel"><g transform="translate(-11.8125, -12)" class="label"><foreignObject height="24" width="23.625"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>has</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g transform="translate(1665.953125, 1945)" class="edgeLabel"><g transform="translate(-55.890625, -12)" class="label"><foreignObject height="24" width="111.78125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>parent-children</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g transform="translate(1589.701171875, 717)" class="edgeLabel"><g transform="translate(-17.859375, -12)" class="label"><foreignObject height="24" width="35.71875"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>owns</p></span></div></foreignObject></g></g><g transform="translate(2142.046875, 1367)" class="edgeLabel"><g transform="translate(-11.8125, -12)" class="label"><foreignObject height="24" width="23.625"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>has</p></span></div></foreignObject></g></g><g transform="translate(1188.416015625, 1656)" class="edgeLabel"><g transform="translate(-11.8125, -12)" class="label"><foreignObject height="24" width="23.625"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>has</p></span></div></foreignObject></g></g><g transform="translate(2300.6875, 1656)" class="edgeLabel"><g transform="translate(-11.8125, -12)" class="label"><foreignObject height="24" width="23.625"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>has</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g transform="translate(2529.4390625003725, 2667)" class="edgeLabel"><g transform="translate(-55.890625, -12)" class="label"><foreignObject height="24" width="111.78125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>parent-children</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g transform="translate(1081.78125, 1945)" class="edgeLabel"><g transform="translate(-11.8125, -12)" class="label"><foreignObject height="24" width="23.625"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>has</p></span></div></foreignObject></g></g><g transform="translate(2142.046875, 1945)" class="edgeLabel"><g transform="translate(-11.8125, -12)" class="label"><foreignObject height="24" width="23.625"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>has</p></span></div></foreignObject></g></g><g transform="translate(1049.96875, 1367)" class="edgeLabel"><g transform="translate(-26.4140625, -12)" class="label"><foreignObject height="24" width="52.828125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>creates</p></span></div></foreignObject></g></g><g transform="translate(1995.21875, 1367)" class="edgeLabel"><g transform="translate(-26.4140625, -12)" class="label"><foreignObject height="24" width="52.828125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>creates</p></span></div></foreignObject></g></g><g transform="translate(1908.7578125, 1367)" class="edgeLabel"><g transform="translate(-40.046875, -12)" class="label"><foreignObject height="24" width="80.09375"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>assigned to</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g transform="translate(2681.2203125003725, 3197)" class="edgeLabel"><g transform="translate(-55.890625, -12)" class="label"><foreignObject height="24" width="111.78125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>parent-children</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g transform="translate(943.505859375, 1367)" class="edgeLabel"><g transform="translate(-11.8125, -12)" class="label"><foreignObject height="24" width="23.625"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>has</p></span></div></foreignObject></g></g><g transform="translate(808.107421875, 1042)" class="edgeLabel"><g transform="translate(-51.9609375, -12)" class="label"><foreignObject height="24" width="103.921875"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>participates in</p></span></div></foreignObject></g></g><g transform="translate(895.26953125, 1945)" class="edgeLabel"><g transform="translate(-11.8125, -12)" class="label"><foreignObject height="24" width="23.625"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>has</p></span></div></foreignObject></g></g><g transform="translate(463.4453125, 1367)" class="edgeLabel"><g transform="translate(-19.671875, -12)" class="label"><foreignObject height="24" width="39.34375"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>sends</p></span></div></foreignObject></g></g><g transform="translate(229.46875, 2667)" class="edgeLabel"><g transform="translate(-11.8125, -12)" class="label"><foreignObject height="24" width="23.625"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>has</p></span></div></foreignObject></g></g><g transform="translate(155.2421875, 2667)" class="edgeLabel"><g transform="translate(-42.4140625, -12)" class="label"><foreignObject height="24" width="84.828125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>tagged with</p></span></div></foreignObject></g></g><g transform="translate(50.4140625, 2667)" class="edgeLabel"><g transform="translate(-42.4140625, -12)" class="label"><foreignObject height="24" width="84.828125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>tagged with</p></span></div></foreignObject></g></g><g transform="translate(1117.603515625, 2667)" class="edgeLabel"><g transform="translate(-11.8125, -12)" class="label"><foreignObject height="24" width="23.625"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>has</p></span></div></foreignObject></g></g><g transform="translate(727.421875, 2667)" class="edgeLabel"><g transform="translate(-11.8125, -12)" class="label"><foreignObject height="24" width="23.625"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>has</p></span></div></foreignObject></g></g><g transform="translate(365.86328125, 1656)" class="edgeLabel"><g transform="translate(-26.7578125, -12)" class="label"><foreignObject height="24" width="53.515625"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>authors</p></span></div></foreignObject></g></g><g transform="translate(1816.34375, 2667)" class="edgeLabel"><g transform="translate(-11.8125, -12)" class="label"><foreignObject height="24" width="23.625"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>has</p></span></div></foreignObject></g></g><g transform="translate(1742.205078125, 1656)" class="edgeLabel"><g transform="translate(-27.3828125, -12)" class="label"><foreignObject height="24" width="54.765625"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>uploads</p></span></div></foreignObject></g></g><g transform="translate(2402.515625, 1656)" class="edgeLabel"><g transform="translate(-23.375, -12)" class="label"><foreignObject height="24" width="46.75"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>causes</p></span></div></foreignObject></g></g><g transform="translate(2346.359375, 2667)" class="edgeLabel"><g transform="translate(-36.15625, -12)" class="label"><foreignObject height="24" width="72.3125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>subject of</p></span></div></foreignObject></g></g><g transform="translate(2147.953125, 2667)" class="edgeLabel"><g transform="translate(-36.15625, -12)" class="label"><foreignObject height="24" width="72.3125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>subject of</p></span></div></foreignObject></g></g><g transform="translate(1848.15625, 1945)" class="edgeLabel"><g transform="translate(-36.15625, -12)" class="label"><foreignObject height="24" width="72.3125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>subject of</p></span></div></foreignObject></g></g><g transform="translate(1409.478515625, 1945)" class="edgeLabel"><g transform="translate(-11.8125, -12)" class="label"><foreignObject height="24" width="23.625"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>has</p></span></div></foreignObject></g></g><g transform="translate(1425.384765625, 1042)" class="edgeLabel"><g transform="translate(-11.8125, -12)" class="label"><foreignObject height="24" width="23.625"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>has</p></span></div></foreignObject></g></g><g transform="translate(1279.447265625, 1367)" class="edgeLabel"><g transform="translate(-11.8125, -12)" class="label"><foreignObject height="24" width="23.625"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>has</p></span></div></foreignObject></g></g><g transform="translate(720.4967915910537, 644.4696952642657)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1450.9851896149364, 1070.1960585010474)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1646.666017145018, 1350.4636317941595)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 36px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">0..1</span></div></foreignObject></g></g><g transform="translate(1650.9531275000002, 1673.55000214323)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 36px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">0..1</span></div></foreignObject></g></g><g transform="translate(1640.7296714495903, 2286.977844812604)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 36px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">0..1</span></div></foreignObject></g></g><g transform="translate(1058.5292350343163, 448.3911843879544)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1714.6809642081164, 1132.9420315870054)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1449.1573605568217, 1136.5236672503595)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1716.8850255677016, 1116.6300446538567)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(2258.9324909034303, 1761.9124935220343)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 36px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">0..1</span></div></foreignObject></g></g><g transform="translate(2514.4390612501866, 2323.5499989287846)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 36px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">0..1</span></div></foreignObject></g></g><g transform="translate(2501.588762591457, 2911.235809340489)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 36px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">0..1</span></div></foreignObject></g></g><g transform="translate(2009.3900377085552, 1676.6114625040232)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(2127.0468775, 1925.500002142857)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1027.5114887097407, 701.8981352033401)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1061.9721418113868, 416.13788533773646)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1061.4577582188754, 420.8999767577031)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(2266.6531846417656, 2404.4689173193797)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 36px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">0..1</span></div></foreignObject></g></g><g transform="translate(2666.2203112501866, 2949.5499989287846)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 36px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">0..1</span></div></foreignObject></g></g><g transform="translate(2641.1830259139742, 3211.04294885487)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 36px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">0..1</span></div></foreignObject></g></g><g transform="translate(1449.8234965481201, 1095.5603219927877)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(798.2170524616802, 693.5895622226744)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g></g><g transform="translate(880.269530625, 1889.4999994642858)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(718.8828759319703, 476.9649024799282)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(214.46875, 2443.5)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(969.3875824677717, 2345.577974285232)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(2015.545307701745, 2313.5677052587193)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1102.6035178125, 2623.500001875)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(2014.6805354501996, 2324.3918085897744)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(718.9879391508119, 450.2400666870735)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1252.8285507866806, 2392.746849640598)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1060.1831727982974, 432.7363049354012)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1063.60254502069, 400.9331191283814)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1256.7281924256101, 2362.507831342992)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(2132.9531275, 2647.5000021428573)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1706.3572869848792, 1212.7911262452708)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1394.4785178125, 1805.500001875)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g></g><g transform="translate(1055.8851391988176, 474.9665365654731)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g></g><g transform="translate(1449.2635086902974, 1167.3893651269898)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(658.9947252410584, 1530.5767121030804)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(600.7628346698764, 1523.257194925767)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(1675.9531274999997, 1633.4500021424847)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(1639.4146522590888, 2285.1305042957947)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(1569.7011709375001, 1342.4999991964287)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(1599.7011709375001, 731.4999991964286)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(2152.0468775, 1381.500002142857)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(1189.5385031462638, 1986.714590254825)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(2276.203609534439, 2024.5480564830252)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(2539.4390612501866, 2283.4499989280393)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(2503.8299799611254, 2911.752850042932)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(2250.2983269729134, 1789.2345671118871)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(1096.0329603830044, 1982.1043441761517)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(2152.363940173041, 1959.2569594285508)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(1067.9181927902173, 1981.0370348320312)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(2037.3315570344391, 2012.8586181205553)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(2033.1741165792007, 2108.161660853115)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(2691.2203112501866, 2909.4499989280393)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(2661.3254359148787, 3227.507870885572)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(2260.0137379299676, 2412.5034394451163)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(943.997958268253, 1420.2082355188734)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(834.4320484324659, 1413.9141508992761)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(809.6194988043475, 2104.0680581170286)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(564.841748096377, 2088.6692715332956)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(183.13198108375633, 2831.2215952313863)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(155.77527175101088, 2826.3937407389094)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(115.17267698528224, 2821.619583806945)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(896.6261023947261, 2836.247373377047)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(742.1302124163445, 2716.2827914327636)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(610.7282243630542, 2816.0883594201873)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(1821.6195584140046, 2685.1271724226917)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(1753.3504769596518, 2680.632703948337)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(2331.656565138158, 2776.143366995564)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(2317.8681240672913, 2738.5383707076257)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(2172.5496733636364, 2726.684693390549)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(2088.2299025171274, 2824.6858754999694)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(1419.4785178124996, 2175.500001875)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(1427.6827137721486, 1502.3507832108073)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(1351.58571498325, 1496.8862539738807)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g></g><g class="nodes"><g transform="translate(896.076171875, 344)" id="classId-User-0" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-154.14453125 -336 L154.14453125 -336 L154.14453125 336 L-154.14453125 336"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-154.14453125 -336 C-38.03619816159937 -336, 78.07213492680125 -336, 154.14453125 -336 M-154.14453125 -336 C-75.82758933116342 -336, 2.489352587673153 -336, 154.14453125 -336 M154.14453125 -336 C154.14453125 -177.96841702804437, 154.14453125 -19.936834056088742, 154.14453125 336 M154.14453125 -336 C154.14453125 -158.00175233563857, 154.14453125 19.996495328722858, 154.14453125 336 M154.14453125 336 C38.61493235232432 336, -76.91466654535137 336, -154.14453125 336 M154.14453125 336 C89.75941159236392 336, 25.374291934727836 336, -154.14453125 336 M-154.14453125 336 C-154.14453125 184.39216346771371, -154.14453125 32.78432693542743, -154.14453125 -336 M-154.14453125 336 C-154.14453125 76.20385108886666, -154.14453125 -183.5922978222667, -154.14453125 -336"/></g><g transform="translate(0, -312)" class="annotation-group text"/><g transform="translate(-16.8828125, -312)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="33.765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 80px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>User</p></span></div></foreignObject></g></g><g transform="translate(-142.14453125, -264)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="72.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+id: string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="98.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 143px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+name: string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="99.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 144px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+email: string</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="211.078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 240px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+email_verified_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="125.359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 169px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+password: string</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="182.421875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 217px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+remember_token: string</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="190.890625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 223px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+two_factor_secret: string</p></span></div></foreignObject></g><g transform="translate(0,156)" style="" class="label"><foreignObject height="24" width="257.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 286px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+two_factor_recovery_codes: string</p></span></div></foreignObject></g><g transform="translate(0,180)" style="" class="label"><foreignObject height="24" width="267.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 290px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+two_factor_confirmed_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,204)" style="" class="label"><foreignObject height="24" width="179.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 213px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+current_team_id: string</p></span></div></foreignObject></g><g transform="translate(0,228)" style="" class="label"><foreignObject height="24" width="197.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 231px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+profile_photo_path: string</p></span></div></foreignObject></g><g transform="translate(0,252)" style="" class="label"><foreignObject height="24" width="162.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 193px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+created_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,276)" style="" class="label"><foreignObject height="24" width="166.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 197px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+updated_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,300)" style="" class="label"><foreignObject height="24" width="162.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 193px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+deleted_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,324)" style="" class="label"><foreignObject height="24" width="139.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 179px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+created_by: string</p></span></div></foreignObject></g><g transform="translate(0,348)" style="" class="label"><foreignObject height="24" width="143.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 183px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+updated_by: string</p></span></div></foreignObject></g><g transform="translate(0,372)" style="" class="label"><foreignObject height="24" width="139.734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 179px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+deleted_by: string</p></span></div></foreignObject></g><g transform="translate(0,396)" style="" class="label"><foreignObject height="24" width="137.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 178px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+status: UserStatus</p></span></div></foreignObject></g><g transform="translate(0,420)" style="" class="label"><foreignObject height="24" width="191.265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 224px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+presence: PresenceStatus</p></span></div></foreignObject></g></g><g transform="translate(-142.14453125, 216)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="87.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 135px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getTeams()</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="136.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 181px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getOwnedTeams()</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="135.515625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 178px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getCurrentTeam()</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="139.296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 185px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+hasVerifiedEmail()</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="167.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 215px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+markEmailAsVerified()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-154.14453125 -288 C-83.31830628195432 -288, -12.492081313908642 -288, 154.14453125 -288 M-154.14453125 -288 C-40.89529750319112 -288, 72.35393624361777 -288, 154.14453125 -288"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-154.14453125 192 C-78.78558495895571 192, -3.426638667911419 192, 154.14453125 192 M-154.14453125 192 C-34.07415576898005 192, 85.9962197120399 192, 154.14453125 192"/></g></g><g transform="translate(1589.701171875, 1042)" id="classId-Team-1" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-117.50390625 -288 L117.50390625 -288 L117.50390625 288 L-117.50390625 288"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-117.50390625 -288 C-59.096102664112244 -288, -0.6882990782244889 -288, 117.50390625 -288 M-117.50390625 -288 C-38.415011102887405 -288, 40.67388404422519 -288, 117.50390625 -288 M117.50390625 -288 C117.50390625 -87.23090275085008, 117.50390625 113.53819449829984, 117.50390625 288 M117.50390625 -288 C117.50390625 -141.91390952823735, 117.50390625 4.172180943525291, 117.50390625 288 M117.50390625 288 C44.69828915726043 288, -28.107327935479134 288, -117.50390625 288 M117.50390625 288 C27.527224213479585 288, -62.44945782304083 288, -117.50390625 288 M-117.50390625 288 C-117.50390625 161.23064787407475, -117.50390625 34.461295748149524, -117.50390625 -288 M-117.50390625 288 C-117.50390625 61.9112233082333, -117.50390625 -164.1775533835334, -117.50390625 -288"/></g><g transform="translate(0, -264)" class="annotation-group text"/><g transform="translate(-19.7421875, -264)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="39.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 85px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Team</p></span></div></foreignObject></g></g><g transform="translate(-105.50390625, -216)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="72.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+id: string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="98.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 143px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+name: string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="87.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 135px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+slug: string</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="139.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 179px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+description: string</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="181.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 216px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+personal_team: boolean</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="128.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 169px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+parent_id: string</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="162.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 193px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+created_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,156)" style="" class="label"><foreignObject height="24" width="166.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 197px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+updated_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,180)" style="" class="label"><foreignObject height="24" width="162.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 193px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+deleted_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,204)" style="" class="label"><foreignObject height="24" width="139.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 179px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+created_by: string</p></span></div></foreignObject></g><g transform="translate(0,228)" style="" class="label"><foreignObject height="24" width="143.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 183px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+updated_by: string</p></span></div></foreignObject></g><g transform="translate(0,252)" style="" class="label"><foreignObject height="24" width="139.734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 179px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+deleted_by: string</p></span></div></foreignObject></g><g transform="translate(0,276)" style="" class="label"><foreignObject height="24" width="143.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 183px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+status: TeamStatus</p></span></div></foreignObject></g><g transform="translate(0,300)" style="" class="label"><foreignObject height="24" width="191.265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 224px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+presence: PresenceStatus</p></span></div></foreignObject></g></g><g transform="translate(-105.50390625, 144)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="89.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 137px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getOwner()</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="81.515625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 130px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getUsers()</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="89.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 135px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getParent()</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="103.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 150px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getChildren()</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="78.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 127px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+addUser()</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="105.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 152px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+removeUser()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-117.50390625 -240 C-62.40296656305123 -240, -7.30202687610246 -240, 117.50390625 -240 M-117.50390625 -240 C-63.21997100795257 -240, -8.936035765905146 -240, 117.50390625 -240"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-117.50390625 120 C-62.70729707755205 120, -7.910687905104098 120, 117.50390625 120 M-117.50390625 120 C-35.21990466807753 120, 47.064096913844935 120, 117.50390625 120"/></g></g><g transform="translate(2142.046875, 1656)" id="classId-Category-2" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-111.828125 -252 L111.828125 -252 L111.828125 252 L-111.828125 252"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-111.828125 -252 C-37.00747338870758 -252, 37.81317822258484 -252, 111.828125 -252 M-111.828125 -252 C-35.0862093176099 -252, 41.6557063647802 -252, 111.828125 -252 M111.828125 -252 C111.828125 -100.47971332396969, 111.828125 51.04057335206062, 111.828125 252 M111.828125 -252 C111.828125 -88.56324789885312, 111.828125 74.87350420229376, 111.828125 252 M111.828125 252 C56.08101872152056 252, 0.33391244304111467 252, -111.828125 252 M111.828125 252 C33.75615055431257 252, -44.31582389137486 252, -111.828125 252 M-111.828125 252 C-111.828125 97.84707541848408, -111.828125 -56.30584916303184, -111.828125 -252 M-111.828125 252 C-111.828125 119.54784249803097, -111.828125 -12.904315003938052, -111.828125 -252"/></g><g transform="translate(0, -228)" class="annotation-group text"/><g transform="translate(-33.15625, -228)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="66.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 109px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Category</p></span></div></foreignObject></g></g><g transform="translate(-99.828125, -180)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="72.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+id: string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="98.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 143px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+name: string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="87.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 135px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+slug: string</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="139.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 179px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+description: string</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="128.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 169px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+parent_id: string</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="118.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 160px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+team_id: string</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="162.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 193px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+created_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,156)" style="" class="label"><foreignObject height="24" width="166.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 197px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+updated_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,180)" style="" class="label"><foreignObject height="24" width="162.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 193px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+deleted_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,204)" style="" class="label"><foreignObject height="24" width="139.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 179px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+created_by: string</p></span></div></foreignObject></g><g transform="translate(0,228)" style="" class="label"><foreignObject height="24" width="143.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 183px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+updated_by: string</p></span></div></foreignObject></g><g transform="translate(0,252)" style="" class="label"><foreignObject height="24" width="139.734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 179px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+deleted_by: string</p></span></div></foreignObject></g></g><g transform="translate(-99.828125, 132)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="80.953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 129px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getTeam()</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="89.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 135px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getParent()</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="103.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 150px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getChildren()</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="79.296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 127px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getPosts()</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="83.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 133px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getTodos()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-111.828125 -204 C-62.43480230991572 -204, -13.041479619831435 -204, 111.828125 -204 M-111.828125 -204 C-40.273473959678384 -204, 31.281177080643232 -204, 111.828125 -204"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-111.828125 108 C-39.06402600734296 108, 33.70007298531408 108, 111.828125 108 M-111.828125 108 C-34.47694679437289 108, 42.874231411254215 108, 111.828125 108"/></g></g><g transform="translate(1117.603515625, 2306)" id="classId-Post-3" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-126.5625 -300 L126.5625 -300 L126.5625 300 L-126.5625 300"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.5625 -300 C-67.40803760758237 -300, -8.253575215164759 -300, 126.5625 -300 M-126.5625 -300 C-30.75851158575462 -300, 65.04547682849076 -300, 126.5625 -300 M126.5625 -300 C126.5625 -86.5807321125981, 126.5625 126.8385357748038, 126.5625 300 M126.5625 -300 C126.5625 -90.7891047108096, 126.5625 118.42179057838081, 126.5625 300 M126.5625 300 C57.83345883878543 300, -10.895582322429135 300, -126.5625 300 M126.5625 300 C32.32658895202552 300, -61.909322095948966 300, -126.5625 300 M-126.5625 300 C-126.5625 156.82832711588364, -126.5625 13.656654231767277, -126.5625 -300 M-126.5625 300 C-126.5625 119.63701131258168, -126.5625 -60.725977374836646, -126.5625 -300"/></g><g transform="translate(0, -276)" class="annotation-group text"/><g transform="translate(-15.46875, -276)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="30.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 78px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Post</p></span></div></foreignObject></g></g><g transform="translate(-114.5625, -228)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="72.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+id: string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="90.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 133px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+title: string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="87.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 135px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+slug: string</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="114.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 155px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+content: string</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="114.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 155px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+excerpt: string</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="213.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 243px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+featured_image_path: string</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="176.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 209px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+published_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,156)" style="" class="label"><foreignObject height="24" width="118.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 160px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+team_id: string</p></span></div></foreignObject></g><g transform="translate(0,180)" style="" class="label"><foreignObject height="24" width="143.453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 184px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+category_id: string</p></span></div></foreignObject></g><g transform="translate(0,204)" style="" class="label"><foreignObject height="24" width="162.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 193px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+created_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,228)" style="" class="label"><foreignObject height="24" width="166.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 197px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+updated_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,252)" style="" class="label"><foreignObject height="24" width="162.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 193px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+deleted_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,276)" style="" class="label"><foreignObject height="24" width="139.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 179px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+created_by: string</p></span></div></foreignObject></g><g transform="translate(0,300)" style="" class="label"><foreignObject height="24" width="143.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 183px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+updated_by: string</p></span></div></foreignObject></g><g transform="translate(0,324)" style="" class="label"><foreignObject height="24" width="139.734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 179px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+deleted_by: string</p></span></div></foreignObject></g><g transform="translate(0,348)" style="" class="label"><foreignObject height="24" width="135.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 175px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+status: PostStatus</p></span></div></foreignObject></g></g><g transform="translate(-114.5625, 180)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="80.953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 129px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getTeam()</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="107.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 152px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getCategory()</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="91.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 139px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getAuthor()</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="73.453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 124px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getTags()</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="118.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 163px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getComments()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.5625 -252 C-64.97089750501515 -252, -3.3792950100302903 -252, 126.5625 -252 M-126.5625 -252 C-70.27570784149958 -252, -13.988915682999178 -252, 126.5625 -252"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-126.5625 156 C-54.50997516144962 156, 17.542549677100766 156, 126.5625 156 M-126.5625 156 C-73.17934969783101 156, -19.79619939566203 156, 126.5625 156"/></g></g><g transform="translate(2147.953125, 2306)" id="classId-Todo-4" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-112.6171875 -324 L112.6171875 -324 L112.6171875 324 L-112.6171875 324"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-112.6171875 -324 C-62.97230896038192 -324, -13.327430420763847 -324, 112.6171875 -324 M-112.6171875 -324 C-64.49063387998547 -324, -16.36408025997092 -324, 112.6171875 -324 M112.6171875 -324 C112.6171875 -101.13193346730552, 112.6171875 121.73613306538897, 112.6171875 324 M112.6171875 -324 C112.6171875 -65.5266794275891, 112.6171875 192.9466411448218, 112.6171875 324 M112.6171875 324 C46.58599005904466 324, -19.445207381910677 324, -112.6171875 324 M112.6171875 324 C45.24456988407465 324, -22.1280477318507 324, -112.6171875 324 M-112.6171875 324 C-112.6171875 66.32845779104298, -112.6171875 -191.34308441791404, -112.6171875 -324 M-112.6171875 324 C-112.6171875 81.40671391409842, -112.6171875 -161.18657217180316, -112.6171875 -324"/></g><g transform="translate(0, -300)" class="annotation-group text"/><g transform="translate(-17.5625, -300)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="35.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 83px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Todo</p></span></div></foreignObject></g></g><g transform="translate(-100.6171875, -252)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="72.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+id: string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="90.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 133px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+title: string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="139.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 179px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+description: string</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="151.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 185px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+due_date: datetime</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="183.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 213px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+completed_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="128.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 169px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+parent_id: string</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="118.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 160px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+team_id: string</p></span></div></foreignObject></g><g transform="translate(0,156)" style="" class="label"><foreignObject height="24" width="143.453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 184px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+category_id: string</p></span></div></foreignObject></g><g transform="translate(0,180)" style="" class="label"><foreignObject height="24" width="141.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 183px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+assignee_id: string</p></span></div></foreignObject></g><g transform="translate(0,204)" style="" class="label"><foreignObject height="24" width="162.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 193px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+created_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,228)" style="" class="label"><foreignObject height="24" width="166.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 197px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+updated_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,252)" style="" class="label"><foreignObject height="24" width="162.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 193px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+deleted_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,276)" style="" class="label"><foreignObject height="24" width="139.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 179px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+created_by: string</p></span></div></foreignObject></g><g transform="translate(0,300)" style="" class="label"><foreignObject height="24" width="143.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 183px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+updated_by: string</p></span></div></foreignObject></g><g transform="translate(0,324)" style="" class="label"><foreignObject height="24" width="139.734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 179px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+deleted_by: string</p></span></div></foreignObject></g><g transform="translate(0,348)" style="" class="label"><foreignObject height="24" width="138.984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 180px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+status: TodoStatus</p></span></div></foreignObject></g><g transform="translate(0,372)" style="" class="label"><foreignObject height="24" width="124.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 169px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+priority: Priority</p></span></div></foreignObject></g></g><g transform="translate(-100.6171875, 180)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="80.953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 129px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getTeam()</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="107.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 152px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getCategory()</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="104.421875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 152px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getAssignee()</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="97.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 142px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getCreator()</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="89.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 135px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getParent()</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="103.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 150px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getChildren()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-112.6171875 -276 C-33.19543313127171 -276, 46.226321237456574 -276, 112.6171875 -276 M-112.6171875 -276 C-29.592529576303946 -276, 53.43212834739211 -276, 112.6171875 -276"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-112.6171875 156 C-53.06293498643316 156, 6.491317527133674 156, 112.6171875 156 M-112.6171875 156 C-49.696120678138705 156, 13.22494614372259 156, 112.6171875 156"/></g></g><g transform="translate(895.26953125, 1656)" id="classId-Conversation-5" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-119.69921875 -216 L119.69921875 -216 L119.69921875 216 L-119.69921875 216"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-119.69921875 -216 C-60.809279553112674 -216, -1.919340356225348 -216, 119.69921875 -216 M-119.69921875 -216 C-29.005194801498433 -216, 61.68882914700313 -216, 119.69921875 -216 M119.69921875 -216 C119.69921875 -116.14627462798636, 119.69921875 -16.292549255972716, 119.69921875 216 M119.69921875 -216 C119.69921875 -81.59612382593471, 119.69921875 52.80775234813058, 119.69921875 216 M119.69921875 216 C24.54100956416876 216, -70.61719962166248 216, -119.69921875 216 M119.69921875 216 C62.56962537098737 216, 5.440031991974735 216, -119.69921875 216 M-119.69921875 216 C-119.69921875 109.33545694486372, -119.69921875 2.6709138897274443, -119.69921875 -216 M-119.69921875 216 C-119.69921875 103.33268239044588, -119.69921875 -9.334635219108236, -119.69921875 -216"/></g><g transform="translate(0, -192)" class="annotation-group text"/><g transform="translate(-48.8984375, -192)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="97.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 135px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Conversation</p></span></div></foreignObject></g></g><g transform="translate(-107.69921875, -144)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="72.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+id: string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="90.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 133px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+title: string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="118.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 160px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+team_id: string</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="162.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 193px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+created_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="166.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 197px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+updated_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="162.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 193px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+deleted_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="139.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 179px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+created_by: string</p></span></div></foreignObject></g><g transform="translate(0,156)" style="" class="label"><foreignObject height="24" width="143.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 183px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+updated_by: string</p></span></div></foreignObject></g><g transform="translate(0,180)" style="" class="label"><foreignObject height="24" width="139.734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 179px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+deleted_by: string</p></span></div></foreignObject></g></g><g transform="translate(-107.69921875, 96)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="80.953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 129px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getTeam()</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="128.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 169px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getParticipants()</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="107.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 156px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getMessages()</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="124.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 167px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+addParticipant()</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="152.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 191px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+removeParticipant()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-119.69921875 -168 C-66.49332345232017 -168, -13.287428154640352 -168, 119.69921875 -168 M-119.69921875 -168 C-69.51959292373206 -168, -19.33996709746414 -168, 119.69921875 -168"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-119.69921875 72 C-38.10999960100379 72, 43.479219547992415 72, 119.69921875 72 M-119.69921875 72 C-24.123593737757105 72, 71.45203127448579 72, 119.69921875 72"/></g></g><g transform="translate(679.357421875, 2306)" id="classId-Message-6" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-113.40625 -192 L113.40625 -192 L113.40625 192 L-113.40625 192"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-113.40625 -192 C-59.600838439415234 -192, -5.795426878830469 -192, 113.40625 -192 M-113.40625 -192 C-56.41088957352811 -192, 0.584470852943781 -192, 113.40625 -192 M113.40625 -192 C113.40625 -76.871713798168, 113.40625 38.256572403663995, 113.40625 192 M113.40625 -192 C113.40625 -113.51462686184342, 113.40625 -35.02925372368685, 113.40625 192 M113.40625 192 C60.82996016704411 192, 8.253670334088227 192, -113.40625 192 M113.40625 192 C41.3300113382289 192, -30.7462273235422 192, -113.40625 192 M-113.40625 192 C-113.40625 96.16719435473992, -113.40625 0.334388709479839, -113.40625 -192 M-113.40625 192 C-113.40625 57.460683922057854, -113.40625 -77.07863215588429, -113.40625 -192"/></g><g transform="translate(0, -168)" class="annotation-group text"/><g transform="translate(-30.328125, -168)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="60.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 106px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Message</p></span></div></foreignObject></g></g><g transform="translate(-101.40625, -120)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="72.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+id: string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="114.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 155px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+content: string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="172.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 211px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+conversation_id: string</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="162.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 193px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+created_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="166.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 197px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+updated_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="162.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 193px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+deleted_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="139.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 179px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+created_by: string</p></span></div></foreignObject></g><g transform="translate(0,156)" style="" class="label"><foreignObject height="24" width="143.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 183px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+updated_by: string</p></span></div></foreignObject></g><g transform="translate(0,180)" style="" class="label"><foreignObject height="24" width="139.734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 179px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+deleted_by: string</p></span></div></foreignObject></g></g><g transform="translate(-101.40625, 120)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="136.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 179px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getConversation()</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="92.265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 138px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getSender()</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="138.296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 182px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getReadReceipts()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-113.40625 -144 C-47.26620219229562 -144, 18.873845615408754 -144, 113.40625 -144 M-113.40625 -144 C-37.03774265140618 -144, 39.33076469718765 -144, 113.40625 -144"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-113.40625 96 C-23.5165543270452 96, 66.3731413459096 96, 113.40625 96 M-113.40625 96 C-61.72673876711864 96, -10.04722753423728 96, 113.40625 96"/></g></g><g transform="translate(612.0078125, 1656)" id="classId-TeamUser-7" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-113.5625 -108 L113.5625 -108 L113.5625 108 L-113.5625 108"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-113.5625 -108 C-29.136669605916808 -108, 55.289160788166384 -108, 113.5625 -108 M-113.5625 -108 C-62.28933638718876 -108, -11.016172774377523 -108, 113.5625 -108 M113.5625 -108 C113.5625 -36.77333621035409, 113.5625 34.45332757929182, 113.5625 108 M113.5625 -108 C113.5625 -61.45912451448349, 113.5625 -14.918249028966983, 113.5625 108 M113.5625 108 C55.75212314307708 108, -2.0582537138458434 108, -113.5625 108 M113.5625 108 C53.14247532584988 108, -7.277549348300241 108, -113.5625 108 M-113.5625 108 C-113.5625 37.8781302073932, -113.5625 -32.2437395852136, -113.5625 -108 M-113.5625 108 C-113.5625 31.96046045691068, -113.5625 -44.07907908617864, -113.5625 -108"/></g><g transform="translate(0, -84)" class="annotation-group text"/><g transform="translate(-36.625, -84)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="73.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 116px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>TeamUser</p></span></div></foreignObject></g></g><g transform="translate(-101.5625, -36)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="118.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 160px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+team_id: string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="111.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 155px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+user_id: string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="87.71875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 133px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+role: string</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="162.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 193px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+created_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="166.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 197px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+updated_at: datetime</p></span></div></foreignObject></g></g><g transform="translate(-101.5625, 108)" class="methods-group text"/><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-113.5625 -60 C-23.042752635391793 -60, 67.47699472921641 -60, 113.5625 -60 M-113.5625 -60 C-38.13986411652988 -60, 37.28277176694024 -60, 113.5625 -60"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-113.5625 84 C-52.86890469813109 84, 7.824690603737821 84, 113.5625 84 M-113.5625 84 C-36.83604813812154 84, 39.89040372375692 84, 113.5625 84"/></g></g><g transform="translate(1231.353515625, 344)" id="classId-ConversationUser-8" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-131.1328125 -96 L131.1328125 -96 L131.1328125 96 L-131.1328125 96"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-131.1328125 -96 C-56.1511353437476 -96, 18.830541812504805 -96, 131.1328125 -96 M-131.1328125 -96 C-52.92504212324259 -96, 25.28272825351482 -96, 131.1328125 -96 M131.1328125 -96 C131.1328125 -25.408621090921173, 131.1328125 45.182757818157654, 131.1328125 96 M131.1328125 -96 C131.1328125 -57.45126222358324, 131.1328125 -18.902524447166485, 131.1328125 96 M131.1328125 96 C57.961205712822036 96, -15.210401074355929 96, -131.1328125 96 M131.1328125 96 C38.97232211072618 96, -53.188168278547636 96, -131.1328125 96 M-131.1328125 96 C-131.1328125 26.88450767859547, -131.1328125 -42.23098464280906, -131.1328125 -96 M-131.1328125 96 C-131.1328125 30.811332119477797, -131.1328125 -34.37733576104441, -131.1328125 -96"/></g><g transform="translate(0, -72)" class="annotation-group text"/><g transform="translate(-65.78125, -72)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="131.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 166px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>ConversationUser</p></span></div></foreignObject></g></g><g transform="translate(-119.1328125, -24)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="172.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 211px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+conversation_id: string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="111.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 155px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+user_id: string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="162.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 193px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+created_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="166.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 197px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+updated_at: datetime</p></span></div></foreignObject></g></g><g transform="translate(-119.1328125, 96)" class="methods-group text"/><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-131.1328125 -48 C-35.672387181691775 -48, 59.78803813661645 -48, 131.1328125 -48 M-131.1328125 -48 C-37.62214960465484 -48, 55.88851329069033 -48, 131.1328125 -48"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-131.1328125 72 C-26.338669544601416 72, 78.45547341079717 72, 131.1328125 72 M-131.1328125 72 C-35.99635805632175 72, 59.14009638735649 72, 131.1328125 72"/></g></g><g transform="translate(229.46875, 2306)" id="classId-Tag-9" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-101.39453125 -120 L101.39453125 -120 L101.39453125 120 L-101.39453125 120"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-101.39453125 -120 C-59.62418458601659 -120, -17.85383792203318 -120, 101.39453125 -120 M-101.39453125 -120 C-42.45553398242367 -120, 16.483463285152666 -120, 101.39453125 -120 M101.39453125 -120 C101.39453125 -31.664864772707404, 101.39453125 56.67027045458519, 101.39453125 120 M101.39453125 -120 C101.39453125 -62.870184245027446, 101.39453125 -5.740368490054891, 101.39453125 120 M101.39453125 120 C42.62902769940706 120, -16.136475851185878 120, -101.39453125 120 M101.39453125 120 C48.32532121396195 120, -4.7438888220761015 120, -101.39453125 120 M-101.39453125 120 C-101.39453125 60.8911097758355, -101.39453125 1.7822195516710053, -101.39453125 -120 M-101.39453125 120 C-101.39453125 62.15728705628714, -101.39453125 4.314574112574277, -101.39453125 -120"/></g><g transform="translate(0, -96)" class="annotation-group text"/><g transform="translate(-12.2890625, -96)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="24.578125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 74px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Tag</p></span></div></foreignObject></g></g><g transform="translate(-89.39453125, -48)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="72.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+id: string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="98.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 143px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+name: string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="87.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 135px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+slug: string</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="91.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+type: string</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="162.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 193px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+created_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="166.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 197px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+updated_at: datetime</p></span></div></foreignObject></g></g><g transform="translate(-89.39453125, 120)" class="methods-group text"/><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-101.39453125 -72 C-55.556703783707206 -72, -9.718876317414413 -72, 101.39453125 -72 M-101.39453125 -72 C-56.402669169077164 -72, -11.410807088154328 -72, 101.39453125 -72"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-101.39453125 96 C-55.893035104690014 96, -10.391538959380028 96, 101.39453125 96 M-101.39453125 96 C-52.528283117737075 96, -3.6620349854741505 96, 101.39453125 96"/></g></g><g transform="translate(139.94140625, 2932)" id="classId-Taggable-10" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-108.7421875 -84 L108.7421875 -84 L108.7421875 84 L-108.7421875 84"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-108.7421875 -84 C-25.504443500785072 -84, 57.733300498429855 -84, 108.7421875 -84 M-108.7421875 -84 C-24.77483320090589 -84, 59.19252109818822 -84, 108.7421875 -84 M108.7421875 -84 C108.7421875 -43.66521014364086, 108.7421875 -3.3304202872817257, 108.7421875 84 M108.7421875 -84 C108.7421875 -43.92606873140042, 108.7421875 -3.8521374628008402, 108.7421875 84 M108.7421875 84 C34.73019668333842 84, -39.28179413332316 84, -108.7421875 84 M108.7421875 84 C22.034887796577053 84, -64.6724119068459 84, -108.7421875 84 M-108.7421875 84 C-108.7421875 38.35981457147515, -108.7421875 -7.280370857049704, -108.7421875 -84 M-108.7421875 84 C-108.7421875 46.01575204412917, -108.7421875 8.031504088258345, -108.7421875 -84"/></g><g transform="translate(0, -60)" class="annotation-group text"/><g transform="translate(-32.171875, -60)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="64.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 108px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Taggable</p></span></div></foreignObject></g></g><g transform="translate(-96.7421875, -12)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="104.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 148px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+tag_id: string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="142.90625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 183px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+taggable_id: string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="161.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 198px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+taggable_type: string</p></span></div></foreignObject></g></g><g transform="translate(-96.7421875, 84)" class="methods-group text"/><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-108.7421875 -36 C-39.182626249816025 -36, 30.37693500036795 -36, 108.7421875 -36 M-108.7421875 -36 C-36.4382935726193 -36, 35.8656003547614 -36, 108.7421875 -36"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-108.7421875 60 C-49.20714588080188 60, 10.327895738396236 60, 108.7421875 60 M-108.7421875 60 C-26.921667416908903 60, 54.898852666182194 60, 108.7421875 60"/></g></g><g transform="translate(750.138671875, 2932)" id="classId-Comment-11" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-128.51953125 -192 L128.51953125 -192 L128.51953125 192 L-128.51953125 192"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-128.51953125 -192 C-52.58252038568101 -192, 23.354490478637985 -192, 128.51953125 -192 M-128.51953125 -192 C-66.53692753435558 -192, -4.5543238187111825 -192, 128.51953125 -192 M128.51953125 -192 C128.51953125 -112.52954888103852, 128.51953125 -33.059097762077045, 128.51953125 192 M128.51953125 -192 C128.51953125 -73.86098406518187, 128.51953125 44.278031869636266, 128.51953125 192 M128.51953125 192 C65.55031246282155 192, 2.581093675643089 192, -128.51953125 192 M128.51953125 192 C41.78008754086281 192, -44.95935616827438 192, -128.51953125 192 M-128.51953125 192 C-128.51953125 55.055669562557256, -128.51953125 -81.88866087488549, -128.51953125 -192 M-128.51953125 192 C-128.51953125 87.2406558970464, -128.51953125 -17.51868820590721, -128.51953125 -192"/></g><g transform="translate(0, -168)" class="annotation-group text"/><g transform="translate(-35.6640625, -168)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="71.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 113px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Comment</p></span></div></foreignObject></g></g><g transform="translate(-116.51953125, -120)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="72.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+id: string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="114.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 155px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+content: string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="178.984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 215px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+commentable_id: string</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="197.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 230px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+commentable_type: string</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="162.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 193px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+created_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="166.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 197px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+updated_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="162.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 193px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+deleted_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,156)" style="" class="label"><foreignObject height="24" width="139.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 179px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+created_by: string</p></span></div></foreignObject></g><g transform="translate(0,180)" style="" class="label"><foreignObject height="24" width="143.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 183px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+updated_by: string</p></span></div></foreignObject></g><g transform="translate(0,204)" style="" class="label"><foreignObject height="24" width="139.734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 179px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+deleted_by: string</p></span></div></foreignObject></g></g><g transform="translate(-116.51953125, 144)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="142.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 183px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getCommentable()</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="91.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 139px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getAuthor()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-128.51953125 -144 C-76.54311712750994 -144, -24.56670300501989 -144, 128.51953125 -144 M-128.51953125 -144 C-72.85380565200374 -144, -17.188080054007486 -144, 128.51953125 -144"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-128.51953125 120 C-76.46425930263572 120, -24.408987355271464 120, 128.51953125 120 M-128.51953125 120 C-40.16796256579369 120, 48.18360611841263 120, 128.51953125 120"/></g></g><g transform="translate(1758.111328125, 2932)" id="classId-Media-12" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-106.17578125 -228 L106.17578125 -228 L106.17578125 228 L-106.17578125 228"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-106.17578125 -228 C-41.37860151872995 -228, 23.418578212540098 -228, 106.17578125 -228 M-106.17578125 -228 C-60.569645174056724 -228, -14.963509098113448 -228, 106.17578125 -228 M106.17578125 -228 C106.17578125 -60.67867842737604, 106.17578125 106.64264314524792, 106.17578125 228 M106.17578125 -228 C106.17578125 -133.990831125969, 106.17578125 -39.98166225193805, 106.17578125 228 M106.17578125 228 C38.749256731736594 228, -28.677267786526812 228, -106.17578125 228 M106.17578125 228 C30.455697067402653 228, -45.264387115194694 228, -106.17578125 228 M-106.17578125 228 C-106.17578125 58.07774086263629, -106.17578125 -111.84451827472742, -106.17578125 -228 M-106.17578125 228 C-106.17578125 78.70121115363173, -106.17578125 -70.59757769273654, -106.17578125 -228"/></g><g transform="translate(0, -204)" class="annotation-group text"/><g transform="translate(-21.8515625, -204)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="43.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 91px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Media</p></span></div></foreignObject></g></g><g transform="translate(-94.17578125, -156)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="72.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+id: string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="98.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 143px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+name: string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="130.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 172px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+file_name: string</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="139.578125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 180px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+mime_type: string</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="97.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 141px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+size: integer</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="87.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 135px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+disk: string</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="91.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+path: string</p></span></div></foreignObject></g><g transform="translate(0,156)" style="" class="label"><foreignObject height="24" width="147.578125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 187px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+mediable_id: string</p></span></div></foreignObject></g><g transform="translate(0,180)" style="" class="label"><foreignObject height="24" width="165.984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 203px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+mediable_type: string</p></span></div></foreignObject></g><g transform="translate(0,204)" style="" class="label"><foreignObject height="24" width="162.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 193px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+created_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,228)" style="" class="label"><foreignObject height="24" width="166.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 197px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+updated_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,252)" style="" class="label"><foreignObject height="24" width="139.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 179px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+created_by: string</p></span></div></foreignObject></g><g transform="translate(0,276)" style="" class="label"><foreignObject height="24" width="143.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 183px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+updated_by: string</p></span></div></foreignObject></g></g><g transform="translate(-94.17578125, 180)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="107.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 154px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getMediable()</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="64.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 115px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getUrl()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-106.17578125 -180 C-42.244186453714484 -180, 21.687408342571032 -180, 106.17578125 -180 M-106.17578125 -180 C-31.141026083096364 -180, 43.89372908380727 -180, 106.17578125 -180"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-106.17578125 156 C-26.705932641598864 156, 52.76391596680227 156, 106.17578125 156 M-106.17578125 156 C-59.59724740272505 156, -13.018713555450105 156, 106.17578125 156"/></g></g><g transform="translate(2206.28125, 2932)" id="classId-Activity-13" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-107.90625 -180 L107.90625 -180 L107.90625 180 L-107.90625 180"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-107.90625 -180 C-60.70589792841217 -180, -13.505545856824341 -180, 107.90625 -180 M-107.90625 -180 C-33.96438410248858 -180, 39.97748179502284 -180, 107.90625 -180 M107.90625 -180 C107.90625 -51.78134457474255, 107.90625 76.4373108505149, 107.90625 180 M107.90625 -180 C107.90625 -48.25053477804576, 107.90625 83.49893044390848, 107.90625 180 M107.90625 180 C56.90492595387727 180, 5.903601907754535 180, -107.90625 180 M107.90625 180 C53.97358464609122 180, 0.04091929218243706 180, -107.90625 180 M-107.90625 180 C-107.90625 79.1837606796677, -107.90625 -21.6324786406646, -107.90625 -180 M-107.90625 180 C-107.90625 73.17151807868453, -107.90625 -33.656963842630944, -107.90625 -180"/></g><g transform="translate(0, -156)" class="annotation-group text"/><g transform="translate(-29.015625, -156)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="58.03125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 102px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Activity</p></span></div></foreignObject></g></g><g transform="translate(-95.90625, -108)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="72.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+id: string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="128.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 171px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+log_name: string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="139.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 179px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+description: string</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="134.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 174px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+subject_id: string</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="152.71875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 189px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+subject_type: string</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="127.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 170px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+causer_id: string</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="146.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 185px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+causer_type: string</p></span></div></foreignObject></g><g transform="translate(0,156)" style="" class="label"><foreignObject height="24" width="122.453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 163px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+properties: json</p></span></div></foreignObject></g><g transform="translate(0,180)" style="" class="label"><foreignObject height="24" width="162.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 193px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+created_at: datetime</p></span></div></foreignObject></g></g><g transform="translate(-95.90625, 132)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="97.453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 142px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getSubject()</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="91.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 138px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getCauser()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-107.90625 -132 C-30.09608535644996 -132, 47.71407928710008 -132, 107.90625 -132 M-107.90625 -132 C-49.32015410616146 -132, 9.265941787677079 -132, 107.90625 -132"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-107.90625 108 C-37.341278436926345 108, 33.22369312614731 108, 107.90625 108 M-107.90625 108 C-32.14285819316456 108, 43.62053361367089 108, 107.90625 108"/></g></g><g transform="translate(1409.478515625, 2306)" id="classId-Permission-14" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-115.3125 -108 L115.3125 -108 L115.3125 108 L-115.3125 108"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-115.3125 -108 C-62.660910950954204 -108, -10.009321901908407 -108, 115.3125 -108 M-115.3125 -108 C-56.780130500036826 -108, 1.7522389999263481 -108, 115.3125 -108 M115.3125 -108 C115.3125 -38.92552683004067, 115.3125 30.148946339918666, 115.3125 108 M115.3125 -108 C115.3125 -58.656890935893365, 115.3125 -9.31378187178673, 115.3125 108 M115.3125 108 C51.59270758240175 108, -12.127084835196499 108, -115.3125 108 M115.3125 108 C44.4700615593877 108, -26.372376881224596 108, -115.3125 108 M-115.3125 108 C-115.3125 54.34854508654728, -115.3125 0.6970901730945656, -115.3125 -108 M-115.3125 108 C-115.3125 27.644583872807544, -115.3125 -52.71083225438491, -115.3125 -108"/></g><g transform="translate(0, -84)" class="annotation-group text"/><g transform="translate(-40.125, -84)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="80.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Permission</p></span></div></foreignObject></g></g><g transform="translate(-103.3125, -36)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="72.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+id: string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="98.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 143px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+name: string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="147.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 187px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+guard_name: string</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="162.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 193px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+created_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="166.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 197px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+updated_at: datetime</p></span></div></foreignObject></g></g><g transform="translate(-103.3125, 108)" class="methods-group text"/><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-115.3125 -60 C-54.12425706276915 -60, 7.063985874461693 -60, 115.3125 -60 M-115.3125 -60 C-48.9664173854984 -60, 17.379665229003194 -60, 115.3125 -60"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-115.3125 84 C-52.63830430082852 84, 10.035891398342955 84, 115.3125 84 M-115.3125 84 C-25.93540750926985 84, 63.4416849814603 84, 115.3125 84"/></g></g><g transform="translate(1409.478515625, 1656)" id="classId-Role-15" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-103.4375 -132 L103.4375 -132 L103.4375 132 L-103.4375 132"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-103.4375 -132 C-36.01394149246154 -132, 31.40961701507692 -132, 103.4375 -132 M-103.4375 -132 C-53.03749831022305 -132, -2.6374966204461003 -132, 103.4375 -132 M103.4375 -132 C103.4375 -64.72359158727112, 103.4375 2.552816825457768, 103.4375 132 M103.4375 -132 C103.4375 -45.59627247535603, 103.4375 40.80745504928794, 103.4375 132 M103.4375 132 C28.557086672464862 132, -46.323326655070275 132, -103.4375 132 M103.4375 132 C39.229733236859204 132, -24.978033526281592 132, -103.4375 132 M-103.4375 132 C-103.4375 31.461320352812052, -103.4375 -69.0773592943759, -103.4375 -132 M-103.4375 132 C-103.4375 30.868021258617517, -103.4375 -70.26395748276497, -103.4375 -132"/></g><g transform="translate(0, -108)" class="annotation-group text"/><g transform="translate(-16.375, -108)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="32.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 80px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Role</p></span></div></foreignObject></g></g><g transform="translate(-91.4375, -60)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="72.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+id: string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="98.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 143px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+name: string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="147.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 187px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+guard_name: string</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="118.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 160px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+team_id: string</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="162.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 193px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+created_at: datetime</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="166.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 197px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+updated_at: datetime</p></span></div></foreignObject></g></g><g transform="translate(-91.4375, 108)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="125.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 171px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+getPermissions()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-103.4375 -84 C-24.09639682993965 -84, 55.2447063401207 -84, 103.4375 -84 M-103.4375 -84 C-39.223149639418736 -84, 24.991200721162528 -84, 103.4375 -84"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-103.4375 84 C-44.44884648024354 84, 14.539807039512922 84, 103.4375 84 M-103.4375 84 C-46.40531684249137 84, 10.626866315017267 84, 103.4375 84"/></g></g><g transform="translate(1524.005859375, 344)" id="classId-ModelHasRoles-16" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-111.51953125 -96 L111.51953125 -96 L111.51953125 96 L-111.51953125 96"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-111.51953125 -96 C-63.37535026331285 -96, -15.231169276625707 -96, 111.51953125 -96 M-111.51953125 -96 C-54.64706608404785 -96, 2.2253990819042997 -96, 111.51953125 -96 M111.51953125 -96 C111.51953125 -54.10220222549688, 111.51953125 -12.204404450993763, 111.51953125 96 M111.51953125 -96 C111.51953125 -53.97193706925345, 111.51953125 -11.9438741385069, 111.51953125 96 M111.51953125 96 C28.22963703060566 96, -55.06025718878868 96, -111.51953125 96 M111.51953125 96 C57.70434388825646 96, 3.88915652651292 96, -111.51953125 96 M-111.51953125 96 C-111.51953125 49.96164506289699, -111.51953125 3.923290125793983, -111.51953125 -96 M-111.51953125 96 C-111.51953125 35.29652369780339, -111.51953125 -25.406952604393226, -111.51953125 -96"/></g><g transform="translate(0, -72)" class="annotation-group text"/><g transform="translate(-55.0859375, -72)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="110.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 153px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>ModelHasRoles</p></span></div></foreignObject></g></g><g transform="translate(-99.51953125, -24)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="109.578125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 154px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+role_id: string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="125.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 169px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+model_id: string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="143.953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 184px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+model_type: string</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="118.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 160px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+team_id: string</p></span></div></foreignObject></g></g><g transform="translate(-99.51953125, 96)" class="methods-group text"/><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-111.51953125 -48 C-59.48152540166774 -48, -7.443519553335477 -48, 111.51953125 -48 M-111.51953125 -48 C-23.736167514285427 -48, 64.04719622142915 -48, 111.51953125 -48"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-111.51953125 72 C-26.849695475374475 72, 57.82014029925105 72, 111.51953125 72 M-111.51953125 72 C-43.378632986463245 72, 24.76226527707351 72, 111.51953125 72"/></g></g><g transform="translate(1813.021484375, 344)" id="classId-RoleHasPermissions-17" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-127.49609375 -72 L127.49609375 -72 L127.49609375 72 L-127.49609375 72"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-127.49609375 -72 C-44.11389612427355 -72, 39.268301501452896 -72, 127.49609375 -72 M-127.49609375 -72 C-74.52130859821966 -72, -21.546523446439338 -72, 127.49609375 -72 M127.49609375 -72 C127.49609375 -37.379062600051974, 127.49609375 -2.7581252001039473, 127.49609375 72 M127.49609375 -72 C127.49609375 -26.66336026243232, 127.49609375 18.67327947513536, 127.49609375 72 M127.49609375 72 C38.30370919900008 72, -50.88867535199984 72, -127.49609375 72 M127.49609375 72 C60.22882093352037 72, -7.038451882959265 72, -127.49609375 72 M-127.49609375 72 C-127.49609375 25.296991109105086, -127.49609375 -21.406017781789828, -127.49609375 -72 M-127.49609375 72 C-127.49609375 18.28464553330165, -127.49609375 -35.4307089333967, -127.49609375 -72"/></g><g transform="translate(0, -48)" class="annotation-group text"/><g transform="translate(-73.1171875, -48)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="146.234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 182px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>RoleHasPermissions</p></span></div></foreignObject></g></g><g transform="translate(-115.49609375, 0)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="157.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 199px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+permission_id: string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="109.578125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 154px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+role_id: string</p></span></div></foreignObject></g></g><g transform="translate(-115.49609375, 72)" class="methods-group text"/><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-127.49609375 -24 C-26.528931279438737 -24, 74.43823119112253 -24, 127.49609375 -24 M-127.49609375 -24 C-36.08715091301343 -24, 55.32179192397314 -24, 127.49609375 -24"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-127.49609375 48 C-58.683426012643 48, 10.129241724714007 48, 127.49609375 48 M-127.49609375 48 C-61.32178428074735 48, 4.852525188505297 48, 127.49609375 48"/></g></g><g transform="translate(2120.873046875, 344)" id="classId-ModelHasPermissions-18" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-130.35546875 -96 L130.35546875 -96 L130.35546875 96 L-130.35546875 96"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-130.35546875 -96 C-51.32232221629397 -96, 27.71082431741206 -96, 130.35546875 -96 M-130.35546875 -96 C-53.351598423137176 -96, 23.652271903725648 -96, 130.35546875 -96 M130.35546875 -96 C130.35546875 -23.926343439767123, 130.35546875 48.14731312046575, 130.35546875 96 M130.35546875 -96 C130.35546875 -52.29921105062747, 130.35546875 -8.59842210125494, 130.35546875 96 M130.35546875 96 C40.57954369907306 96, -49.19638135185389 96, -130.35546875 96 M130.35546875 96 C35.15927338086459 96, -60.03692198827082 96, -130.35546875 96 M-130.35546875 96 C-130.35546875 21.15682258271481, -130.35546875 -53.68635483457038, -130.35546875 -96 M-130.35546875 96 C-130.35546875 53.92173654953013, -130.35546875 11.843473099060262, -130.35546875 -96"/></g><g transform="translate(0, -72)" class="annotation-group text"/><g transform="translate(-78.8359375, -72)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="157.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 194px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>ModelHasPermissions</p></span></div></foreignObject></g></g><g transform="translate(-118.35546875, -24)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="157.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 199px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+permission_id: string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="125.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 169px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+model_id: string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="143.953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 184px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+model_type: string</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="118.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 160px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+team_id: string</p></span></div></foreignObject></g></g><g transform="translate(-118.35546875, 96)" class="methods-group text"/><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-130.35546875 -48 C-26.497770593215506 -48, 77.35992756356899 -48, 130.35546875 -48 M-130.35546875 -48 C-64.93181011956074 -48, 0.4918485108785262 -48, 130.35546875 -48"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-130.35546875 72 C-39.45584745119321 72, 51.443773847613585 72, 130.35546875 72 M-130.35546875 72 C-55.93219118593892 72, 18.491086378122162 72, 130.35546875 72"/></g></g><g transform="translate(2381.029296875, 344)" id="classId-UserStatus-19" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-79.80078125 -108 L79.80078125 -108 L79.80078125 108 L-79.80078125 108"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-79.80078125 -108 C-31.363997749099596 -108, 17.072785751800808 -108, 79.80078125 -108 M-79.80078125 -108 C-43.879445434520576 -108, -7.958109619041153 -108, 79.80078125 -108 M79.80078125 -108 C79.80078125 -24.201041219604207, 79.80078125 59.597917560791586, 79.80078125 108 M79.80078125 -108 C79.80078125 -23.904859004538324, 79.80078125 60.19028199092335, 79.80078125 108 M79.80078125 108 C46.34114452606314 108, 12.88150780212628 108, -79.80078125 108 M79.80078125 108 C46.24883490300558 108, 12.69688855601116 108, -79.80078125 108 M-79.80078125 108 C-79.80078125 53.75794588506328, -79.80078125 -0.48410822987344204, -79.80078125 -108 M-79.80078125 108 C-79.80078125 22.627887822787073, -79.80078125 -62.74422435442585, -79.80078125 -108"/></g><g transform="translate(-53.9296875, -84)" class="annotation-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="107.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 146px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>«enumeration»</p></span></div></foreignObject></g></g><g transform="translate(-39.75, -60)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="79.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 119px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>UserStatus</p></span></div></foreignObject></g></g><g transform="translate(-67.80078125, -12)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="50.71875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 109px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>ACTIVE</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="65.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 126px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>INACTIVE</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="81.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 142px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>SUSPENDED</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="63"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 120px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>PENDING</p></span></div></foreignObject></g></g><g transform="translate(-67.80078125, 108)" class="methods-group text"/><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-79.80078125 -36 C-19.729817561856066 -36, 40.34114612628787 -36, 79.80078125 -36 M-79.80078125 -36 C-17.638469465056453 -36, 44.523842319887095 -36, 79.80078125 -36"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-79.80078125 84 C-20.140487089056798 84, 39.519807071886405 84, 79.80078125 84 M-79.80078125 84 C-45.842349964725784 84, -11.883918679451568 84, 79.80078125 84"/></g></g><g transform="translate(2580.810546875, 344)" id="classId-PresenceStatus-20" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-69.98046875 -108 L69.98046875 -108 L69.98046875 108 L-69.98046875 108"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-69.98046875 -108 C-34.778906635604386 -108, 0.4226554787912278 -108, 69.98046875 -108 M-69.98046875 -108 C-18.837027747274448 -108, 32.306413255451105 -108, 69.98046875 -108 M69.98046875 -108 C69.98046875 -22.573311659452287, 69.98046875 62.853376681095426, 69.98046875 108 M69.98046875 -108 C69.98046875 -45.92199534448072, 69.98046875 16.15600931103856, 69.98046875 108 M69.98046875 108 C23.2523355965184 108, -23.4757975569632 108, -69.98046875 108 M69.98046875 108 C18.194023224801867 108, -33.592422300396265 108, -69.98046875 108 M-69.98046875 108 C-69.98046875 45.5935828389898, -69.98046875 -16.812834322020393, -69.98046875 -108 M-69.98046875 108 C-69.98046875 50.77233236944004, -69.98046875 -6.455335261119913, -69.98046875 -108"/></g><g transform="translate(-53.9296875, -84)" class="annotation-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="107.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 146px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>«enumeration»</p></span></div></foreignObject></g></g><g transform="translate(-57.0390625, -60)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="114.078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 146px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>PresenceStatus</p></span></div></foreignObject></g></g><g transform="translate(-57.98046875, -12)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="52.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 110px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>ONLINE</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="37.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 95px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>AWAY</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="36.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 93px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>BUSY</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="58.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 116px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>OFFLINE</p></span></div></foreignObject></g></g><g transform="translate(-57.98046875, 108)" class="methods-group text"/><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-69.98046875 -36 C-19.56984908875338 -36, 30.84077057249324 -36, 69.98046875 -36 M-69.98046875 -36 C-40.708468703974404 -36, -11.4364686579488 -36, 69.98046875 -36"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-69.98046875 84 C-41.399348414272545 84, -12.81822807854509 84, 69.98046875 84 M-69.98046875 84 C-41.54189461849188 84, -13.103320486983762 84, 69.98046875 84"/></g></g><g transform="translate(2775.271484375, 344)" id="classId-TeamStatus-21" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-74.48046875 -96 L74.48046875 -96 L74.48046875 96 L-74.48046875 96"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-74.48046875 -96 C-35.22264552109236 -96, 4.035177707815279 -96, 74.48046875 -96 M-74.48046875 -96 C-27.567027229168808 -96, 19.346414291662384 -96, 74.48046875 -96 M74.48046875 -96 C74.48046875 -55.389283069551254, 74.48046875 -14.778566139102509, 74.48046875 96 M74.48046875 -96 C74.48046875 -20.822230397069916, 74.48046875 54.35553920586017, 74.48046875 96 M74.48046875 96 C34.49329208363036 96, -5.493884582739284 96, -74.48046875 96 M74.48046875 96 C19.995865206248034 96, -34.48873833750393 96, -74.48046875 96 M-74.48046875 96 C-74.48046875 57.4482494584287, -74.48046875 18.896498916857396, -74.48046875 -96 M-74.48046875 96 C-74.48046875 46.502532159334535, -74.48046875 -2.9949356813309294, -74.48046875 -96"/></g><g transform="translate(-53.9296875, -72)" class="annotation-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="107.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 146px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>«enumeration»</p></span></div></foreignObject></g></g><g transform="translate(-42.609375, -48)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="85.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 124px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>TeamStatus</p></span></div></foreignObject></g></g><g transform="translate(-62.48046875, 0)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="50.71875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 109px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>ACTIVE</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="65.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 126px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>INACTIVE</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="71.03125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 133px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>ARCHIVED</p></span></div></foreignObject></g></g><g transform="translate(-62.48046875, 96)" class="methods-group text"/><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-74.48046875 -24 C-15.122602994385481 -24, 44.23526276122904 -24, 74.48046875 -24 M-74.48046875 -24 C-27.966527947289435 -24, 18.54741285542113 -24, 74.48046875 -24"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-74.48046875 72 C-26.64366695173827 72, 21.193134846523463 72, 74.48046875 72 M-74.48046875 72 C-35.248084418969604 72, 3.9842999120607914 72, 74.48046875 72"/></g></g><g transform="translate(2980.208984375, 344)" id="classId-PostStatus-22" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-80.45703125 -108 L80.45703125 -108 L80.45703125 108 L-80.45703125 108"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-80.45703125 -108 C-46.98189857534479 -108, -13.506765900689587 -108, 80.45703125 -108 M-80.45703125 -108 C-23.975317956195106 -108, 32.50639533760979 -108, 80.45703125 -108 M80.45703125 -108 C80.45703125 -56.47208081737593, 80.45703125 -4.944161634751865, 80.45703125 108 M80.45703125 -108 C80.45703125 -51.408274957033356, 80.45703125 5.183450085933288, 80.45703125 108 M80.45703125 108 C32.64081481577542 108, -15.175401618449158 108, -80.45703125 108 M80.45703125 108 C23.315375487508057 108, -33.82628027498389 108, -80.45703125 108 M-80.45703125 108 C-80.45703125 37.4739157835197, -80.45703125 -33.0521684329606, -80.45703125 -108 M-80.45703125 108 C-80.45703125 37.65178548227976, -80.45703125 -32.696429035440474, -80.45703125 -108"/></g><g transform="translate(-53.9296875, -84)" class="annotation-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="107.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 146px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>«enumeration»</p></span></div></foreignObject></g></g><g transform="translate(-38.3359375, -60)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="76.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 117px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>PostStatus</p></span></div></foreignObject></g></g><g transform="translate(-68.45703125, -12)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="46.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 102px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>DRAFT</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="77.453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 138px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>PUBLISHED</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="71.03125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 133px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>ARCHIVED</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="82.984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 145px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>SCHEDULED</p></span></div></foreignObject></g></g><g transform="translate(-68.45703125, 108)" class="methods-group text"/><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-80.45703125 -36 C-31.84936400918059 -36, 16.75830323163882 -36, 80.45703125 -36 M-80.45703125 -36 C-36.509473174347846 -36, 7.4380849013043076 -36, 80.45703125 -36"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-80.45703125 84 C-46.72091549761429 84, -12.984799745228585 84, 80.45703125 84 M-80.45703125 84 C-19.290343534801686 84, 41.87634418039663 84, 80.45703125 84"/></g></g><g transform="translate(3197.716796875, 344)" id="classId-TodoStatus-23" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-87.05078125 -120 L87.05078125 -120 L87.05078125 120 L-87.05078125 120"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-87.05078125 -120 C-38.1103652479819 -120, 10.830050754036193 -120, 87.05078125 -120 M-87.05078125 -120 C-41.224383559621685 -120, 4.602014130756629 -120, 87.05078125 -120 M87.05078125 -120 C87.05078125 -55.25621919289509, 87.05078125 9.487561614209824, 87.05078125 120 M87.05078125 -120 C87.05078125 -48.84760770444947, 87.05078125 22.304784591101054, 87.05078125 120 M87.05078125 120 C37.53812565483364 120, -11.974529940332715 120, -87.05078125 120 M87.05078125 120 C36.12600477193694 120, -14.798771706126118 120, -87.05078125 120 M-87.05078125 120 C-87.05078125 71.60111333935724, -87.05078125 23.20222667871448, -87.05078125 -120 M-87.05078125 120 C-87.05078125 45.93164005124392, -87.05078125 -28.136719897512165, -87.05078125 -120"/></g><g transform="translate(-53.9296875, -96)" class="annotation-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="107.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 146px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>«enumeration»</p></span></div></foreignObject></g></g><g transform="translate(-40.4296875, -72)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="80.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 122px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>TodoStatus</p></span></div></foreignObject></g></g><g transform="translate(-75.05078125, -24)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="63.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 122px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>CREATED</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="96.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 156px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>IN_PROGRESS</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="68.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 126px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>ON_HOLD</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="84.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 146px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>COMPLETED</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="81.953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 145px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>CANCELLED</p></span></div></foreignObject></g></g><g transform="translate(-75.05078125, 120)" class="methods-group text"/><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-87.05078125 -48 C-36.518584131384806 -48, 14.013612987230388 -48, 87.05078125 -48 M-87.05078125 -48 C-46.071970855008935 -48, -5.09316046001787 -48, 87.05078125 -48"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-87.05078125 96 C-39.71290081954893 96, 7.624979610902145 96, 87.05078125 96 M-87.05078125 96 C-33.43341367845134 96, 20.183953893097325 96, 87.05078125 96"/></g></g><g transform="translate(3403.021484375, 344)" id="classId-Priority-24" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-68.25390625 -108 L68.25390625 -108 L68.25390625 108 L-68.25390625 108"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-68.25390625 -108 C-22.901814270262122 -108, 22.450277709475756 -108, 68.25390625 -108 M-68.25390625 -108 C-38.31064753979054 -108, -8.367388829581067 -108, 68.25390625 -108 M68.25390625 -108 C68.25390625 -52.62907218197154, 68.25390625 2.7418556360569255, 68.25390625 108 M68.25390625 -108 C68.25390625 -58.632922737277205, 68.25390625 -9.26584547455441, 68.25390625 108 M68.25390625 108 C23.870496026379072 108, -20.512914197241855 108, -68.25390625 108 M68.25390625 108 C22.76405450302287 108, -22.72579724395426 108, -68.25390625 108 M-68.25390625 108 C-68.25390625 44.06849690316445, -68.25390625 -19.863006193671097, -68.25390625 -108 M-68.25390625 108 C-68.25390625 25.137909671647378, -68.25390625 -57.724180656705244, -68.25390625 -108"/></g><g transform="translate(-53.9296875, -84)" class="annotation-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="107.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 146px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>«enumeration»</p></span></div></foreignObject></g></g><g transform="translate(-28.2734375, -60)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="56.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 99px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Priority</p></span></div></foreignObject></g></g><g transform="translate(-56.25390625, -12)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="32.515625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 86px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>LOW</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="55.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 117px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>MEDIUM</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="36.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 90px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>HIGH</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="58.578125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 115px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>URGENT</p></span></div></foreignObject></g></g><g transform="translate(-56.25390625, 108)" class="methods-group text"/><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-68.25390625 -36 C-37.62922116888407 -36, -7.0045360877681375 -36, 68.25390625 -36 M-68.25390625 -36 C-31.989701075719168 -36, 4.274504098561664 -36, 68.25390625 -36"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-68.25390625 84 C-39.129475764605004 84, -10.005045279210016 84, 68.25390625 84 M-68.25390625 84 C-38.787340208456875 84, -9.32077416691375 84, 68.25390625 84"/></g></g><g transform="translate(1665.953125, 1656)" id="Team---Team---1" class="label edgeLabel"><rect height="0.1" width="0.1"/><g transform="translate(0, 0)" style="" class="label"><rect/><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 10px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g></g><g transform="translate(1627.646484375, 2306)" id="Team---Team---2" class="label edgeLabel"><rect height="0.1" width="0.1"/><g transform="translate(0, 0)" style="" class="label"><rect/><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 10px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g></g><g transform="translate(2529.4390625003725, 2306)" id="Category---Category---1" class="label edgeLabel"><rect height="0.1" width="0.1"/><g transform="translate(0, 0)" style="" class="label"><rect/><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 10px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g></g><g transform="translate(2491.4937500003725, 2932)" id="Category---Category---2" class="label edgeLabel"><rect height="0.1" width="0.1"/><g transform="translate(0, 0)" style="" class="label"><rect/><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 10px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g></g><g transform="translate(2681.2203125003725, 2932)" id="Todo---Todo---1" class="label edgeLabel"><rect height="0.1" width="0.1"/><g transform="translate(0, 0)" style="" class="label"><rect/><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 10px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g></g><g transform="translate(2643.2750000003725, 3234.050000000745)" id="Todo---Todo---2" class="label edgeLabel"><rect height="0.1" width="0.1"/><g transform="translate(0, 0)" style="" class="label"><rect/><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 10px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g></g></g></g></g></svg>