<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 3654.609375 302" style="max-width: 3654.61px; background-color: #2d333b;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#a44141;}#my-svg .error-text{fill:#ddd;stroke:#ddd;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#ecf0f1;stroke:#ecf0f1;}#my-svg .marker.cross{stroke:#ecf0f1;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#my-svg .cluster-label text{fill:#F9FFFE;}#my-svg .cluster-label span{color:#F9FFFE;}#my-svg .cluster-label span p{background-color:transparent;}#my-svg .label text,#my-svg span{fill:#ccc;color:#ccc;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#my-svg .rough-node .label text,#my-svg .node .label text,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-anchor:middle;}#my-svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#my-svg .rough-node .label,#my-svg .node .label,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-align:center;}#my-svg .node.clickable{cursor:pointer;}#my-svg .root .anchor path{fill:#ecf0f1!important;stroke-width:0;stroke:#ecf0f1;}#my-svg .arrowheadPath{fill:lightgrey;}#my-svg .edgePath .path{stroke:#ecf0f1;stroke-width:2.0px;}#my-svg .flowchart-link{stroke:#ecf0f1;fill:none;}#my-svg .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#my-svg .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#my-svg .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#my-svg .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#my-svg .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#my-svg .cluster text{fill:#F9FFFE;}#my-svg .cluster span{color:#F9FFFE;}#my-svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:#282c34;border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#my-svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#my-svg rect.text{fill:none;stroke-width:0;}#my-svg .icon-shape,#my-svg .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#my-svg .icon-shape p,#my-svg .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#my-svg .icon-shape rect,#my-svg .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"/></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><g class="root"><g class="clusters"/><g class="edgePaths"><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M1969.434,52.447L1736.521,62.206C1503.608,71.965,1037.783,91.482,804.87,104.741C571.957,118,571.957,125,571.957,128.5L571.957,132"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_C_0" d="M1969.434,65.615L1916.61,73.18C1863.786,80.744,1758.139,95.872,1705.316,106.936C1652.492,118,1652.492,125,1652.492,128.5L1652.492,132"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_D_0" d="M2229.434,65.883L2281.202,73.402C2332.971,80.922,2436.509,95.961,2488.278,106.98C2540.047,118,2540.047,125,2540.047,128.5L2540.047,132"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_E_0" d="M2229.434,53.927L2407.956,63.439C2586.478,72.951,2943.522,91.976,3122.044,104.988C3300.566,118,3300.566,125,3300.566,128.5L3300.566,132"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_B1_0" d="M512.035,169.359L440.355,176.966C368.674,184.573,225.314,199.786,153.633,210.893C81.953,222,81.953,229,81.953,232.5L81.953,236"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_B2_0" d="M512.035,173.34L471.796,180.283C431.557,187.226,351.079,201.113,310.84,211.557C270.602,222,270.602,229,270.602,232.5L270.602,236"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_B3_0" d="M520.227,190L512.244,194.167C504.26,198.333,488.294,206.667,480.311,214.333C472.328,222,472.328,229,472.328,232.5L472.328,236"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_B4_0" d="M623.687,190L631.671,194.167C639.654,198.333,655.62,206.667,663.603,214.333C671.586,222,671.586,229,671.586,232.5L671.586,236"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_B5_0" d="M631.879,173.454L671.571,180.378C711.263,187.302,790.647,201.151,830.339,211.576C870.031,222,870.031,229,870.031,232.5L870.031,236"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_B6_0" d="M631.879,169.015L708.234,176.679C784.589,184.343,937.298,199.672,1013.653,210.836C1090.008,222,1090.008,229,1090.008,232.5L1090.008,236"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_C1_0" d="M1590.156,172.629L1544.438,179.691C1498.719,186.752,1407.281,200.876,1361.563,211.438C1315.844,222,1315.844,229,1315.844,232.5L1315.844,236"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_C2_0" d="M1597.158,190L1588.618,194.167C1580.079,198.333,1563,206.667,1554.461,214.333C1545.922,222,1545.922,229,1545.922,232.5L1545.922,236"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_C3_0" d="M1707.827,190L1716.366,194.167C1724.905,198.333,1741.984,206.667,1750.523,214.333C1759.063,222,1759.063,229,1759.063,232.5L1759.063,236"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_C4_0" d="M1714.828,173.561L1755.592,180.468C1796.357,187.374,1877.885,201.187,1918.65,211.594C1959.414,222,1959.414,229,1959.414,232.5L1959.414,236"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_D1_0" d="M2460.313,175.024L2416.129,181.686C2371.945,188.349,2283.578,201.675,2239.395,211.837C2195.211,222,2195.211,229,2195.211,232.5L2195.211,236"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_D2_0" d="M2481.427,190L2472.38,194.167C2463.334,198.333,2445.241,206.667,2436.195,214.333C2427.148,222,2427.148,229,2427.148,232.5L2427.148,236"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_D3_0" d="M2598.667,190L2607.714,194.167C2616.76,198.333,2634.853,206.667,2643.899,214.333C2652.945,222,2652.945,229,2652.945,232.5L2652.945,236"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_D4_0" d="M2619.781,176.283L2658.516,182.736C2697.25,189.189,2774.719,202.094,2813.453,212.047C2852.188,222,2852.188,229,2852.188,232.5L2852.188,236"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_E1_0" d="M3244.293,173.812L3208.563,180.676C3172.833,187.541,3101.374,201.271,3065.644,211.635C3029.914,222,3029.914,229,3029.914,232.5L3029.914,236"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_E2_0" d="M3257.444,190L3250.789,194.167C3244.134,198.333,3230.825,206.667,3224.17,214.333C3217.516,222,3217.516,229,3217.516,232.5L3217.516,236"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_E3_0" d="M3343.689,190L3350.344,194.167C3356.998,198.333,3370.308,206.667,3376.962,214.333C3383.617,222,3383.617,229,3383.617,232.5L3383.617,236"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_E4_0" d="M3356.84,173.827L3392.505,180.689C3428.169,187.552,3499.499,201.276,3535.163,211.638C3570.828,222,3570.828,229,3570.828,232.5L3570.828,236"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(2099.43359375, 47)" id="flowchart-A-0" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Enhanced Laravel Application</p></span></div></foreignObject></g></g><g transform="translate(571.95703125, 163)" id="flowchart-B-1" class="node default"><rect height="54" width="119.84375" y="-27" x="-59.921875" style="" class="basic label-container"/><g transform="translate(-29.921875, -12)" style="" class="label"><rect/><foreignObject height="24" width="59.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Backend</p></span></div></foreignObject></g></g><g transform="translate(1652.4921875, 163)" id="flowchart-C-3" class="node default"><rect height="54" width="124.671875" y="-27" x="-62.3359375" style="" class="basic label-container"/><g transform="translate(-32.3359375, -12)" style="" class="label"><rect/><foreignObject height="24" width="64.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Frontend</p></span></div></foreignObject></g></g><g transform="translate(2540.046875, 163)" id="flowchart-D-5" class="node default"><rect height="54" width="159.46875" y="-27" x="-79.734375" style="" class="basic label-container"/><g transform="translate(-49.734375, -12)" style="" class="label"><rect/><foreignObject height="24" width="99.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Infrastructure</p></span></div></foreignObject></g></g><g transform="translate(3300.56640625, 163)" id="flowchart-E-7" class="node default"><rect height="54" width="112.546875" y="-27" x="-56.2734375" style="" class="basic label-container"/><g transform="translate(-26.2734375, -12)" style="" class="label"><rect/><foreignObject height="24" width="52.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>DevOps</p></span></div></foreignObject></g></g><g transform="translate(81.953125, 267)" id="flowchart-B1-9" class="node default"><rect height="54" width="147.90625" y="-27" x="-73.953125" style="" class="basic label-container"/><g transform="translate(-43.953125, -12)" style="" class="label"><rect/><foreignObject height="24" width="87.90625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Laravel 12.x</p></span></div></foreignObject></g></g><g transform="translate(270.6015625, 267)" id="flowchart-B2-11" class="node default"><rect height="54" width="129.390625" y="-27" x="-64.6953125" style="" class="basic label-container"/><g transform="translate(-34.6953125, -12)" style="" class="label"><rect/><foreignObject height="24" width="69.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PHP 8.4.x</p></span></div></foreignObject></g></g><g transform="translate(472.328125, 267)" id="flowchart-B3-13" class="node default"><rect height="54" width="174.0625" y="-27" x="-87.03125" style="" class="basic label-container"/><g transform="translate(-57.03125, -12)" style="" class="label"><rect/><foreignObject height="24" width="114.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PostgreSQL 16.x</p></span></div></foreignObject></g></g><g transform="translate(671.5859375, 267)" id="flowchart-B4-15" class="node default"><rect height="54" width="124.453125" y="-27" x="-62.2265625" style="" class="basic label-container"/><g transform="translate(-32.2265625, -12)" style="" class="label"><rect/><foreignObject height="24" width="64.453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Redis 7.x</p></span></div></foreignObject></g></g><g transform="translate(870.03125, 267)" id="flowchart-B5-17" class="node default"><rect height="54" width="172.4375" y="-27" x="-86.21875" style="" class="basic label-container"/><g transform="translate(-56.21875, -12)" style="" class="label"><rect/><foreignObject height="24" width="112.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FrankenPHP 1.x</p></span></div></foreignObject></g></g><g transform="translate(1090.0078125, 267)" id="flowchart-B6-19" class="node default"><rect height="54" width="167.515625" y="-27" x="-83.7578125" style="" class="basic label-container"/><g transform="translate(-53.7578125, -12)" style="" class="label"><rect/><foreignObject height="24" width="107.515625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Meilisearch 1.x</p></span></div></foreignObject></g></g><g transform="translate(1315.84375, 267)" id="flowchart-C1-21" class="node default"><rect height="54" width="184.15625" y="-27" x="-92.078125" style="" class="basic label-container"/><g transform="translate(-62.078125, -12)" style="" class="label"><rect/><foreignObject height="24" width="124.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Livewire/Volt 3.x</p></span></div></foreignObject></g></g><g transform="translate(1545.921875, 267)" id="flowchart-C2-23" class="node default"><rect height="54" width="176" y="-27" x="-88" style="" class="basic label-container"/><g transform="translate(-58, -12)" style="" class="label"><rect/><foreignObject height="24" width="116"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Tailwind CSS 4.x</p></span></div></foreignObject></g></g><g transform="translate(1759.0625, 267)" id="flowchart-C3-25" class="node default"><rect height="54" width="150.28125" y="-27" x="-75.140625" style="" class="basic label-container"/><g transform="translate(-45.140625, -12)" style="" class="label"><rect/><foreignObject height="24" width="90.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Filament 3.x</p></span></div></foreignObject></g></g><g transform="translate(1959.4140625, 267)" id="flowchart-C4-27" class="node default"><rect height="54" width="150.421875" y="-27" x="-75.2109375" style="" class="basic label-container"/><g transform="translate(-45.2109375, -12)" style="" class="label"><rect/><foreignObject height="24" width="90.421875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Alpine.js 3.x</p></span></div></foreignObject></g></g><g transform="translate(2195.2109375, 267)" id="flowchart-D1-29" class="node default"><rect height="54" width="221.171875" y="-27" x="-110.5859375" style="" class="basic label-container"/><g transform="translate(-80.5859375, -12)" style="" class="label"><rect/><foreignObject height="24" width="161.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>S3-compatible Storage</p></span></div></foreignObject></g></g><g transform="translate(2427.1484375, 267)" id="flowchart-D2-31" class="node default"><rect height="54" width="142.703125" y="-27" x="-71.3515625" style="" class="basic label-container"/><g transform="translate(-41.3515625, -12)" style="" class="label"><rect/><foreignObject height="24" width="82.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Redis Cloud</p></span></div></foreignObject></g></g><g transform="translate(2652.9453125, 267)" id="flowchart-D3-33" class="node default"><rect height="54" width="208.890625" y="-27" x="-104.4453125" style="" class="basic label-container"/><g transform="translate(-74.4453125, -12)" style="" class="label"><rect/><foreignObject height="24" width="148.890625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PostgreSQL Database</p></span></div></foreignObject></g></g><g transform="translate(2852.1875, 267)" id="flowchart-D4-35" class="node default"><rect height="54" width="89.59375" y="-27" x="-44.796875" style="" class="basic label-container"/><g transform="translate(-14.796875, -12)" style="" class="label"><rect/><foreignObject height="24" width="29.59375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>CDN</p></span></div></foreignObject></g></g><g transform="translate(3029.9140625, 267)" id="flowchart-E1-37" class="node default"><rect height="54" width="165.859375" y="-27" x="-82.9296875" style="" class="basic label-container"/><g transform="translate(-52.9296875, -12)" style="" class="label"><rect/><foreignObject height="24" width="105.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>GitHub Actions</p></span></div></foreignObject></g></g><g transform="translate(3217.515625, 267)" id="flowchart-E2-39" class="node default"><rect height="54" width="109.34375" y="-27" x="-54.671875" style="" class="basic label-container"/><g transform="translate(-24.671875, -12)" style="" class="label"><rect/><foreignObject height="24" width="49.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Docker</p></span></div></foreignObject></g></g><g transform="translate(3383.6171875, 267)" id="flowchart-E3-41" class="node default"><rect height="54" width="122.859375" y="-27" x="-61.4296875" style="" class="basic label-container"/><g transform="translate(-31.4296875, -12)" style="" class="label"><rect/><foreignObject height="24" width="62.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Pest PHP</p></span></div></foreignObject></g></g><g transform="translate(3570.828125, 267)" id="flowchart-E4-43" class="node default"><rect height="54" width="151.5625" y="-27" x="-75.78125" style="" class="basic label-container"/><g transform="translate(-45.78125, -12)" style="" class="label"><rect/><foreignObject height="24" width="91.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Laravel Herd</p></span></div></foreignObject></g></g></g></g></g></svg>