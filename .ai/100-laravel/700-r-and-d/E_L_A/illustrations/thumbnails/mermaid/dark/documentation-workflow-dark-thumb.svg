<svg aria-roledescription="er" role="graphics-document document" viewBox="0 0 115.328125 470" style="max-width: 115.328px; background-color: #2d333b;" class="erDiagram" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#a44141;}#my-svg .error-text{fill:#ddd;stroke:#ddd;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#ecf0f1;stroke:#ecf0f1;}#my-svg .marker.cross{stroke:#ecf0f1;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg .entityBox{fill:#1f2020;stroke:#ccc;}#my-svg .relationshipLabelBox{fill:#282c34;opacity:0.7;background-color:#282c34;}#my-svg .relationshipLabelBox rect{opacity:0.5;}#my-svg .labelBkg{background-color:rgba(40, 44, 52, 0.5);}#my-svg .edgeLabel .label{fill:#ccc;font-size:14px;}#my-svg .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#my-svg .edge-pattern-dashed{stroke-dasharray:8,8;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#my-svg .relationshipLine{stroke:#ecf0f1;stroke-width:1;fill:none;}#my-svg .marker{fill:none!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><defs><marker orient="auto" markerHeight="18" markerWidth="18" refY="9" refX="0" class="marker onlyOne er" id="my-svg_er-onlyOneStart"><path d="M9,0 L9,18 M15,0 L15,18"/></marker></defs><defs><marker orient="auto" markerHeight="18" markerWidth="18" refY="9" refX="18" class="marker onlyOne er" id="my-svg_er-onlyOneEnd"><path d="M3,0 L3,18 M9,0 L9,18"/></marker></defs><defs><marker orient="auto" markerHeight="18" markerWidth="30" refY="9" refX="0" class="marker zeroOrOne er" id="my-svg_er-zeroOrOneStart"><circle r="6" cy="9" cx="21" fill="white"/><path d="M9,0 L9,18"/></marker></defs><defs><marker orient="auto" markerHeight="18" markerWidth="30" refY="9" refX="30" class="marker zeroOrOne er" id="my-svg_er-zeroOrOneEnd"><circle r="6" cy="9" cx="9" fill="white"/><path d="M21,0 L21,18"/></marker></defs><defs><marker orient="auto" markerHeight="36" markerWidth="45" refY="18" refX="18" class="marker oneOrMore er" id="my-svg_er-oneOrMoreStart"><path d="M0,18 Q 18,0 36,18 Q 18,36 0,18 M42,9 L42,27"/></marker></defs><defs><marker orient="auto" markerHeight="36" markerWidth="45" refY="18" refX="27" class="marker oneOrMore er" id="my-svg_er-oneOrMoreEnd"><path d="M3,9 L3,27 M9,18 Q27,0 45,18 Q27,36 9,18"/></marker></defs><defs><marker orient="auto" markerHeight="36" markerWidth="57" refY="18" refX="18" class="marker zeroOrMore er" id="my-svg_er-zeroOrMoreStart"><circle r="6" cy="18" cx="48" fill="white"/><path d="M0,18 Q18,0 36,18 Q18,36 0,18"/></marker></defs><defs><marker orient="auto" markerHeight="36" markerWidth="57" refY="18" refX="39" class="marker zeroOrMore er" id="my-svg_er-zeroOrMoreEnd"><circle r="6" cy="18" cx="9" fill="white"/><path d="M21,18 Q39,0 57,18 Q39,36 21,18"/></marker></defs><g class="root"><g class="clusters"/><g class="edgePaths"><path marker-end="url(#my-svg_er-zeroOrMoreEnd)" marker-start="url(#my-svg_er-onlyOneStart)" style="undefined;" class="edge-thickness-normal edge-pattern-solid relationshipLine" id="id_entity-ENTITY1-0_entity-ENTITY2-1_0" d="M57.664,92L57.664,100.417C57.664,108.833,57.664,125.667,57.664,142.5C57.664,159.333,57.664,176.167,57.664,184.583L57.664,193"/><path marker-end="url(#my-svg_er-onlyOneEnd)" marker-start="url(#my-svg_er-oneOrMoreStart)" style="undefined;" class="edge-thickness-normal edge-pattern-solid relationshipLine" id="id_entity-ENTITY2-1_entity-ENTITY3-2_1" d="M57.664,277L57.664,285.417C57.664,293.833,57.664,310.667,57.664,327.5C57.664,344.333,57.664,361.167,57.664,369.583L57.664,378"/></g><g class="edgeLabels"><g transform="translate(57.6640625, 142.5)" class="edgeLabel"><g transform="translate(-10.3359375, -10.5)" class="label"><foreignObject height="21" width="20.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>has</p></span></div></foreignObject></g></g><g transform="translate(57.6640625, 327.5)" class="edgeLabel"><g transform="translate(-32.3515625, -10.5)" class="label"><foreignObject height="21" width="64.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>belongs to</p></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(57.6640625, 50)" id="entity-ENTITY1-0" class="node default"><rect height="84" width="99.328125" y="-42" x="-49.6640625" style="" class="basic label-container"/><g transform="translate(-29.6640625, -12)" style="" class="label"><rect/><foreignObject height="24" width="59.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ENTITY1</p></span></div></foreignObject></g></g><g transform="translate(57.6640625, 235)" id="entity-ENTITY2-1" class="node default"><rect height="84" width="99.328125" y="-42" x="-49.6640625" style="" class="basic label-container"/><g transform="translate(-29.6640625, -12)" style="" class="label"><rect/><foreignObject height="24" width="59.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ENTITY2</p></span></div></foreignObject></g></g><g transform="translate(57.6640625, 420)" id="entity-ENTITY3-2" class="node default"><rect height="84" width="99.328125" y="-42" x="-49.6640625" style="" class="basic label-container"/><g transform="translate(-29.6640625, -12)" style="" class="label"><rect/><foreignObject height="24" width="59.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ENTITY3</p></span></div></foreignObject></g></g></g></g></g></svg>