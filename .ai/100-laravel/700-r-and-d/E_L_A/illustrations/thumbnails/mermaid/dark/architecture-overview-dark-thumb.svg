<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 1236.390625 1360" style="max-width: 1236.39px; background-color: #2d333b;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#a44141;}#my-svg .error-text{fill:#ddd;stroke:#ddd;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#ecf0f1;stroke:#ecf0f1;}#my-svg .marker.cross{stroke:#ecf0f1;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#my-svg .cluster-label text{fill:#F9FFFE;}#my-svg .cluster-label span{color:#F9FFFE;}#my-svg .cluster-label span p{background-color:transparent;}#my-svg .label text,#my-svg span{fill:#ccc;color:#ccc;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#my-svg .rough-node .label text,#my-svg .node .label text,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-anchor:middle;}#my-svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#my-svg .rough-node .label,#my-svg .node .label,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-align:center;}#my-svg .node.clickable{cursor:pointer;}#my-svg .root .anchor path{fill:#ecf0f1!important;stroke-width:0;stroke:#ecf0f1;}#my-svg .arrowheadPath{fill:lightgrey;}#my-svg .edgePath .path{stroke:#ecf0f1;stroke-width:2.0px;}#my-svg .flowchart-link{stroke:#ecf0f1;fill:none;}#my-svg .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#my-svg .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#my-svg .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#my-svg .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#my-svg .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#my-svg .cluster text{fill:#F9FFFE;}#my-svg .cluster span{color:#F9FFFE;}#my-svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:#282c34;border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#my-svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#my-svg rect.text{fill:none;stroke-width:0;}#my-svg .icon-shape,#my-svg .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#my-svg .icon-shape p,#my-svg .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#my-svg .icon-shape rect,#my-svg .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"/></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><g class="root"><g class="clusters"><g data-look="classic" id="Infrastructure" class="cluster"><rect height="104" width="1203.29296875" y="1248" x="25.09765625" style=""/><g transform="translate(554.923828125, 1248)" class="cluster-label"><foreignObject height="24" width="143.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Infrastructure Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="Domain" class="cluster"><rect height="208" width="1135.1796875" y="990" x="8" style=""/><g transform="translate(526.80859375, 990)" class="cluster-label"><foreignObject height="24" width="97.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Domain Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="Application" class="cluster"><rect height="416" width="698.6328125" y="524" x="37.390625" style=""/><g transform="translate(324.06640625, 524)" class="cluster-label"><foreignObject height="24" width="125.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Application Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="Web" class="cluster"><rect height="312" width="543.5703125" y="162" x="70.0390625" style=""/><g transform="translate(304.47265625, 162)" class="cluster-label"><foreignObject height="24" width="74.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Web Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="Client" class="cluster"><rect height="104" width="597.390625" y="8" x="35.52734375" style=""/><g transform="translate(290.80859375, 8)" class="cluster-label"><foreignObject height="24" width="86.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Client Layer</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Browser_FrankenPHP_0" d="M146.801,87L146.801,91.167C146.801,95.333,146.801,103.667,146.801,112C146.801,120.333,146.801,128.667,146.801,137C146.801,145.333,146.801,153.667,172.747,163.639C198.694,173.611,250.586,185.223,276.533,191.029L302.479,196.834"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MobileApp_FrankenPHP_0" d="M342.105,87L342.105,91.167C342.105,95.333,342.105,103.667,342.105,112C342.105,120.333,342.105,128.667,342.105,137C342.105,145.333,342.105,153.667,344.69,161.457C347.275,169.248,352.445,176.496,355.03,180.12L357.614,183.743"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_API_FrankenPHP_0" d="M529.527,87L529.527,91.167C529.527,95.333,529.527,103.667,529.527,112C529.527,120.333,529.527,128.667,529.527,137C529.527,145.333,529.527,153.667,517.237,162.084C504.948,170.502,480.368,179.004,468.078,183.255L455.788,187.506"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FrankenPHP_Laravel_0" d="M379.195,241L379.195,245.167C379.195,249.333,379.195,257.667,379.195,265.333C379.195,273,379.195,280,379.195,283.5L379.195,287"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Laravel_Livewire_0" d="M312.188,343.86L300.898,348.217C289.609,352.573,267.031,361.287,255.742,369.143C244.453,377,244.453,384,244.453,387.5L244.453,391"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Laravel_Filament_0" d="M438.067,345L447.152,349.167C456.237,353.333,474.408,361.667,483.493,369.333C492.578,377,492.578,384,492.578,387.5L492.578,391"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Livewire_Controllers_0" d="M244.453,449L244.453,453.167C244.453,457.333,244.453,465.667,244.453,474C244.453,482.333,244.453,490.667,244.453,499C244.453,507.333,244.453,515.667,254.713,523.793C264.972,531.919,285.491,539.838,295.751,543.797L306.01,547.756"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Filament_Controllers_0" d="M492.578,449L492.578,453.167C492.578,457.333,492.578,465.667,492.578,474C492.578,482.333,492.578,490.667,492.578,499C492.578,507.333,492.578,515.667,484.099,523.722C475.62,531.778,458.661,539.555,450.182,543.444L441.703,547.333"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Controllers_Commands_0" d="M448.648,596.05L467.094,601.375C485.539,606.7,522.43,617.35,540.875,626.175C559.32,635,559.32,642,559.32,645.5L559.32,649"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Controllers_Queries_0" d="M309.742,593.225L286.372,599.021C263.003,604.817,216.263,616.408,192.893,630.871C169.523,645.333,169.523,662.667,169.523,680C169.523,697.333,169.523,714.667,169.523,732C169.523,749.333,169.523,766.667,169.523,784C169.523,801.333,169.523,818.667,169.523,830.833C169.523,843,169.523,850,169.523,853.5L169.523,857"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Commands_Events_0" d="M605.195,707L612.275,711.167C619.354,715.333,633.513,723.667,640.592,731.333C647.672,739,647.672,746,647.672,749.5L647.672,753"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Commands_Models_0" d="M490.688,687.405L421.805,694.838C352.922,702.27,215.156,717.135,146.273,733.234C77.391,749.333,77.391,766.667,77.391,784C77.391,801.333,77.391,818.667,77.391,836C77.391,853.333,77.391,870.667,77.391,888C77.391,905.333,77.391,922.667,77.391,935.5C77.391,948.333,77.391,956.667,77.391,965C77.391,973.333,77.391,981.667,77.391,994.5C77.391,1007.333,77.391,1024.667,77.391,1042C77.391,1059.333,77.391,1076.667,78.754,1088.878C80.117,1101.089,82.844,1108.178,84.207,1111.722L85.57,1115.267"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Commands_Services_0" d="M530.172,707L525.674,711.167C521.176,715.333,512.18,723.667,507.682,736.5C503.184,749.333,503.184,766.667,503.184,784C503.184,801.333,503.184,818.667,503.184,836C503.184,853.333,503.184,870.667,503.184,888C503.184,905.333,503.184,922.667,503.184,935.5C503.184,948.333,503.184,956.667,503.184,965C503.184,973.333,503.184,981.667,507.193,989.547C511.202,997.427,519.22,1004.855,523.229,1008.568L527.238,1012.282"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Queries_Models_0" d="M150.797,915L147.907,919.167C145.017,923.333,139.237,931.667,136.347,940C133.457,948.333,133.457,956.667,133.457,965C133.457,973.333,133.457,981.667,133.457,994.5C133.457,1007.333,133.457,1024.667,133.457,1042C133.457,1059.333,133.457,1076.667,130.947,1088.952C128.437,1101.238,123.417,1108.475,120.907,1112.094L118.397,1115.713"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Queries_Repositories_0" d="M191.767,915L195.2,919.167C198.633,923.333,205.498,931.667,208.931,940C212.363,948.333,212.363,956.667,212.363,965C212.363,973.333,212.363,981.667,212.363,994.5C212.363,1007.333,212.363,1024.667,212.363,1042C212.363,1059.333,212.363,1076.667,216.885,1089.075C221.407,1101.483,230.45,1108.967,234.971,1112.708L239.493,1116.45"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Services_Models_0" d="M500.234,1050.022L446.245,1057.351C392.255,1064.681,284.276,1079.341,224.521,1090.47C164.765,1101.6,153.233,1109.199,147.467,1112.999L141.701,1116.799"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Services_Repositories_0" d="M500.234,1057.063L476.087,1063.219C451.939,1069.375,403.643,1081.688,373.633,1091.648C343.623,1101.608,331.897,1109.215,326.035,1113.019L320.172,1116.823"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Services_Policies_0" d="M505.41,1069L497.09,1073.167C488.77,1077.333,472.131,1085.667,463.812,1093.333C455.492,1101,455.492,1108,455.492,1111.5L455.492,1115"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Models_Database_0" d="M97.391,1173L97.391,1177.167C97.391,1181.333,97.391,1189.667,97.391,1198C97.391,1206.333,97.391,1214.667,97.391,1223C97.391,1231.333,97.391,1239.667,104.419,1247.68C111.447,1255.693,125.504,1263.386,132.532,1267.233L139.56,1271.08"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Repositories_Database_0" d="M275.203,1173L275.203,1177.167C275.203,1181.333,275.203,1189.667,275.203,1198C275.203,1206.333,275.203,1214.667,275.203,1223C275.203,1231.333,275.203,1239.667,269.133,1247.645C263.063,1255.624,250.923,1263.248,244.853,1267.061L238.782,1270.873"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Events_Jobs_0" d="M647.672,811L647.672,815.167C647.672,819.333,647.672,827.667,647.672,835.333C647.672,843,647.672,850,647.672,853.5L647.672,857"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Jobs_Services_0" d="M647.672,915L647.672,919.167C647.672,923.333,647.672,931.667,647.672,940C647.672,948.333,647.672,956.667,647.672,965C647.672,973.333,647.672,981.667,641.167,989.662C634.662,997.657,621.652,1005.314,615.147,1009.143L608.642,1012.971"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Services_Cache_0" d="M553.11,1069L552.151,1073.167C551.193,1077.333,549.276,1085.667,548.318,1098.5C547.359,1111.333,547.359,1128.667,547.359,1146C547.359,1163.333,547.359,1180.667,547.359,1193.5C547.359,1206.333,547.359,1214.667,547.359,1223C547.359,1231.333,547.359,1239.667,547.359,1247.333C547.359,1255,547.359,1262,547.359,1265.5L547.359,1269"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Services_Queue_0" d="M618.406,1058.629L639.353,1064.524C660.299,1070.419,702.193,1082.21,723.139,1096.772C744.086,1111.333,744.086,1128.667,744.086,1146C744.086,1163.333,744.086,1180.667,744.086,1193.5C744.086,1206.333,744.086,1214.667,744.086,1223C744.086,1231.333,744.086,1239.667,744.086,1247.333C744.086,1255,744.086,1262,744.086,1265.5L744.086,1269"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Services_Storage_0" d="M618.406,1050.167L671.257,1057.473C724.107,1064.778,829.807,1079.389,882.658,1095.361C935.508,1111.333,935.508,1128.667,935.508,1146C935.508,1163.333,935.508,1180.667,935.508,1193.5C935.508,1206.333,935.508,1214.667,935.508,1223C935.508,1231.333,935.508,1239.667,935.508,1247.333C935.508,1255,935.508,1262,935.508,1265.5L935.508,1269"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Services_Search_0" d="M618.406,1047.449L702.535,1055.207C786.664,1062.966,954.922,1078.483,1039.051,1094.908C1123.18,1111.333,1123.18,1128.667,1123.18,1146C1123.18,1163.333,1123.18,1180.667,1123.18,1193.5C1123.18,1206.333,1123.18,1214.667,1123.18,1223C1123.18,1231.333,1123.18,1239.667,1123.18,1247.333C1123.18,1255,1123.18,1262,1123.18,1265.5L1123.18,1269"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(146.80078125, 60)" id="flowchart-Browser-0" class="node default"><rect height="54" width="152.546875" y="-27" x="-76.2734375" style="" class="basic label-container"/><g transform="translate(-46.2734375, -12)" style="" class="label"><rect/><foreignObject height="24" width="92.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Web Browser</p></span></div></foreignObject></g></g><g transform="translate(342.10546875, 60)" id="flowchart-MobileApp-1" class="node default"><rect height="54" width="138.0625" y="-27" x="-69.03125" style="" class="basic label-container"/><g transform="translate(-39.03125, -12)" style="" class="label"><rect/><foreignObject height="24" width="78.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Mobile App</p></span></div></foreignObject></g></g><g transform="translate(529.52734375, 60)" id="flowchart-API-2" class="node default"><rect height="54" width="136.78125" y="-27" x="-68.390625" style="" class="basic label-container"/><g transform="translate(-38.390625, -12)" style="" class="label"><rect/><foreignObject height="24" width="76.78125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>API Clients</p></span></div></foreignObject></g></g><g transform="translate(379.1953125, 214)" id="flowchart-FrankenPHP-3" class="node default"><rect height="54" width="145.625" y="-27" x="-72.8125" style="" class="basic label-container"/><g transform="translate(-42.8125, -12)" style="" class="label"><rect/><foreignObject height="24" width="85.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FrankenPHP</p></span></div></foreignObject></g></g><g transform="translate(379.1953125, 318)" id="flowchart-Laravel-4" class="node default"><rect height="54" width="134.015625" y="-27" x="-67.0078125" style="" class="basic label-container"/><g transform="translate(-37.0078125, -12)" style="" class="label"><rect/><foreignObject height="24" width="74.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Laravel 12</p></span></div></foreignObject></g></g><g transform="translate(244.453125, 422)" id="flowchart-Livewire-5" class="node default"><rect height="54" width="157.046875" y="-27" x="-78.5234375" style="" class="basic label-container"/><g transform="translate(-48.5234375, -12)" style="" class="label"><rect/><foreignObject height="24" width="97.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Livewire/Volt</p></span></div></foreignObject></g></g><g transform="translate(492.578125, 422)" id="flowchart-Filament-6" class="node default"><rect height="54" width="172.0625" y="-27" x="-86.03125" style="" class="basic label-container"/><g transform="translate(-56.03125, -12)" style="" class="label"><rect/><foreignObject height="24" width="112.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Filament Admin</p></span></div></foreignObject></g></g><g transform="translate(379.1953125, 576)" id="flowchart-Controllers-7" class="node default"><rect height="54" width="138.90625" y="-27" x="-69.453125" style="" class="basic label-container"/><g transform="translate(-39.453125, -12)" style="" class="label"><rect/><foreignObject height="24" width="78.90625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Controllers</p></span></div></foreignObject></g></g><g transform="translate(559.3203125, 680)" id="flowchart-Commands-8" class="node default"><rect height="54" width="137.265625" y="-27" x="-68.6328125" style="" class="basic label-container"/><g transform="translate(-38.6328125, -12)" style="" class="label"><rect/><foreignObject height="24" width="77.265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Commands</p></span></div></foreignObject></g></g><g transform="translate(169.5234375, 888)" id="flowchart-Queries-9" class="node default"><rect height="54" width="114.265625" y="-27" x="-57.1328125" style="" class="basic label-container"/><g transform="translate(-27.1328125, -12)" style="" class="label"><rect/><foreignObject height="24" width="54.265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Queries</p></span></div></foreignObject></g></g><g transform="translate(647.671875, 784)" id="flowchart-Events-10" class="node default"><rect height="54" width="106.703125" y="-27" x="-53.3515625" style="" class="basic label-container"/><g transform="translate(-23.3515625, -12)" style="" class="label"><rect/><foreignObject height="24" width="46.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Events</p></span></div></foreignObject></g></g><g transform="translate(647.671875, 888)" id="flowchart-Jobs-11" class="node default"><rect height="54" width="91.609375" y="-27" x="-45.8046875" style="" class="basic label-container"/><g transform="translate(-15.8046875, -12)" style="" class="label"><rect/><foreignObject height="24" width="31.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Jobs</p></span></div></foreignObject></g></g><g transform="translate(97.390625, 1146)" id="flowchart-Models-12" class="node default"><rect height="54" width="108.78125" y="-27" x="-54.390625" style="" class="basic label-container"/><g transform="translate(-24.390625, -12)" style="" class="label"><rect/><foreignObject height="24" width="48.78125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Models</p></span></div></foreignObject></g></g><g transform="translate(559.3203125, 1042)" id="flowchart-Services-13" class="node default"><rect height="54" width="118.171875" y="-27" x="-59.0859375" style="" class="basic label-container"/><g transform="translate(-29.0859375, -12)" style="" class="label"><rect/><foreignObject height="24" width="58.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Services</p></span></div></foreignObject></g></g><g transform="translate(275.203125, 1146)" id="flowchart-Repositories-14" class="node default"><rect height="54" width="146.84375" y="-27" x="-73.421875" style="" class="basic label-container"/><g transform="translate(-43.421875, -12)" style="" class="label"><rect/><foreignObject height="24" width="86.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Repositories</p></span></div></foreignObject></g></g><g transform="translate(455.4921875, 1146)" id="flowchart-Policies-15" class="node default"><rect height="54" width="113.734375" y="-27" x="-56.8671875" style="" class="basic label-container"/><g transform="translate(-26.8671875, -12)" style="" class="label"><rect/><foreignObject height="24" width="53.734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Policies</p></span></div></foreignObject></g></g><g transform="translate(192.40234375, 1300)" id="flowchart-Database-16" class="node default"><rect height="54" width="139.171875" y="-27" x="-69.5859375" style="" class="basic label-container"/><g transform="translate(-39.5859375, -12)" style="" class="label"><rect/><foreignObject height="24" width="79.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PostgreSQL</p></span></div></foreignObject></g></g><g transform="translate(547.359375, 1300)" id="flowchart-Cache-17" class="node default"><rect height="54" width="145.53125" y="-27" x="-72.765625" style="" class="basic label-container"/><g transform="translate(-42.765625, -12)" style="" class="label"><rect/><foreignObject height="24" width="85.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Redis Cache</p></span></div></foreignObject></g></g><g transform="translate(744.0859375, 1300)" id="flowchart-Queue-18" class="node default"><rect height="54" width="147.921875" y="-27" x="-73.9609375" style="" class="basic label-container"/><g transform="translate(-43.9609375, -12)" style="" class="label"><rect/><foreignObject height="24" width="87.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Redis Queue</p></span></div></foreignObject></g></g><g transform="translate(935.5078125, 1300)" id="flowchart-Storage-19" class="node default"><rect height="54" width="134.921875" y="-27" x="-67.4609375" style="" class="basic label-container"/><g transform="translate(-37.4609375, -12)" style="" class="label"><rect/><foreignObject height="24" width="74.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>S3 Storage</p></span></div></foreignObject></g></g><g transform="translate(1123.1796875, 1300)" id="flowchart-Search-20" class="node default"><rect height="54" width="140.421875" y="-27" x="-70.2109375" style="" class="basic label-container"/><g transform="translate(-40.2109375, -12)" style="" class="label"><rect/><foreignObject height="24" width="80.421875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Meilisearch</p></span></div></foreignObject></g></g></g></g></g></svg>