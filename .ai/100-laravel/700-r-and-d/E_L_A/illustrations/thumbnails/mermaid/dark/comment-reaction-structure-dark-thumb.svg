<svg aria-roledescription="class" role="graphics-document document" viewBox="0 0 1875.44140625 1572" style="max-width: 1875.44px; background-color: rgb(40, 44, 52);" class="classDiagram" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#a44141;}#my-svg .error-text{fill:#ddd;stroke:#ddd;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#ecf0f1;stroke:#ecf0f1;}#my-svg .marker.cross{stroke:#ecf0f1;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg g.classGroup text{fill:#ccc;stroke:none;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:10px;}#my-svg g.classGroup text .title{font-weight:bolder;}#my-svg .nodeLabel,#my-svg .edgeLabel{color:#ecf0f1;}#my-svg .edgeLabel .label rect{fill:#1f2020;}#my-svg .label text{fill:#ecf0f1;}#my-svg .labelBkg{background:#1f2020;}#my-svg .edgeLabel .label span{background:#1f2020;}#my-svg .classTitle{font-weight:bolder;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#my-svg .divider{stroke:#ccc;stroke-width:1;}#my-svg g.clickable{cursor:pointer;}#my-svg g.classGroup rect{fill:#1f2020;stroke:#ccc;}#my-svg g.classGroup line{stroke:#ccc;stroke-width:1;}#my-svg .classLabel .box{stroke:none;stroke-width:0;fill:#1f2020;opacity:0.5;}#my-svg .classLabel .label{fill:#ccc;font-size:10px;}#my-svg .relation{stroke:#ecf0f1;stroke-width:1;fill:none;}#my-svg .dashed-line{stroke-dasharray:3;}#my-svg .dotted-line{stroke-dasharray:1 2;}#my-svg #compositionStart,#my-svg .composition{fill:#ecf0f1!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg #compositionEnd,#my-svg .composition{fill:#ecf0f1!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg #dependencyStart,#my-svg .dependency{fill:#ecf0f1!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg #dependencyStart,#my-svg .dependency{fill:#ecf0f1!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg #extensionStart,#my-svg .extension{fill:transparent!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg #extensionEnd,#my-svg .extension{fill:transparent!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg #aggregationStart,#my-svg .aggregation{fill:transparent!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg #aggregationEnd,#my-svg .aggregation{fill:transparent!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg #lollipopStart,#my-svg .lollipop{fill:#1f2020!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg #lollipopEnd,#my-svg .lollipop{fill:#1f2020!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg .edgeTerminals{font-size:11px;line-height:initial;}#my-svg .classTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="18" class="marker aggregation class" id="my-svg_class-aggregationStart"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="1" class="marker aggregation class" id="my-svg_class-aggregationEnd"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="18" class="marker extension class" id="my-svg_class-extensionStart"><path d="M 1,7 L18,13 V 1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="1" class="marker extension class" id="my-svg_class-extensionEnd"><path d="M 1,1 V 13 L18,7 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="18" class="marker composition class" id="my-svg_class-compositionStart"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="1" class="marker composition class" id="my-svg_class-compositionEnd"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="6" class="marker dependency class" id="my-svg_class-dependencyStart"><path d="M 5,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="13" class="marker dependency class" id="my-svg_class-dependencyEnd"><path d="M 18,7 L9,13 L14,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="13" class="marker lollipop class" id="my-svg_class-lollipopStart"><circle r="6" cy="7" cx="7" fill="transparent" stroke="black"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="1" class="marker lollipop class" id="my-svg_class-lollipopEnd"><circle r="6" cy="7" cx="7" fill="transparent" stroke="black"/></marker></defs><g class="root"><g class="clusters"/><g class="edgePaths"><path marker-start="url(#my-svg_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_CommentState_Pending_1" d="M1567.516,1330.097L1590.11,1340.914C1612.704,1351.731,1657.891,1373.366,1680.485,1389.849C1703.078,1406.333,1703.078,1417.667,1703.078,1423.333L1703.078,1429"/><path marker-start="url(#my-svg_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_CommentState_Approved_2" d="M1355.472,1351.173L1349.753,1358.478C1344.035,1365.782,1332.597,1380.391,1326.879,1393.362C1321.16,1406.333,1321.16,1417.667,1321.16,1423.333L1321.16,1429"/><path marker-start="url(#my-svg_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_CommentState_Rejected_3" d="M1281.922,1301.064L1224.466,1316.72C1167.011,1332.376,1052.099,1363.688,994.643,1383.511C937.188,1403.333,937.188,1411.667,937.188,1415.833L937.188,1420"/><path marker-start="url(#my-svg_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_CommentState_Deleted_4" d="M1281.496,1284.014L1160.673,1302.511C1039.85,1321.009,798.205,1358.005,677.382,1380.669C556.559,1403.333,556.559,1411.667,556.559,1415.833L556.559,1420"/><path marker-start="url(#my-svg_class-compositionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Comment_CommentState_5" d="M1161.864,964.702L1205.768,990.085C1249.671,1015.468,1337.478,1066.234,1381.382,1103.284C1425.285,1140.333,1425.285,1163.667,1425.285,1175.333L1425.285,1187"/><path marker-start="url(#my-svg_class-compositionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Comment_CommentReaction_6" d="M1074.554,1097.246L1075.538,1100.539C1076.522,1103.831,1078.489,1110.415,1079.473,1119.874C1080.457,1129.333,1080.457,1141.667,1080.457,1147.833L1080.457,1154"/><path marker-end="url(#my-svg_class-aggregationEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Comment_User_7" d="M870.602,1027.708L857.08,1042.59C843.559,1057.472,816.516,1087.236,788.265,1114.524C760.014,1141.811,730.555,1166.622,715.825,1179.027L701.096,1191.433"/><path marker-end="url(#my-svg_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-dashed relation" id="id_CommentAggregateRoot_Comment_8" d="M766.857,598L777.771,604.167C788.684,610.333,810.511,622.667,827.212,636.753C843.912,650.84,855.487,666.68,861.274,674.6L867.062,682.52"/><path marker-end="url(#my-svg_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-dashed relation" id="id_CommentAggregateRoot_CommentReaction_9" d="M485.469,598L485.469,604.167C485.469,610.333,485.469,622.667,485.469,669C485.469,715.333,485.469,795.667,485.469,876C485.469,956.333,485.469,1036.667,562.138,1095.518C638.808,1154.369,792.148,1191.738,868.817,1210.423L945.487,1229.107"/><path marker-start="url(#my-svg_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-dashed relation" id="id_Commentable_Post_10" d="M1284.525,216.547L1290.622,220.956C1296.72,225.365,1308.915,234.182,1315.012,257.258C1321.109,280.333,1321.109,317.667,1321.109,336.333L1321.109,355"/><path marker-start="url(#my-svg_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-dashed relation" id="id_Commentable_Todo_11" d="M1107.66,223.589L1106.956,226.824C1106.253,230.059,1104.845,236.53,1104.141,258.431C1103.438,280.333,1103.438,317.667,1103.438,336.333L1103.438,355"/><path marker-end="url(#my-svg_class-aggregationEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Post_Comment_12" d="M1321.109,523L1321.109,541.667C1321.109,560.333,1321.109,597.667,1294.347,636.961C1267.586,676.255,1214.062,717.511,1187.3,738.139L1160.538,758.766"/><path marker-end="url(#my-svg_class-aggregationEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Todo_Comment_13" d="M1103.438,523L1103.438,541.667C1103.438,560.333,1103.438,597.667,1102.107,619.709C1100.776,641.751,1098.115,648.503,1096.784,651.878L1095.454,655.254"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g transform="translate(1425.28515625, 1117)" class="edgeLabel"><g transform="translate(-32.375, -12)" class="label"><foreignObject height="24" width="64.75"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>has state</p></span></div></foreignObject></g></g><g transform="translate(1080.45703125, 1117)" class="edgeLabel"><g transform="translate(-47.21875, -12)" class="label"><foreignObject height="24" width="94.4375"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>has reactions</p></span></div></foreignObject></g></g><g transform="translate(789.47265625, 1117)" class="edgeLabel"><g transform="translate(-43.15625, -12)" class="label"><foreignObject height="24" width="86.3125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>authored by</p></span></div></foreignObject></g></g><g transform="translate(832.337890625, 635)" class="edgeLabel"><g transform="translate(-58.8671875, -12)" class="label"><foreignObject height="24" width="117.734375"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>creates/updates</p></span></div></foreignObject></g></g><g transform="translate(485.46875, 876)" class="edgeLabel"><g transform="translate(-31.0390625, -12)" class="label"><foreignObject height="24" width="62.078125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>manages</p></span></div></foreignObject></g></g><g transform="translate(1321.109375, 243)" class="edgeLabel"><g transform="translate(-41.890625, -12)" class="label"><foreignObject height="24" width="83.78125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>implements</p></span></div></foreignObject></g></g><g transform="translate(1103.4375, 243)" class="edgeLabel"><g transform="translate(-41.890625, -12)" class="label"><foreignObject height="24" width="83.78125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>implements</p></span></div></foreignObject></g></g><g transform="translate(1321.109375, 635)" class="edgeLabel"><g transform="translate(-50.90625, -12)" class="label"><foreignObject height="24" width="101.8125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>has comments</p></span></div></foreignObject></g></g><g transform="translate(1103.4375, 635)" class="edgeLabel"><g transform="translate(-50.90625, -12)" class="label"><foreignObject height="24" width="101.8125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>has comments</p></span></div></foreignObject></g></g><g transform="translate(1153.923586332473, 977.4376861416151)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1060.039072185961, 1101.0620534238903)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(847.7314813519198, 1030.573620916728)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 36px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">0..*</span></div></foreignObject></g></g><g transform="translate(1306.1093775000002, 540.5000021428572)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1088.4375, 540.5)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1435.285158125, 1164.5000016071428)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g><g transform="translate(1090.457030625, 1131.4999994642858)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 36px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">0..*</span></div></foreignObject></g><g transform="translate(705.3761193542898, 1198.2279025394087)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g><g transform="translate(1164.299012594475, 765.9519714233454)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 36px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">0..*</span></div></foreignObject></g><g transform="translate(1104.2255392373622, 656.2198709982066)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 36px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">0..*</span></div></foreignObject></g></g><g class="nodes"><g transform="translate(1133.021484375, 107)" id="classId-Commentable-0" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-175.55078125 -99 L175.55078125 -99 L175.55078125 99 L-175.55078125 99"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-175.55078125 -99 C-51.28923850539884 -99, 72.97230423920232 -99, 175.55078125 -99 M-175.55078125 -99 C-37.19068052154594 -99, 101.16942020690811 -99, 175.55078125 -99 M175.55078125 -99 C175.55078125 -54.709189478691464, 175.55078125 -10.418378957382927, 175.55078125 99 M175.55078125 -99 C175.55078125 -57.630197775427725, 175.55078125 -16.26039555085545, 175.55078125 99 M175.55078125 99 C79.95095440459673 99, -15.648872440806542 99, -175.55078125 99 M175.55078125 99 C70.2742267767806 99, -35.00232769643881 99, -175.55078125 99 M-175.55078125 99 C-175.55078125 31.462281451331947, -175.55078125 -36.075437097336106, -175.55078125 -99 M-175.55078125 99 C-175.55078125 27.90469184517343, -175.55078125 -43.19061630965314, -175.55078125 -99"/></g><g transform="translate(-41.171875, -75)" class="annotation-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="82.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 122px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>«interface»</p></span></div></foreignObject></g></g><g transform="translate(-51.5390625, -51)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="103.078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 140px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Commentable</p></span></div></foreignObject></g></g><g transform="translate(-163.55078125, -3)" class="members-group text"/><g transform="translate(-163.55078125, 27)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="231.890625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 266px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+commentsAreEnabled() : : bool</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="275.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 309px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+commentsAreAutoApproved() : : bool</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="275.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 308px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+commentsAreReactionsOnly() : : bool</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-175.55078125 -27 C-74.85827382093653 -27, 25.834233608126937 -27, 175.55078125 -27 M-175.55078125 -27 C-35.583799916947584 -27, 104.38318141610483 -27, 175.55078125 -27"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-175.55078125 -3 C-105.06778201916617 -3, -34.58478278833235 -3, 175.55078125 -3 M-175.55078125 -3 C-41.348308087709455 -3, 92.85416507458109 -3, 175.55078125 -3"/></g></g><g transform="translate(1425.28515625, 1262)" id="classId-CommentState-1" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-125.99609375 -75 L125.99609375 -75 L125.99609375 75 L-125.99609375 75"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-125.99609375 -75 C-61.25879727468592 -75, 3.478499200628164 -75, 125.99609375 -75 M-125.99609375 -75 C-64.5564677967013 -75, -3.1168418434025966 -75, 125.99609375 -75 M125.99609375 -75 C125.99609375 -42.7497552611361, 125.99609375 -10.499510522272203, 125.99609375 75 M125.99609375 -75 C125.99609375 -28.783642255527973, 125.99609375 17.432715488944055, 125.99609375 75 M125.99609375 75 C49.49658941794988 75, -27.002914914100245 75, -125.99609375 75 M125.99609375 75 C54.12320584626292 75, -17.74968205747416 75, -125.99609375 75 M-125.99609375 75 C-125.99609375 41.290622005427075, -125.99609375 7.5812440108541495, -125.99609375 -75 M-125.99609375 75 C-125.99609375 30.20405187938841, -125.99609375 -14.591896241223182, -125.99609375 -75"/></g><g transform="translate(-37.90625, -51)" class="annotation-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="75.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 116px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>«abstract»</p></span></div></foreignObject></g></g><g transform="translate(-54.9609375, -27)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="109.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 145px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>CommentState</p></span></div></foreignObject></g></g><g transform="translate(-113.99609375, 21)" class="members-group text"/><g transform="translate(-113.99609375, 51)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="173.03125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 212px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+config() : : StateConfig</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-125.99609375 -3 C-60.65688819875183 -3, 4.682317352496341 -3, 125.99609375 -3 M-125.99609375 -3 C-33.697774542623094 -3, 58.60054466475381 -3, 125.99609375 -3"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-125.99609375 21 C-71.91187397439427 21, -17.827654198788522 21, 125.99609375 21 M-125.99609375 21 C-60.06873725352166 21, 5.858619242956678 21, 125.99609375 21"/></g></g><g transform="translate(1703.078125, 1492)" id="classId-Pending-2" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-164.36328125 -63 L164.36328125 -63 L164.36328125 63 L-164.36328125 63"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-164.36328125 -63 C-36.27352174972401 -63, 91.81623775055198 -63, 164.36328125 -63 M-164.36328125 -63 C-72.4781311111183 -63, 19.407019027763397 -63, 164.36328125 -63 M164.36328125 -63 C164.36328125 -35.23167392058585, 164.36328125 -7.463347841171689, 164.36328125 63 M164.36328125 -63 C164.36328125 -17.805353668850472, 164.36328125 27.389292662299056, 164.36328125 63 M164.36328125 63 C59.36218914920683 63, -45.638902951586346 63, -164.36328125 63 M164.36328125 63 C54.319595815476674 63, -55.72408961904665 63, -164.36328125 63 M-164.36328125 63 C-164.36328125 31.5896302872342, -164.36328125 0.17926057446840105, -164.36328125 -63 M-164.36328125 63 C-164.36328125 22.900616812204724, -164.36328125 -17.19876637559055, -164.36328125 -63"/></g><g transform="translate(0, -39)" class="annotation-group text"/><g transform="translate(-29.4140625, -39)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="58.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 102px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Pending</p></span></div></foreignObject></g></g><g transform="translate(-152.36328125, 9)" class="members-group text"/><g transform="translate(-152.36328125, 39)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="275.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 301px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+canTransitionTo(State $state) : : bool</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-164.36328125 -15 C-98.15647607226113 -15, -31.949670894522257 -15, 164.36328125 -15 M-164.36328125 -15 C-54.10069685316948 -15, 56.16188754366104 -15, 164.36328125 -15"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-164.36328125 9 C-84.48344326423351 9, -4.603605278467029 9, 164.36328125 9 M-164.36328125 9 C-68.49272017808178 9, 27.377840893836435 9, 164.36328125 9"/></g></g><g transform="translate(1321.16015625, 1492)" id="classId-Approved-3" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-167.5546875 -63 L167.5546875 -63 L167.5546875 63 L-167.5546875 63"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-167.5546875 -63 C-58.404713497805986 -63, 50.74526050438803 -63, 167.5546875 -63 M-167.5546875 -63 C-97.46657940694116 -63, -27.37847131388233 -63, 167.5546875 -63 M167.5546875 -63 C167.5546875 -23.861027400782184, 167.5546875 15.277945198435631, 167.5546875 63 M167.5546875 -63 C167.5546875 -28.781124442092995, 167.5546875 5.4377511158140095, 167.5546875 63 M167.5546875 63 C51.578507932229385 63, -64.39767163554123 63, -167.5546875 63 M167.5546875 63 C62.22875267655088 63, -43.097182146898234 63, -167.5546875 63 M-167.5546875 63 C-167.5546875 16.685871174366405, -167.5546875 -29.62825765126719, -167.5546875 -63 M-167.5546875 63 C-167.5546875 36.44804997207444, -167.5546875 9.89609994414888, -167.5546875 -63"/></g><g transform="translate(0, -39)" class="annotation-group text"/><g transform="translate(-35.796875, -39)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="71.59375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 114px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Approved</p></span></div></foreignObject></g></g><g transform="translate(-155.5546875, 9)" class="members-group text"/><g transform="translate(-155.5546875, 39)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="275.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 301px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+canTransitionTo(State $state) : : bool</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-167.5546875 -15 C-91.94157049648815 -15, -16.32845349297631 -15, 167.5546875 -15 M-167.5546875 -15 C-38.68434978905822 -15, 90.18598792188357 -15, 167.5546875 -15"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-167.5546875 9 C-70.34776695333214 9, 26.85915359333572 9, 167.5546875 9 M-167.5546875 9 C-86.50967322104106 9, -5.46465894208211 9, 167.5546875 9"/></g></g><g transform="translate(937.1875, 1492)" id="classId-Rejected-4" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-166.41796875 -72 L166.41796875 -72 L166.41796875 72 L-166.41796875 72"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-166.41796875 -72 C-60.943179820524065 -72, 44.53160910895187 -72, 166.41796875 -72 M-166.41796875 -72 C-56.961493457464925 -72, 52.49498183507015 -72, 166.41796875 -72 M166.41796875 -72 C166.41796875 -27.195749353768733, 166.41796875 17.608501292462535, 166.41796875 72 M166.41796875 -72 C166.41796875 -19.19254542338966, 166.41796875 33.61490915322068, 166.41796875 72 M166.41796875 72 C76.4901146131728 72, -13.437739523654386 72, -166.41796875 72 M166.41796875 72 C88.4495270141213 72, 10.481085278242602 72, -166.41796875 72 M-166.41796875 72 C-166.41796875 42.79005906931933, -166.41796875 13.58011813863866, -166.41796875 -72 M-166.41796875 72 C-166.41796875 34.58039059871201, -166.41796875 -2.839218802575985, -166.41796875 -72"/></g><g transform="translate(0, -48)" class="annotation-group text"/><g transform="translate(-33.5234375, -48)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="67.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 106px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Rejected</p></span></div></foreignObject></g></g><g transform="translate(-154.41796875, 0)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="174.765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 211px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+rejectionReason: string</p></span></div></foreignObject></g></g><g transform="translate(-154.41796875, 48)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="275.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 301px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+canTransitionTo(State $state) : : bool</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-166.41796875 -24 C-34.02199445000227 -24, 98.37397984999546 -24, 166.41796875 -24 M-166.41796875 -24 C-54.74070846210485 -24, 56.9365518257903 -24, 166.41796875 -24"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-166.41796875 24 C-96.73572501523189 24, -27.053481280463785 24, 166.41796875 24 M-166.41796875 24 C-43.23621328555592 24, 79.94554217888816 24, 166.41796875 24"/></g></g><g transform="translate(556.55859375, 1492)" id="classId-Deleted-5" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-164.2109375 -72 L164.2109375 -72 L164.2109375 72 L-164.2109375 72"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-164.2109375 -72 C-56.425581662398955 -72, 51.35977417520209 -72, 164.2109375 -72 M-164.2109375 -72 C-80.1348754038264 -72, 3.941186692347202 -72, 164.2109375 -72 M164.2109375 -72 C164.2109375 -20.355895675470677, 164.2109375 31.288208649058646, 164.2109375 72 M164.2109375 -72 C164.2109375 -25.678612437278893, 164.2109375 20.642775125442213, 164.2109375 72 M164.2109375 72 C36.398968847370455 72, -91.41299980525909 72, -164.2109375 72 M164.2109375 72 C93.53466185250015 72, 22.8583862050003 72, -164.2109375 72 M-164.2109375 72 C-164.2109375 31.663126277478042, -164.2109375 -8.673747445043915, -164.2109375 -72 M-164.2109375 72 C-164.2109375 17.504132917291813, -164.2109375 -36.991734165416375, -164.2109375 -72"/></g><g transform="translate(0, -48)" class="annotation-group text"/><g transform="translate(-29.109375, -48)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="58.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Deleted</p></span></div></foreignObject></g></g><g transform="translate(-152.2109375, 0)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="158.421875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 198px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+deletedAt: DateTime</p></span></div></foreignObject></g></g><g transform="translate(-152.2109375, 48)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="275.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 301px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+canTransitionTo(State $state) : : bool</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-164.2109375 -24 C-85.41827040215632 -24, -6.625603304312648 -24, 164.2109375 -24 M-164.2109375 -24 C-57.322912285827144 -24, 49.56511292834571 -24, 164.2109375 -24"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-164.2109375 24 C-35.04393258576155 24, 94.1230723284769 24, 164.2109375 24 M-164.2109375 24 C-50.18041713389516 24, 63.850103232209676 24, 164.2109375 24"/></g></g><g transform="translate(1008.44140625, 876)" id="classId-Comment-6" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-137.83984375 -204 L137.83984375 -204 L137.83984375 204 L-137.83984375 204"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-137.83984375 -204 C-70.36498133721432 -204, -2.8901189244286343 -204, 137.83984375 -204 M-137.83984375 -204 C-37.4211449061315 -204, 62.997553937736996 -204, 137.83984375 -204 M137.83984375 -204 C137.83984375 -105.19034827079724, 137.83984375 -6.380696541594489, 137.83984375 204 M137.83984375 -204 C137.83984375 -83.89902172658506, 137.83984375 36.20195654682988, 137.83984375 204 M137.83984375 204 C30.305991481756323 204, -77.22786078648735 204, -137.83984375 204 M137.83984375 204 C33.4777470065181 204, -70.8843497369638 204, -137.83984375 204 M-137.83984375 204 C-137.83984375 54.4934894948249, -137.83984375 -95.0130210103502, -137.83984375 -204 M-137.83984375 204 C-137.83984375 63.66431583943961, -137.83984375 -76.67136832112078, -137.83984375 -204"/></g><g transform="translate(0, -180)" class="annotation-group text"/><g transform="translate(-35.6640625, -180)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="71.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 113px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Comment</p></span></div></foreignObject></g></g><g transform="translate(-125.83984375, -132)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="72.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+id: string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="114.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 155px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+content: string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="111.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 155px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+user_id: string</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="197.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 230px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+commentable_type: string</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="178.984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 215px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+commentable_id: string</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="164"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 197px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+parent_id: string|null</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="161.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 196px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+state: CommentState</p></span></div></foreignObject></g><g transform="translate(0,156)" style="" class="label"><foreignObject height="24" width="212.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 243px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+approved_at: DateTime|null</p></span></div></foreignObject></g><g transform="translate(0,180)" style="" class="label"><foreignObject height="24" width="207.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 234px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+rejected_at: DateTime|null</p></span></div></foreignObject></g><g transform="translate(0,204)" style="" class="label"><foreignObject height="24" width="216.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 242px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+rejection_reason: string|null</p></span></div></foreignObject></g></g><g transform="translate(-125.83984375, 132)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="133.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 175px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+approve() : : void</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="212.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 241px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+reject(string reason) : : void</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="122.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 162px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+delete() : : void</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-137.83984375 -156 C-81.4435375132033 -156, -25.047231276406578 -156, 137.83984375 -156 M-137.83984375 -156 C-63.59281783492219 -156, 10.654208080155627 -156, 137.83984375 -156"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-137.83984375 108 C-59.7022372216737 108, 18.435369306652603 108, 137.83984375 108 M-137.83984375 108 C-76.69408429201184 108, -15.548324834023674 108, 137.83984375 108"/></g></g><g transform="translate(1080.45703125, 1262)" id="classId-CommentReaction-7" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-129.140625 -108 L129.140625 -108 L129.140625 108 L-129.140625 108"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-129.140625 -108 C-45.98226506564768 -108, 37.17609486870464 -108, 129.140625 -108 M-129.140625 -108 C-64.52564813110385 -108, 0.08932873779230022 -108, 129.140625 -108 M129.140625 -108 C129.140625 -23.864039790226684, 129.140625 60.27192041954663, 129.140625 108 M129.140625 -108 C129.140625 -28.03236919082603, 129.140625 51.93526161834794, 129.140625 108 M129.140625 108 C47.607167299307804 108, -33.92629040138439 108, -129.140625 108 M129.140625 108 C36.873619337514626 108, -55.39338632497075 108, -129.140625 108 M-129.140625 108 C-129.140625 22.26635025835806, -129.140625 -63.46729948328388, -129.140625 -108 M-129.140625 108 C-129.140625 37.53930231217723, -129.140625 -32.921395375645545, -129.140625 -108"/></g><g transform="translate(0, -84)" class="annotation-group text"/><g transform="translate(-68.3125, -84)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="136.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 170px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>CommentReaction</p></span></div></foreignObject></g></g><g transform="translate(-117.140625, -36)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="72.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+id: string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="148.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 188px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+comment_id: string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="111.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 155px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+user_id: string</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="91.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+type: string</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="165.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 201px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+created_at: DateTime</p></span></div></foreignObject></g></g><g transform="translate(-117.140625, 108)" class="methods-group text"/><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-129.140625 -60 C-56.76406086324998 -60, 15.612503273500039 -60, 129.140625 -60 M-129.140625 -60 C-56.477331985039754 -60, 16.18596102992049 -60, 129.140625 -60"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-129.140625 84 C-45.96345933597017 84, 37.21370632805966 84, 129.140625 84 M-129.140625 84 C-57.2949005043031 84, 14.550823991393798 84, 129.140625 84"/></g></g><g transform="translate(485.46875, 439)" id="classId-CommentAggregateRoot-8" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-477.46875 -159 L477.46875 -159 L477.46875 159 L-477.46875 159"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-477.46875 -159 C-154.70106267635435 -159, 168.0666246472913 -159, 477.46875 -159 M-477.46875 -159 C-111.91759825120528 -159, 253.63355349758945 -159, 477.46875 -159 M477.46875 -159 C477.46875 -90.44848637720864, 477.46875 -21.896972754417277, 477.46875 159 M477.46875 -159 C477.46875 -66.41100672556962, 477.46875 26.17798654886076, 477.46875 159 M477.46875 159 C253.7054576346796 159, 29.94216526935918 159, -477.46875 159 M477.46875 159 C280.5409561925825 159, 83.61316238516503 159, -477.46875 159 M-477.46875 159 C-477.46875 93.47961666636942, -477.46875 27.959233332738847, -477.46875 -159 M-477.46875 159 C-477.46875 70.47120133339182, -477.46875 -18.057597333216364, -477.46875 -159"/></g><g transform="translate(0, -135)" class="annotation-group text"/><g transform="translate(-89.9375, -135)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="179.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 211px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>CommentAggregateRoot</p></span></div></foreignObject></g></g><g transform="translate(-465.46875, -87)" class="members-group text"/><g transform="translate(-465.46875, -57)" class="methods-group text"><g transform="translate(0,-24)" style="" class="label"><foreignObject height="48" width="841"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 841px; text-align: center; width: 841px;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+createComment(string content, string userId, string commentableType, string commentableId, string|null parentId) : : self</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="291.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 313px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+updateComment(string content) : : self</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="198.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 234px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+approveComment() : : self</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="276.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 301px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+rejectComment(string reason) : : self</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="186.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 221px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+deleteComment() : : self</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="336.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 356px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+addReaction(string type, string userId) : : self</p></span></div></foreignObject></g><g transform="translate(0,156)" style="" class="label"><foreignObject height="24" width="364.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 381px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+removeReaction(string type, string userId) : : self</p></span></div></foreignObject></g><g transform="translate(0,180)" style="" class="label"><foreignObject height="24" width="387.421875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 414px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+configureReactionsOnly(bool isReactionsOnly) : : self</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-477.46875 -111 C-258.62319587766086 -111, -39.777641755321724 -111, 477.46875 -111 M-477.46875 -111 C-244.58040698238207 -111, -11.692063964764145 -111, 477.46875 -111"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-477.46875 -87 C-232.3383101926878 -87, 12.7921296146244 -87, 477.46875 -87 M-477.46875 -87 C-277.142658996563 -87, -76.81656799312606 -87, 477.46875 -87"/></g></g><g transform="translate(617.30859375, 1262)" id="classId-User-9" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-70.01953125 -84 L70.01953125 -84 L70.01953125 84 L-70.01953125 84"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-70.01953125 -84 C-25.424052722842823 -84, 19.171425804314353 -84, 70.01953125 -84 M-70.01953125 -84 C-31.082241455856014 -84, 7.855048338287972 -84, 70.01953125 -84 M70.01953125 -84 C70.01953125 -48.57215518431745, 70.01953125 -13.144310368634905, 70.01953125 84 M70.01953125 -84 C70.01953125 -37.765051269198594, 70.01953125 8.469897461602812, 70.01953125 84 M70.01953125 84 C38.35472572927692 84, 6.6899202085538505 84, -70.01953125 84 M70.01953125 84 C35.34233822562871 84, 0.6651452012574168 84, -70.01953125 84 M-70.01953125 84 C-70.01953125 18.84783332533121, -70.01953125 -46.30433334933758, -70.01953125 -84 M-70.01953125 84 C-70.01953125 25.041576052074703, -70.01953125 -33.916847895850594, -70.01953125 -84"/></g><g transform="translate(0, -60)" class="annotation-group text"/><g transform="translate(-16.8828125, -60)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="33.765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 80px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>User</p></span></div></foreignObject></g></g><g transform="translate(-58.01953125, -12)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="72.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+id: string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="98.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 143px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+name: string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="99.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 144px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+email: string</p></span></div></foreignObject></g></g><g transform="translate(-58.01953125, 84)" class="methods-group text"/><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-70.01953125 -36 C-19.763725531554563 -36, 30.492080186890874 -36, 70.01953125 -36 M-70.01953125 -36 C-28.306399358543302 -36, 13.406732532913395 -36, 70.01953125 -36"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-70.01953125 60 C-34.08580384058427 60, 1.8479235688314617 60, 70.01953125 60 M-70.01953125 60 C-18.84531950356068 60, 32.32889224287864 60, 70.01953125 60"/></g></g><g transform="translate(1321.109375, 439)" id="classId-Post-10" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-77.171875 -84 L77.171875 -84 L77.171875 84 L-77.171875 84"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-77.171875 -84 C-35.279249846609154 -84, 6.613375306781691 -84, 77.171875 -84 M-77.171875 -84 C-36.88197052907622 -84, 3.40793394184756 -84, 77.171875 -84 M77.171875 -84 C77.171875 -21.961071423090765, 77.171875 40.07785715381847, 77.171875 84 M77.171875 -84 C77.171875 -29.008024345774963, 77.171875 25.983951308450074, 77.171875 84 M77.171875 84 C45.98992784524472 84, 14.807980690489444 84, -77.171875 84 M77.171875 84 C42.8485240534297 84, 8.525173106859398 84, -77.171875 84 M-77.171875 84 C-77.171875 44.54551901432738, -77.171875 5.091038028654765, -77.171875 -84 M-77.171875 84 C-77.171875 19.041521015610044, -77.171875 -45.91695796877991, -77.171875 -84"/></g><g transform="translate(0, -60)" class="annotation-group text"/><g transform="translate(-15.46875, -60)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="30.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 78px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Post</p></span></div></foreignObject></g></g><g transform="translate(-65.171875, -12)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="72.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+id: string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="90.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 133px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+title: string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="114.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 155px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+content: string</p></span></div></foreignObject></g></g><g transform="translate(-65.171875, 84)" class="methods-group text"/><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-77.171875 -36 C-18.621446849841142 -36, 39.928981300317716 -36, 77.171875 -36 M-77.171875 -36 C-43.592863064399474 -36, -10.013851128798947 -36, 77.171875 -36"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-77.171875 60 C-41.42563236916441 60, -5.6793897383288225 60, 77.171875 60 M-77.171875 60 C-33.06391603241434 60, 11.04404293517132 60, 77.171875 60"/></g></g><g transform="translate(1103.4375, 439)" id="classId-Todo-11" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-90.5 -84 L90.5 -84 L90.5 84 L-90.5 84"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-90.5 -84 C-42.56132451813541 -84, 5.377350963729185 -84, 90.5 -84 M-90.5 -84 C-26.44833607679128 -84, 37.60332784641744 -84, 90.5 -84 M90.5 -84 C90.5 -22.84326843179636, 90.5 38.31346313640728, 90.5 84 M90.5 -84 C90.5 -29.06581926331647, 90.5 25.86836147336706, 90.5 84 M90.5 84 C39.63751020088273 84, -11.22497959823454 84, -90.5 84 M90.5 84 C25.76762532301659 84, -38.96474935396682 84, -90.5 84 M-90.5 84 C-90.5 35.881226692208486, -90.5 -12.237546615583028, -90.5 -84 M-90.5 84 C-90.5 18.99196205334401, -90.5 -46.01607589331198, -90.5 -84"/></g><g transform="translate(0, -60)" class="annotation-group text"/><g transform="translate(-17.5625, -60)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="35.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 83px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Todo</p></span></div></foreignObject></g></g><g transform="translate(-78.5, -12)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="72.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+id: string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="90.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 133px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+title: string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="139.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 179px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+description: string</p></span></div></foreignObject></g></g><g transform="translate(-78.5, 84)" class="methods-group text"/><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-90.5 -36 C-26.624292108350865 -36, 37.25141578329827 -36, 90.5 -36 M-90.5 -36 C-19.94178231013511 -36, 50.61643537972978 -36, 90.5 -36"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-90.5 60 C-19.96625991630502 60, 50.56748016738996 60, 90.5 60 M-90.5 60 C-33.262373746767025 60, 23.97525250646595 60, 90.5 60"/></g></g></g></g></g></svg>