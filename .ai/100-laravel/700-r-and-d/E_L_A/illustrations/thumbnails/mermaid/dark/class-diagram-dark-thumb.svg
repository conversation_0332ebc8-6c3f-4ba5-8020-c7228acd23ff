<svg aria-roledescription="class" role="graphics-document document" viewBox="0 0 650.6304321289062 1828.5595703125" style="max-width: 650.63px; background-color: #2d333b;" class="classDiagram" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#a44141;}#my-svg .error-text{fill:#ddd;stroke:#ddd;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#ecf0f1;stroke:#ecf0f1;}#my-svg .marker.cross{stroke:#ecf0f1;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg g.classGroup text{fill:#ccc;stroke:none;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:10px;}#my-svg g.classGroup text .title{font-weight:bolder;}#my-svg .nodeLabel,#my-svg .edgeLabel{color:#ecf0f1;}#my-svg .edgeLabel .label rect{fill:#1f2020;}#my-svg .label text{fill:#ecf0f1;}#my-svg .labelBkg{background:#1f2020;}#my-svg .edgeLabel .label span{background:#1f2020;}#my-svg .classTitle{font-weight:bolder;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#my-svg .divider{stroke:#ccc;stroke-width:1;}#my-svg g.clickable{cursor:pointer;}#my-svg g.classGroup rect{fill:#1f2020;stroke:#ccc;}#my-svg g.classGroup line{stroke:#ccc;stroke-width:1;}#my-svg .classLabel .box{stroke:none;stroke-width:0;fill:#1f2020;opacity:0.5;}#my-svg .classLabel .label{fill:#ccc;font-size:10px;}#my-svg .relation{stroke:#ecf0f1;stroke-width:1;fill:none;}#my-svg .dashed-line{stroke-dasharray:3;}#my-svg .dotted-line{stroke-dasharray:1 2;}#my-svg #compositionStart,#my-svg .composition{fill:#ecf0f1!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg #compositionEnd,#my-svg .composition{fill:#ecf0f1!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg #dependencyStart,#my-svg .dependency{fill:#ecf0f1!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg #dependencyStart,#my-svg .dependency{fill:#ecf0f1!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg #extensionStart,#my-svg .extension{fill:transparent!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg #extensionEnd,#my-svg .extension{fill:transparent!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg #aggregationStart,#my-svg .aggregation{fill:transparent!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg #aggregationEnd,#my-svg .aggregation{fill:transparent!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg #lollipopStart,#my-svg .lollipop{fill:#1f2020!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg #lollipopEnd,#my-svg .lollipop{fill:#1f2020!important;stroke:#ecf0f1!important;stroke-width:1;}#my-svg .edgeTerminals{font-size:11px;line-height:initial;}#my-svg .classTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="18" class="marker aggregation class" id="my-svg_class-aggregationStart"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="1" class="marker aggregation class" id="my-svg_class-aggregationEnd"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="18" class="marker extension class" id="my-svg_class-extensionStart"><path d="M 1,7 L18,13 V 1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="1" class="marker extension class" id="my-svg_class-extensionEnd"><path d="M 1,1 V 13 L18,7 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="18" class="marker composition class" id="my-svg_class-compositionStart"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="1" class="marker composition class" id="my-svg_class-compositionEnd"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="6" class="marker dependency class" id="my-svg_class-dependencyStart"><path d="M 5,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="13" class="marker dependency class" id="my-svg_class-dependencyEnd"><path d="M 18,7 L9,13 L14,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="13" class="marker lollipop class" id="my-svg_class-lollipopStart"><circle r="6" cy="7" cx="7" fill="transparent" stroke="black"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="1" class="marker lollipop class" id="my-svg_class-lollipopEnd"><circle r="6" cy="7" cx="7" fill="transparent" stroke="black"/></marker></defs><g class="root"><g class="clusters"/><g class="edgePaths"><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_User_Post_1" d="M280.727,512L280.727,518.167C280.727,524.333,280.727,536.667,280.727,551C280.727,565.333,280.727,581.667,280.727,589.833L280.727,598"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_User_Todo_2" d="M152.168,460.305L142.68,475.087C133.193,489.87,114.217,519.435,104.73,580.384C95.242,641.333,95.242,733.667,95.242,826C95.242,918.333,95.242,1010.667,95.754,1063C96.265,1115.333,97.288,1127.667,97.8,1133.833L98.311,1140"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_User_Team_3" d="M409.285,405.018L430.559,429.015C451.832,453.012,494.379,501.006,515.652,531.17C536.926,561.333,536.926,573.667,536.926,579.833L536.926,586"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="Team-cyclic-special-1" d="M578.17,1066L579.23,1072.167C580.29,1078.333,582.409,1090.667,583.469,1152.992C584.529,1215.317,584.529,1327.633,584.529,1383.792L584.529,1439.95"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="Team-cyclic-special-mid" d="M584.529,1440.05L584.529,1496.208C584.529,1552.367,584.529,1664.683,588.968,1727.008C593.406,1789.333,602.284,1801.667,606.722,1807.833L611.161,1814"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="Team-cyclic-special-2" d="M611.233,1814L615.672,1807.833C620.11,1801.667,628.988,1789.333,633.426,1727C637.865,1664.667,637.865,1552.333,637.865,1440C637.865,1327.667,637.865,1215.333,635.618,1153C633.371,1090.667,628.876,1078.333,626.629,1072.167L624.382,1066"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Team_Category_5" d="M484.035,1066L482.676,1072.167C481.317,1078.333,478.599,1090.667,477.24,1146C475.881,1201.333,475.881,1299.667,475.881,1348.833L475.881,1398"/><path style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Team_Todo_6" d="M441.164,958.577L423.778,982.647C406.391,1006.718,371.618,1054.859,337.823,1104.812C304.027,1154.766,271.209,1206.532,254.8,1232.415L238.391,1258.297"/></g><g class="edgeLabels"><g transform="translate(280.7265625, 549)" class="edgeLabel"><g transform="translate(-26.7578125, -12)" class="label"><foreignObject height="24" width="53.515625"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>authors</p></span></div></foreignObject></g></g><g transform="translate(95.2421875, 826)" class="edgeLabel"><g transform="translate(-40.046875, -12)" class="label"><foreignObject height="24" width="80.09375"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>assigned to</p></span></div></foreignObject></g></g><g transform="translate(536.92578125, 549)" class="edgeLabel"><g transform="translate(-39.234375, -12)" class="label"><foreignObject height="24" width="78.46875"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>member of</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g transform="translate(584.5289062503725, 1777)" class="edgeLabel"><g transform="translate(-33.3359375, -12)" class="label"><foreignObject height="24" width="66.671875"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>parent of</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g transform="translate(475.880859375, 1103)" class="edgeLabel"><g transform="translate(-11.8125, -12)" class="label"><foreignObject height="24" width="23.625"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>has</p></span></div></foreignObject></g></g><g transform="translate(336.845703125, 1103)" class="edgeLabel"><g transform="translate(-35.90625, -12)" class="label"><foreignObject height="24" width="71.8125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>related to</p></span></div></foreignObject></g></g><g transform="translate(265.7265612500001, 529.4999989285715)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(130.0919187041857, 466.9304782972404)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(409.6696276771248, 428.0634041813437)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g></g><g transform="translate(566.3510438238642, 1085.78771321667)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(569.5289081251864, 1457.5500016073559)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(633.6304340373284, 1808.5595219670247)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(465.6201156396319, 1079.8616999308476)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(418.7574740822728, 963.9801228005398)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(290.72656125, 575.4999989285715)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(106.81328583442803, 1116.3199498562733)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(546.925780625, 563.4999994642857)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(594.5289081251861, 1417.4500016066106)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(608.1118498750355, 1786.0338580327652)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(611.2801768637507, 1082.5780065934407)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(485.8808596875, 1375.500000267857)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g><g transform="translate(255.42942529329844, 1246.5490230601733)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">n</span></div></foreignObject></g></g><g class="nodes"><g transform="translate(280.7265625, 260)" id="classId-User-0" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-128.55859375 -252 L128.55859375 -252 L128.55859375 252 L-128.55859375 252"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-128.55859375 -252 C-29.654356616193454 -252, 69.2498805176131 -252, 128.55859375 -252 M-128.55859375 -252 C-52.81006613108855 -252, 22.938461487822906 -252, 128.55859375 -252 M128.55859375 -252 C128.55859375 -72.76457737097729, 128.55859375 106.47084525804541, 128.55859375 252 M128.55859375 -252 C128.55859375 -59.29742967982645, 128.55859375 133.4051406403471, 128.55859375 252 M128.55859375 252 C73.48894215109948 252, 18.419290552198945 252, -128.55859375 252 M128.55859375 252 C43.88926379509597 252, -40.78006615980806 252, -128.55859375 252 M-128.55859375 252 C-128.55859375 63.51615840968944, -128.55859375 -124.96768318062112, -128.55859375 -252 M-128.55859375 252 C-128.55859375 99.00577246166702, -128.55859375 -53.98845507666596, -128.55859375 -252"/></g><g transform="translate(0, -228)" class="annotation-group text"/><g transform="translate(-16.8828125, -228)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="33.765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 80px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>User</p></span></div></foreignObject></g></g><g transform="translate(-116.55859375, -180)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="67.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 117px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+bigint id</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="147.78125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 191px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+bigint snowflake_id</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="81.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 131px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+string slug</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="85.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 131px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+string type</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="93.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 139px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+string email</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="119.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 164px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+string password</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="216.234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 247px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+timestamp email_verified_at</p></span></div></foreignObject></g><g transform="translate(0,156)" style="" class="label"><foreignObject height="24" width="96.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 140px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+string status</p></span></div></foreignObject></g></g><g transform="translate(-116.55859375, 36)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="102.953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 147px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+timestamps()</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="100.203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 145px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+userstamps()</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="101"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 146px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+softDeletes()</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="56.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 107px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+posts()</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="59.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 109px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+todos()</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="63.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 111px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+teams()</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="117.765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 162px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+conversations()</p></span></div></foreignObject></g><g transform="translate(0,156)" style="" class="label"><foreignObject height="24" width="86.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 135px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+messages()</p></span></div></foreignObject></g><g transform="translate(0,180)" style="" class="label"><foreignObject height="24" width="93.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 140px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+comments()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-128.55859375 -204 C-30.376704981336005 -204, 67.80518378732799 -204, 128.55859375 -204 M-128.55859375 -204 C-64.55898421849923 -204, -0.5593746869984528 -204, 128.55859375 -204"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-128.55859375 12 C-45.818924418962965 12, 36.92074491207407 12, 128.55859375 12 M-128.55859375 12 C-76.6530240250728 12, -24.747454300145606 12, 128.55859375 12"/></g></g><g transform="translate(536.92578125, 826)" id="classId-Team-1" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-95.76171875 -240 L95.76171875 -240 L95.76171875 240 L-95.76171875 240"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-95.76171875 -240 C-40.67825496466928 -240, 14.405208820661443 -240, 95.76171875 -240 M-95.76171875 -240 C-37.315678163020536 -240, 21.13036242395893 -240, 95.76171875 -240 M95.76171875 -240 C95.76171875 -99.02987791176653, 95.76171875 41.94024417646693, 95.76171875 240 M95.76171875 -240 C95.76171875 -123.84481626876504, 95.76171875 -7.689632537530088, 95.76171875 240 M95.76171875 240 C49.75461528692577 240, 3.7475118238515392 240, -95.76171875 240 M95.76171875 240 C32.56285075268035 240, -30.636017244639305 240, -95.76171875 240 M-95.76171875 240 C-95.76171875 115.79249553415426, -95.76171875 -8.415008931691489, -95.76171875 -240 M-95.76171875 240 C-95.76171875 66.67100575807746, -95.76171875 -106.65798848384509, -95.76171875 -240"/></g><g transform="translate(0, -216)" class="annotation-group text"/><g transform="translate(-19.7421875, -216)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="39.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 85px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Team</p></span></div></foreignObject></g></g><g transform="translate(-83.76171875, -168)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="67.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 117px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+bigint id</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="147.78125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 191px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+bigint snowflake_id</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="92.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 139px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+string name</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="81.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 131px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+string slug</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="123.59375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 165px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+bigint parent_id</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="86"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 131px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+string path</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="74.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 120px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+int depth</p></span></div></foreignObject></g><g transform="translate(0,156)" style="" class="label"><foreignObject height="24" width="96.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 140px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+string status</p></span></div></foreignObject></g></g><g transform="translate(-83.76171875, 48)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="102.953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 147px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+timestamps()</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="100.203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 145px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+userstamps()</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="101"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 146px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+softDeletes()</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="67.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 114px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+parent()</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="78.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 127px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+children()</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="56.78125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 107px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+users()</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="94.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 139px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+categories()</p></span></div></foreignObject></g><g transform="translate(0,156)" style="" class="label"><foreignObject height="24" width="59.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 109px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+todos()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-95.76171875 -192 C-41.69597300587253 -192, 12.369772738254937 -192, 95.76171875 -192 M-95.76171875 -192 C-47.25109626151241 -192, 1.2595262269751828 -192, 95.76171875 -192"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-95.76171875 24 C-52.18974444132639 24, -8.61777013265278 24, 95.76171875 24 M-95.76171875 24 C-20.83216723579345 24, 54.0973842784131 24, 95.76171875 24"/></g></g><g transform="translate(280.7265625, 826)" id="classId-Post-2" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-110.4375 -228 L110.4375 -228 L110.4375 228 L-110.4375 228"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-110.4375 -228 C-40.41091164379641 -228, 29.61567671240718 -228, 110.4375 -228 M-110.4375 -228 C-22.654901784348368 -228, 65.12769643130326 -228, 110.4375 -228 M110.4375 -228 C110.4375 -59.57404507598153, 110.4375 108.85190984803694, 110.4375 228 M110.4375 -228 C110.4375 -125.20159643172674, 110.4375 -22.403192863453484, 110.4375 228 M110.4375 228 C41.36221647517891 228, -27.713067049642177 228, -110.4375 228 M110.4375 228 C52.91996138093499 228, -4.597577238130015 228, -110.4375 228 M-110.4375 228 C-110.4375 122.96359119341048, -110.4375 17.92718238682096, -110.4375 -228 M-110.4375 228 C-110.4375 127.05773100590831, -110.4375 26.115462011816618, -110.4375 -228"/></g><g transform="translate(0, -204)" class="annotation-group text"/><g transform="translate(-15.46875, -204)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="30.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 78px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Post</p></span></div></foreignObject></g></g><g transform="translate(-98.4375, -156)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="67.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 117px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+bigint id</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="147.78125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 191px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+bigint snowflake_id</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="81.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 131px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+string slug</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="84.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 129px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+string title</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="98.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 139px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+text content</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="96.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 140px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+string status</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="181.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 216px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+timestamp published_at</p></span></div></foreignObject></g></g><g transform="translate(-98.4375, 36)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="102.953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 147px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+timestamps()</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="100.203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 145px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+userstamps()</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="101"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 146px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+softDeletes()</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="50.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 101px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+user()</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="94.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 139px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+categories()</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="49.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+tags()</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="64.03125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 113px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+media()</p></span></div></foreignObject></g><g transform="translate(0,156)" style="" class="label"><foreignObject height="24" width="93.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 140px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+comments()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-110.4375 -180 C-25.76811546405314 -180, 58.90126907189372 -180, 110.4375 -180 M-110.4375 -180 C-62.65066237669039 -180, -14.863824753380783 -180, 110.4375 -180"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-110.4375 12 C-44.26636973101587 12, 21.904760537968258 12, 110.4375 12 M-110.4375 12 C-62.794326561899936 12, -15.151153123799872 12, 110.4375 12"/></g></g><g transform="translate(123.1953125, 1440)" id="classId-Todo-3" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-115.1953125 -300 L115.1953125 -300 L115.1953125 300 L-115.1953125 300"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-115.1953125 -300 C-63.89698679129968 -300, -12.598661082599364 -300, 115.1953125 -300 M-115.1953125 -300 C-34.32153854054373 -300, 46.552235418912545 -300, 115.1953125 -300 M115.1953125 -300 C115.1953125 -127.45693640242712, 115.1953125 45.086127195145764, 115.1953125 300 M115.1953125 -300 C115.1953125 -178.8370181525515, 115.1953125 -57.674036305103016, 115.1953125 300 M115.1953125 300 C36.38651586830592 300, -42.42228076338816 300, -115.1953125 300 M115.1953125 300 C38.181772270985945 300, -38.83176795802811 300, -115.1953125 300 M-115.1953125 300 C-115.1953125 77.10806512951507, -115.1953125 -145.78386974096986, -115.1953125 -300 M-115.1953125 300 C-115.1953125 89.35612121382732, -115.1953125 -121.28775757234536, -115.1953125 -300"/></g><g transform="translate(0, -276)" class="annotation-group text"/><g transform="translate(-17.5625, -276)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="35.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 83px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Todo</p></span></div></foreignObject></g></g><g transform="translate(-103.1953125, -228)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="67.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 117px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+bigint id</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="147.78125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 191px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+bigint snowflake_id</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="84.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 129px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+string title</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="122.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 163px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+text description</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="123.59375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 165px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+bigint parent_id</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="86"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 131px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+string path</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="74.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 120px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+int depth</p></span></div></foreignObject></g><g transform="translate(0,156)" style="" class="label"><foreignObject height="24" width="96.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 140px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+string status</p></span></div></foreignObject></g><g transform="translate(0,180)" style="" class="label"><foreignObject height="24" width="139.078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 177px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+timestamp due_at</p></span></div></foreignObject></g><g transform="translate(0,204)" style="" class="label"><foreignObject height="24" width="188.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 220px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+timestamp completed_at</p></span></div></foreignObject></g></g><g transform="translate(-103.1953125, 36)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="102.953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 147px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+timestamps()</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="100.203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 145px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+userstamps()</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="101"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 146px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+softDeletes()</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="50.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 101px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+user()</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="56.90625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 105px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+team()</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="67.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 114px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+parent()</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="78.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 127px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+children()</p></span></div></foreignObject></g><g transform="translate(0,156)" style="" class="label"><foreignObject height="24" width="94.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 139px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+categories()</p></span></div></foreignObject></g><g transform="translate(0,180)" style="" class="label"><foreignObject height="24" width="49.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+tags()</p></span></div></foreignObject></g><g transform="translate(0,204)" style="" class="label"><foreignObject height="24" width="64.03125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 113px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+media()</p></span></div></foreignObject></g><g transform="translate(0,228)" style="" class="label"><foreignObject height="24" width="93.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 140px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+comments()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-115.1953125 -252 C-25.09847950999732 -252, 64.99835348000536 -252, 115.1953125 -252 M-115.1953125 -252 C-59.90842734879091 -252, -4.621542197581817 -252, 115.1953125 -252"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-115.1953125 12 C-63.94001977556757 12, -12.684727051135141 12, 115.1953125 12 M-115.1953125 12 C-49.531551689798874 12, 16.13220912040225 12, 115.1953125 12"/></g></g><g transform="translate(475.880859375, 1440)" id="classId-Category-4" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-45.15625 -42 L45.15625 -42 L45.15625 42 L-45.15625 42"/><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-45.15625 -42 C-9.447958897393676 -42, 26.26033220521265 -42, 45.15625 -42 M-45.15625 -42 C-17.640347976563838 -42, 9.875554046872324 -42, 45.15625 -42 M45.15625 -42 C45.15625 -17.683874164358965, 45.15625 6.63225167128207, 45.15625 42 M45.15625 -42 C45.15625 -15.881979440445427, 45.15625 10.236041119109146, 45.15625 42 M45.15625 42 C14.368083384695595 42, -16.42008323060881 42, -45.15625 42 M45.15625 42 C25.008136331686288 42, 4.860022663372575 42, -45.15625 42 M-45.15625 42 C-45.15625 17.965074304138753, -45.15625 -6.069851391722494, -45.15625 -42 M-45.15625 42 C-45.15625 23.495520203322997, -45.15625 4.991040406645993, -45.15625 -42"/></g><g transform="translate(0, -18)" class="annotation-group text"/><g transform="translate(-33.15625, -18)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="66.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 109px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Category</p></span></div></foreignObject></g></g><g transform="translate(-33.15625, 30)" class="members-group text"/><g transform="translate(-33.15625, 60)" class="methods-group text"/><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-45.15625 6 C-11.145406044831276 6, 22.865437910337448 6, 45.15625 6 M-45.15625 6 C-20.770172399126615 6, 3.61590520174677 6, 45.15625 6"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-45.15625 24 C-19.98091830506195 24, 5.194413389876097 24, 45.15625 24 M-45.15625 24 C-23.764755940291366 24, -2.3732618805827315 24, 45.15625 24"/></g></g><g transform="translate(584.5289062503725, 1440)" id="Team---Team---1" class="label edgeLabel"><rect height="0.1" width="0.1"/><g transform="translate(0, 0)" style="" class="label"><rect/><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 10px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g></g><g transform="translate(611.1968750003725, 1814.050000000745)" id="Team---Team---2" class="label edgeLabel"><rect height="0.1" width="0.1"/><g transform="translate(0, 0)" style="" class="label"><rect/><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 10px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"></span></div></foreignObject></g></g></g></g></g></svg>