<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 2582.84765625 889" style="max-width: 2582.85px; background-color: #2d333b;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#a44141;}#my-svg .error-text{fill:#ddd;stroke:#ddd;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#ecf0f1;stroke:#ecf0f1;}#my-svg .marker.cross{stroke:#ecf0f1;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#my-svg .cluster-label text{fill:#F9FFFE;}#my-svg .cluster-label span{color:#F9FFFE;}#my-svg .cluster-label span p{background-color:transparent;}#my-svg .label text,#my-svg span{fill:#ccc;color:#ccc;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#my-svg .rough-node .label text,#my-svg .node .label text,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-anchor:middle;}#my-svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#my-svg .rough-node .label,#my-svg .node .label,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-align:center;}#my-svg .node.clickable{cursor:pointer;}#my-svg .root .anchor path{fill:#ecf0f1!important;stroke-width:0;stroke:#ecf0f1;}#my-svg .arrowheadPath{fill:lightgrey;}#my-svg .edgePath .path{stroke:#ecf0f1;stroke-width:2.0px;}#my-svg .flowchart-link{stroke:#ecf0f1;fill:none;}#my-svg .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#my-svg .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#my-svg .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#my-svg .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#my-svg .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#my-svg .cluster text{fill:#F9FFFE;}#my-svg .cluster span{color:#F9FFFE;}#my-svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:#282c34;border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#my-svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#my-svg rect.text{fill:none;stroke-width:0;}#my-svg .icon-shape,#my-svg .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#my-svg .icon-shape p,#my-svg .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#my-svg .icon-shape rect,#my-svg .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"/></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><g class="root"><g class="clusters"><g data-look="classic" id="Monitoring" class="cluster"><rect height="233" width="831.44140625" y="648" x="8" style=""/><g transform="translate(348.345703125, 648)" class="cluster-label"><foreignObject height="24" width="150.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Monitoring &amp; Logging</p></span></div></foreignObject></g></g><g data-look="classic" id="Search" class="cluster"><rect height="104" width="477.81640625" y="648" x="2097.03125" style=""/><g transform="translate(2283.830078125, 648)" class="cluster-label"><foreignObject height="24" width="104.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Search Service</p></span></div></foreignObject></g></g><g data-look="classic" id="Storage" class="cluster"><rect height="104" width="291.171875" y="648" x="859.44140625" style=""/><g transform="translate(951.33203125, 648)" class="cluster-label"><foreignObject height="24" width="107.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Object Storage</p></span></div></foreignObject></g></g><g data-look="classic" id="Cache" class="cluster"><rect height="104" width="301.484375" y="648" x="1170.61328125" style=""/><g transform="translate(1277.58984375, 648)" class="cluster-label"><foreignObject height="24" width="87.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Cache Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="Database" class="cluster"><rect height="104" width="560.62890625" y="648" x="1492.09765625" style=""/><g transform="translate(1711.857421875, 648)" class="cluster-label"><foreignObject height="24" width="121.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Database Cluster</p></span></div></foreignObject></g></g><g data-look="classic" id="Queue" class="cluster"><rect height="128" width="1173.63671875" y="470" x="8" style=""/><g transform="translate(532.576171875, 470)" class="cluster-label"><foreignObject height="24" width="124.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Queue Processing</p></span></div></foreignObject></g></g><g data-look="classic" id="WebServers" class="cluster"><rect height="128" width="1178.875" y="470" x="1201.63671875" style=""/><g transform="translate(1722.87109375, 470)" class="cluster-label"><foreignObject height="24" width="136.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Web Server Cluster</p></span></div></foreignObject></g></g><g data-look="classic" id="LoadBalancer" class="cluster"><rect height="104" width="761.2421875" y="316" x="1274.8984375" style=""/><g transform="translate(1605.00390625, 316)" class="cluster-label"><foreignObject height="24" width="101.03125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Load Balancer</p></span></div></foreignObject></g></g><g data-look="classic" id="CDN" class="cluster"><rect height="104" width="361.5078125" y="162" x="1407.25" style=""/><g transform="translate(1495.37890625, 162)" class="cluster-label"><foreignObject height="24" width="185.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Content Delivery Network</p></span></div></foreignObject></g></g><g data-look="classic" id="Users" class="cluster"><rect height="104" width="431.8125" y="8" x="1431.578125" style=""/><g transform="translate(1612.8203125, 8)" class="cluster-label"><foreignObject height="24" width="69.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>End Users</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Browser_CloudFront_0" d="M1542.852,87L1542.852,91.167C1542.852,95.333,1542.852,103.667,1542.852,112C1542.852,120.333,1542.852,128.667,1542.852,137C1542.852,145.333,1542.852,153.667,1542.852,161.333C1542.852,169,1542.852,176,1542.852,179.5L1542.852,183"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Mobile_CloudFront_0" d="M1748.758,87L1748.758,91.167C1748.758,95.333,1748.758,103.667,1748.758,112C1748.758,120.333,1748.758,128.667,1748.758,137C1748.758,145.333,1748.758,153.667,1731.853,162.102C1714.949,170.538,1681.14,179.076,1664.236,183.345L1647.331,187.614"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CloudFront_ALB_0" d="M1542.852,241L1542.852,245.167C1542.852,249.333,1542.852,257.667,1542.852,266C1542.852,274.333,1542.852,282.667,1542.852,291C1542.852,299.333,1542.852,307.667,1542.852,315.333C1542.852,323,1542.852,330,1542.852,333.5L1542.852,337"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ALB_WebServer1_0" d="M1666.328,381.971L1722.344,388.309C1778.361,394.647,1890.393,407.324,1946.41,417.829C2002.426,428.333,2002.426,436.667,2002.426,445C2002.426,453.333,2002.426,461.667,2002.426,469.333C2002.426,477,2002.426,484,2002.426,487.5L2002.426,491"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ALB_WebServer2_0" d="M1620.515,395L1632.5,399.167C1644.485,403.333,1668.456,411.667,1680.441,420C1692.426,428.333,1692.426,436.667,1692.426,445C1692.426,453.333,1692.426,461.667,1692.426,469.333C1692.426,477,1692.426,484,1692.426,487.5L1692.426,491"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ALB_WebServerN_0" d="M1455.25,395L1441.731,399.167C1428.212,403.333,1401.174,411.667,1387.656,420C1374.137,428.333,1374.137,436.667,1374.137,445C1374.137,453.333,1374.137,461.667,1374.137,469.333C1374.137,477,1374.137,484,1374.137,487.5L1374.137,491"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer1_Redis_0" d="M2132.426,563.133L2158.357,568.944C2184.288,574.755,2236.15,586.378,2262.081,596.356C2288.012,606.333,2288.012,614.667,2141.069,623C1994.126,631.333,1700.241,639.667,1547.447,647.637C1394.653,655.607,1382.95,663.213,1377.099,667.017L1371.248,670.82"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer2_Redis_0" d="M1780.785,573L1790.225,577.167C1799.665,581.333,1818.546,589.667,1827.986,598C1837.426,606.333,1837.426,614.667,1758.914,623C1680.402,631.333,1523.379,639.667,1442.069,647.472C1360.758,655.277,1355.161,662.553,1352.362,666.191L1349.564,669.83"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServerN_Redis_0" d="M1467.547,573L1477.527,577.167C1487.507,581.333,1507.466,589.667,1517.446,598C1527.426,606.333,1527.426,614.667,1493.914,623C1460.402,631.333,1393.379,639.667,1359.867,647.333C1326.355,655,1326.355,662,1326.355,665.5L1326.355,669"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer1_PrimaryDB_0" d="M2118.564,573L2130.972,577.167C2143.38,581.333,2168.196,589.667,2180.604,598C2193.012,606.333,2193.012,614.667,2139.979,623C2086.947,631.333,1980.882,639.667,1915.124,647.802C1849.367,655.936,1823.917,663.873,1811.192,667.841L1798.467,671.809"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer2_PrimaryDB_0" d="M1722.895,573L1726.15,577.167C1729.405,581.333,1735.915,589.667,1739.171,598C1742.426,606.333,1742.426,614.667,1757.824,623C1773.223,631.333,1804.02,639.667,1809.878,647.747C1815.737,655.827,1796.659,663.655,1787.119,667.568L1777.58,671.482"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServerN_PrimaryDB_0" d="M1455.36,573L1464.037,577.167C1472.715,581.333,1490.07,589.667,1498.748,598C1507.426,606.333,1507.426,614.667,1555.324,623C1603.223,631.333,1699.02,639.667,1740.539,647.657C1782.058,655.648,1769.299,663.296,1762.92,667.12L1756.541,670.943"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer1_ReplicaDB_0" d="M2106.377,573L2117.482,577.167C2128.588,581.333,2150.8,589.667,2161.906,598C2173.012,606.333,2173.012,614.667,2119.979,623C2066.947,631.333,1960.882,639.667,1913.899,647.645C1866.917,655.623,1879.017,663.245,1885.068,667.057L1891.118,670.868"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer2_ReplicaDB_0" d="M1710.707,573L1712.66,577.167C1714.613,581.333,1718.52,589.667,1720.473,598C1722.426,606.333,1722.426,614.667,1737.824,623C1753.223,631.333,1784.02,639.667,1808.624,647.74C1833.228,655.813,1851.639,663.625,1860.845,667.531L1870.051,671.438"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServerN_ReplicaDB_0" d="M1443.172,573L1450.548,577.167C1457.923,581.333,1472.675,589.667,1480.05,598C1487.426,606.333,1487.426,614.667,1535.324,623C1583.223,631.333,1679.02,639.667,1740.753,648.259C1802.487,656.852,1830.157,665.704,1843.992,670.13L1857.827,674.556"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer1_S3_0" d="M2039.345,573L2043.29,577.167C2047.234,581.333,2055.123,589.667,2059.067,598C2063.012,606.333,2063.012,614.667,1897.146,623C1731.281,631.333,1399.551,639.667,1229.168,647.575C1058.784,655.483,1049.748,662.966,1045.23,666.707L1040.712,670.449"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer2_S3_0" d="M1698.52,573L1699.171,577.167C1699.822,581.333,1701.124,589.667,1701.775,598C1702.426,606.333,1702.426,614.667,1591.193,623C1479.96,631.333,1257.493,639.667,1144.19,647.423C1030.886,655.178,1026.745,662.357,1024.674,665.946L1022.603,669.535"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServerN_S3_0" d="M1430.985,573L1437.058,577.167C1443.132,581.333,1455.279,589.667,1461.352,598C1467.426,606.333,1467.426,614.667,1392.026,623C1316.626,631.333,1165.827,639.667,1089.752,647.345C1013.677,655.024,1012.326,662.048,1011.65,665.56L1010.975,669.072"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer1_Meilisearch_0" d="M2027.158,573L2029.8,577.167C2032.442,581.333,2037.727,589.667,2040.369,598C2043.012,606.333,2043.012,614.667,2121.217,623C2199.422,631.333,2355.832,639.667,2399.109,650.235C2442.386,660.803,2372.529,673.607,2337.601,680.009L2302.673,686.41"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer2_Meilisearch_0" d="M1686.332,573L1685.681,577.167C1685.03,581.333,1683.728,589.667,1683.077,598C1682.426,606.333,1682.426,614.667,1791.128,623C1899.829,631.333,2117.233,639.667,2218.031,647.707C2318.829,655.747,2303.022,663.493,2295.118,667.366L2287.214,671.24"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServerN_Meilisearch_0" d="M1418.797,573L1423.569,577.167C1428.34,581.333,1437.883,589.667,1442.654,598C1447.426,606.333,1447.426,614.667,1567.794,623C1688.163,631.333,1928.9,639.667,2053.487,647.559C2178.075,655.451,2186.513,662.902,2190.732,666.627L2194.951,670.352"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer1_Redis_2" d="M2014.97,573L2016.311,577.167C2017.651,581.333,2020.331,589.667,2021.671,598C2023.012,606.333,2023.012,614.667,1916.902,623C1810.793,631.333,1598.574,639.667,1488.161,647.563C1377.748,655.46,1369.14,662.92,1364.836,666.65L1360.532,670.38"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer2_Redis_2" d="M1674.145,573L1672.191,577.167C1670.238,581.333,1666.332,589.667,1664.379,598C1662.426,606.333,1662.426,614.667,1609.747,623C1557.069,631.333,1451.712,639.667,1397.671,647.378C1343.629,655.089,1340.902,662.178,1339.539,665.722L1338.176,669.267"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServerN_Redis_2" d="M1406.61,573L1410.079,577.167C1413.548,581.333,1420.487,589.667,1423.956,598C1427.426,606.333,1427.426,614.667,1407.247,623C1387.069,631.333,1346.712,639.667,1327.897,647.378C1309.082,655.089,1311.808,662.178,1313.172,665.722L1314.535,669.267"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_QueueWorker1_Redis_0" d="M472.242,551.755L510.118,559.462C547.993,567.17,623.745,582.585,661.62,594.459C699.496,606.333,699.496,614.667,790.639,623C881.783,631.333,1064.069,639.667,1161.064,647.637C1258.058,655.607,1269.761,663.213,1275.612,667.017L1281.463,670.82"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_QueueWorker2_Redis_0" d="M866.269,561L876.437,567.167C886.606,573.333,906.944,585.667,917.112,596C927.281,606.333,927.281,614.667,983.794,623C1040.306,631.333,1153.331,639.667,1214.147,647.563C1274.963,655.46,1283.571,662.92,1287.875,666.65L1292.179,670.38"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Scheduler_Redis_0" d="M1075.771,561L1081.013,567.167C1086.256,573.333,1096.741,585.667,1101.984,596C1107.227,606.333,1107.227,614.667,1137.081,623C1166.936,631.333,1226.646,639.667,1259.299,647.472C1291.953,655.277,1297.55,662.553,1300.349,666.191L1303.147,669.83"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_QueueWorker1_PrimaryDB_0" d="M472.242,552.961L506.785,560.467C541.327,567.974,610.411,582.987,644.954,594.66C679.496,606.333,679.496,614.667,829.46,623C979.424,631.333,1279.353,639.667,1439.018,647.75C1598.684,655.834,1618.086,663.668,1627.787,667.585L1637.488,671.502"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_QueueWorker2_PrimaryDB_0" d="M831.57,561L833.813,567.167C836.057,573.333,840.544,585.667,842.788,596C845.031,606.333,845.031,614.667,991.662,623C1138.293,631.333,1431.555,639.667,1577.048,647.365C1722.541,655.064,1720.265,662.128,1719.128,665.661L1717.99,669.193"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Scheduler_PrimaryDB_0" d="M1008.294,561L998.125,567.167C987.956,573.333,967.619,585.667,957.45,596C947.281,606.333,947.281,614.667,1081.87,623C1216.46,631.333,1485.638,639.667,1616.927,647.504C1748.216,655.342,1741.615,662.684,1738.315,666.355L1735.015,670.025"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_QueueWorker1_S3_0" d="M386.332,561L386.638,567.167C386.944,573.333,387.556,585.667,387.862,596C388.168,606.333,388.168,614.667,482.179,623C576.19,631.333,764.212,639.667,861.979,647.532C959.745,655.398,967.255,662.795,971.011,666.494L974.766,670.193"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_QueueWorker2_S3_0" d="M823.132,561L823.449,567.167C823.765,573.333,824.398,585.667,824.715,596C825.031,606.333,825.031,614.667,853.364,623C881.697,631.333,938.362,639.667,967.37,647.345C996.378,655.024,997.729,662.048,998.404,665.56L999.08,669.072"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer1_Prometheus_0" d="M1926.254,573L1918.116,577.167C1909.978,581.333,1893.702,589.667,1885.564,598C1877.426,606.333,1877.426,614.667,1693.844,623C1510.263,631.333,1143.1,639.667,954.015,647.622C764.93,655.577,753.923,663.155,748.419,666.943L742.915,670.732"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer2_Prometheus_0" d="M1661.957,573L1658.702,577.167C1655.447,581.333,1648.936,589.667,1645.681,598C1642.426,606.333,1642.426,614.667,1454.963,623C1267.5,631.333,892.574,639.667,722.927,648.903C553.28,658.139,588.912,668.277,606.728,673.347L624.543,678.416"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServerN_Prometheus_0" d="M1394.422,573L1396.589,577.167C1398.757,581.333,1403.091,589.667,1405.259,598C1407.426,606.333,1407.426,614.667,1238.713,623C1070,631.333,732.574,639.667,602.078,650.344C471.581,661.021,548.014,674.041,586.231,680.551L624.447,687.062"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_QueueWorker1_Prometheus_0" d="M297.742,560.14L276.681,566.45C255.62,572.76,213.497,585.38,192.436,595.857C171.375,606.333,171.375,614.667,171.375,623C171.375,631.333,171.375,639.667,246.881,651.255C322.387,662.844,473.398,677.687,548.904,685.109L624.41,692.531"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_QueueWorker2_Prometheus_0" d="M814.695,561L813.084,567.167C811.473,573.333,808.252,585.667,806.642,596C805.031,606.333,805.031,614.667,707.705,623C610.379,631.333,415.727,639.667,385.624,651.126C355.521,662.586,489.967,677.171,557.191,684.464L624.414,691.757"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Prometheus_Grafana_0" d="M700.398,727L700.398,731.167C700.398,735.333,700.398,743.667,700.398,752C700.398,760.333,700.398,768.667,700.398,776.333C700.398,784,700.398,791,700.398,794.5L700.398,798"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer1_ELK_0" d="M1914.066,573L1904.626,577.167C1895.186,581.333,1876.306,589.667,1866.866,598C1857.426,606.333,1857.426,614.667,1659.963,623C1462.5,631.333,1067.574,639.667,855.035,648.769C642.496,657.872,612.344,667.743,597.268,672.679L582.192,677.615"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServer2_ELK_0" d="M1604.066,573L1594.626,577.167C1585.186,581.333,1566.306,589.667,1556.866,598C1547.426,606.333,1547.426,614.667,1372.463,623C1197.5,631.333,847.574,639.667,673.709,647.363C499.844,655.06,502.04,662.12,503.138,665.65L504.236,669.18"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebServerN_ELK_0" d="M1331.48,573L1326.923,577.167C1322.366,581.333,1313.251,589.667,1308.694,598C1304.137,606.333,1304.137,614.667,1149.305,623C994.474,631.333,684.811,639.667,541.706,648.23C398.601,656.794,422.053,665.588,433.779,669.985L445.505,674.383"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_QueueWorker1_ELK_0" d="M297.742,552.177L261.083,559.814C224.423,567.451,151.104,582.726,114.445,594.529C77.785,606.333,77.785,614.667,77.785,623C77.785,631.333,77.785,639.667,139.034,651.138C200.283,662.609,322.78,677.217,384.029,684.522L445.278,691.826"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_QueueWorker2_ELK_0" d="M778.609,561L768.757,567.167C758.905,573.333,739.201,585.667,729.348,596C719.496,606.333,719.496,614.667,633.092,623C546.689,631.333,373.882,639.667,328.183,650.601C282.484,661.536,363.894,675.072,404.599,681.84L445.304,688.608"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(1542.8515625, 60)" id="flowchart-Browser-0" class="node default"><rect height="54" width="152.546875" y="-27" x="-76.2734375" style="" class="basic label-container"/><g transform="translate(-46.2734375, -12)" style="" class="label"><rect/><foreignObject height="24" width="92.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Web Browser</p></span></div></foreignObject></g></g><g transform="translate(1748.7578125, 60)" id="flowchart-Mobile-1" class="node default"><rect height="54" width="159.265625" y="-27" x="-79.6328125" style="" class="basic label-container"/><g transform="translate(-49.6328125, -12)" style="" class="label"><rect/><foreignObject height="24" width="99.265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Mobile Device</p></span></div></foreignObject></g></g><g transform="translate(1542.8515625, 214)" id="flowchart-CloudFront-2" class="node default"><rect height="54" width="201.203125" y="-27" x="-100.6015625" style="" class="basic label-container"/><g transform="translate(-70.6015625, -12)" style="" class="label"><rect/><foreignObject height="24" width="141.203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>CDN Edge Locations</p></span></div></foreignObject></g></g><g transform="translate(1542.8515625, 368)" id="flowchart-ALB-3" class="node default"><rect height="54" width="246.953125" y="-27" x="-123.4765625" style="" class="basic label-container"/><g transform="translate(-93.4765625, -12)" style="" class="label"><rect/><foreignObject height="24" width="186.953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Application Load Balancer</p></span></div></foreignObject></g></g><g transform="translate(2002.42578125, 534)" id="flowchart-WebServer1-4" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Web Server 1\nFrankenPHP + Laravel</p></span></div></foreignObject></g></g><g transform="translate(1692.42578125, 534)" id="flowchart-WebServer2-5" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Web Server 2\nFrankenPHP + Laravel</p></span></div></foreignObject></g></g><g transform="translate(1374.13671875, 534)" id="flowchart-WebServerN-6" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Web Server N\nFrankenPHP + Laravel</p></span></div></foreignObject></g></g><g transform="translate(384.9921875, 534)" id="flowchart-QueueWorker1-7" class="node default"><rect height="54" width="174.5" y="-27" x="-87.25" style="" class="basic label-container"/><g transform="translate(-57.25, -12)" style="" class="label"><rect/><foreignObject height="24" width="114.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Queue Worker 1</p></span></div></foreignObject></g></g><g transform="translate(821.74609375, 534)" id="flowchart-QueueWorker2-8" class="node default"><rect height="54" width="174.5" y="-27" x="-87.25" style="" class="basic label-container"/><g transform="translate(-57.25, -12)" style="" class="label"><rect/><foreignObject height="24" width="114.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Queue Worker 2</p></span></div></foreignObject></g></g><g transform="translate(1052.81640625, 534)" id="flowchart-Scheduler-9" class="node default"><rect height="54" width="187.640625" y="-27" x="-93.8203125" style="" class="basic label-container"/><g transform="translate(-63.8203125, -12)" style="" class="label"><rect/><foreignObject height="24" width="127.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Laravel Scheduler</p></span></div></foreignObject></g></g><g transform="translate(1708.06640625, 700)" id="flowchart-PrimaryDB-10" class="node default"><rect height="54" width="198.734375" y="-27" x="-99.3671875" style="" class="basic label-container"/><g transform="translate(-69.3671875, -12)" style="" class="label"><rect/><foreignObject height="24" width="138.734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Primary PostgreSQL</p></span></div></foreignObject></g></g><g transform="translate(1937.36328125, 700)" id="flowchart-ReplicaDB-11" class="node default"><rect height="54" width="151.453125" y="-27" x="-75.7265625" style="" class="basic label-container"/><g transform="translate(-45.7265625, -12)" style="" class="label"><rect/><foreignObject height="24" width="91.453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Read Replica</p></span></div></foreignObject></g></g><g transform="translate(1326.35546875, 700)" id="flowchart-Redis-12" class="node default"><rect height="54" width="152.96875" y="-27" x="-76.484375" style="" class="basic label-container"/><g transform="translate(-46.484375, -12)" style="" class="label"><rect/><foreignObject height="24" width="92.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Redis Cluster</p></span></div></foreignObject></g></g><g transform="translate(1005.02734375, 700)" id="flowchart-S3-13" class="node default"><rect height="54" width="221.171875" y="-27" x="-110.5859375" style="" class="basic label-container"/><g transform="translate(-80.5859375, -12)" style="" class="label"><rect/><foreignObject height="24" width="161.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>S3-compatible Storage</p></span></div></foreignObject></g></g><g transform="translate(2228.52734375, 700)" id="flowchart-Meilisearch-14" class="node default"><rect height="54" width="140.421875" y="-27" x="-70.2109375" style="" class="basic label-container"/><g transform="translate(-40.2109375, -12)" style="" class="label"><rect/><foreignObject height="24" width="80.421875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Meilisearch</p></span></div></foreignObject></g></g><g transform="translate(700.3984375, 700)" id="flowchart-Prometheus-15" class="node default"><rect height="54" width="144.015625" y="-27" x="-72.0078125" style="" class="basic label-container"/><g transform="translate(-42.0078125, -12)" style="" class="label"><rect/><foreignObject height="24" width="84.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Prometheus</p></span></div></foreignObject></g></g><g transform="translate(700.3984375, 829)" id="flowchart-Grafana-16" class="node default"><rect height="54" width="116.921875" y="-27" x="-58.4609375" style="" class="basic label-container"/><g transform="translate(-28.4609375, -12)" style="" class="label"><rect/><foreignObject height="24" width="56.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Grafana</p></span></div></foreignObject></g></g><g transform="translate(513.8203125, 700)" id="flowchart-ELK-17" class="node default"><rect height="54" width="129.140625" y="-27" x="-64.5703125" style="" class="basic label-container"/><g transform="translate(-34.5703125, -12)" style="" class="label"><rect/><foreignObject height="24" width="69.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ELK Stack</p></span></div></foreignObject></g></g></g></g></g></svg>