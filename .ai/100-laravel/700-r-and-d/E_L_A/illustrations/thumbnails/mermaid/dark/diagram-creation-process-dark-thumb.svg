<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 2188.95703125 1259" style="max-width: 2188.95703125px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#a44141;}#my-svg .error-text{fill:#ddd;stroke:#ddd;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#ecf0f1;stroke:#ecf0f1;}#my-svg .marker.cross{stroke:#ecf0f1;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#my-svg .cluster-label text{fill:#F9FFFE;}#my-svg .cluster-label span{color:#F9FFFE;}#my-svg .cluster-label span p{background-color:transparent;}#my-svg .label text,#my-svg span{fill:#ccc;color:#ccc;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#my-svg .rough-node .label text,#my-svg .node .label text,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-anchor:middle;}#my-svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#my-svg .rough-node .label,#my-svg .node .label,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-align:center;}#my-svg .node.clickable{cursor:pointer;}#my-svg .root .anchor path{fill:#ecf0f1!important;stroke-width:0;stroke:#ecf0f1;}#my-svg .arrowheadPath{fill:lightgrey;}#my-svg .edgePath .path{stroke:#ecf0f1;stroke-width:2.0px;}#my-svg .flowchart-link{stroke:#ecf0f1;fill:none;}#my-svg .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#my-svg .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#my-svg .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#my-svg .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#my-svg .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#my-svg .cluster text{fill:#F9FFFE;}#my-svg .cluster span{color:#F9FFFE;}#my-svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:#282c34;border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#my-svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#my-svg rect.text{fill:none;stroke-width:0;}#my-svg .icon-shape,#my-svg .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#my-svg .icon-shape p,#my-svg .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#my-svg .icon-shape rect,#my-svg .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"/></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><g class="root"><g class="clusters"><g data-look="classic" id="subGraph0" class="cluster"><rect height="104" width="960.984375" y="419" x="944.26171875" style=""/><g transform="translate(1373.40234375, 419)" class="cluster-label"><foreignObject height="24" width="102.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Diagram Types</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M1675.508,111.5L1675.508,123.917C1675.508,136.333,1675.508,161.167,1675.508,177.083C1675.508,193,1675.508,200,1675.508,203.5L1675.508,207"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_0" d="M1675.508,265L1675.508,269.167C1675.508,273.333,1675.508,281.667,1675.508,289.333C1675.508,297,1675.508,304,1675.508,307.5L1675.508,311"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_D_0" d="M1781.375,356.423L1827.347,362.685C1873.319,368.948,1965.263,381.474,2011.235,391.904C2057.207,402.333,2057.207,410.667,2057.207,418.333C2057.207,426,2057.207,433,2057.207,436.5L2057.207,440"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_E_0" d="M2057.207,498L2057.207,502.167C2057.207,506.333,2057.207,514.667,2057.207,523C2057.207,531.333,2057.207,539.667,2057.207,547.333C2057.207,555,2057.207,562,2057.207,565.5L2057.207,569"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_F_0" d="M2057.207,627L2057.207,631.167C2057.207,635.333,2057.207,643.667,2057.207,651.333C2057.207,659,2057.207,666,2057.207,669.5L2057.207,673"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_G_0" d="M2057.207,731L2057.207,735.167C2057.207,739.333,2057.207,747.667,2057.207,755.333C2057.207,763,2057.207,770,2057.207,773.5L2057.207,777"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_H_0" d="M2057.207,835L2057.207,839.167C2057.207,843.333,2057.207,851.667,2057.207,859.333C2057.207,867,2057.207,874,2057.207,877.5L2057.207,881"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_I_0" d="M2057.207,939L2057.207,943.167C2057.207,947.333,2057.207,955.667,2057.207,963.333C2057.207,971,2057.207,978,2057.207,981.5L2057.207,985"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_J_0" d="M2057.207,1043L2057.207,1047.167C2057.207,1051.333,2057.207,1059.667,2057.207,1067.333C2057.207,1075,2057.207,1082,2057.207,1085.5L2057.207,1089"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_K_0" d="M2057.207,1147L2057.207,1151.167C2057.207,1155.333,2057.207,1163.667,2057.207,1171.333C2057.207,1179,2057.207,1186,2057.207,1189.5L2057.207,1193"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_C1_0" d="M1569.641,350.73L1482.182,357.941C1394.723,365.153,1219.805,379.577,1132.346,390.955C1044.887,402.333,1044.887,410.667,1044.887,418.333C1044.887,426,1044.887,433,1044.887,436.5L1044.887,440"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_C2_0" d="M1569.641,353.685L1508.761,360.404C1447.882,367.123,1326.122,380.562,1265.243,391.447C1204.363,402.333,1204.363,410.667,1204.363,418.333C1204.363,426,1204.363,433,1204.363,436.5L1204.363,440"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_C3_0" d="M1569.641,359.577L1535.087,365.315C1500.533,371.052,1431.424,382.526,1396.87,392.43C1362.316,402.333,1362.316,410.667,1362.316,418.333C1362.316,426,1362.316,433,1362.316,436.5L1362.316,440"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_C4_0" d="M1596.968,369L1584.848,373.167C1572.727,377.333,1548.487,385.667,1536.366,394C1524.246,402.333,1524.246,410.667,1524.246,418.333C1524.246,426,1524.246,433,1524.246,436.5L1524.246,440"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_C5_0" d="M1673.08,369L1672.705,373.167C1672.331,377.333,1671.581,385.667,1671.207,394C1670.832,402.333,1670.832,410.667,1670.832,418.333C1670.832,426,1670.832,433,1670.832,436.5L1670.832,440"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_C6_0" d="M1750.49,369L1762.061,373.167C1773.633,377.333,1796.775,385.667,1808.347,394C1819.918,402.333,1819.918,410.667,1819.918,418.333C1819.918,426,1819.918,433,1819.918,436.5L1819.918,440"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(0, 0)" class="root"><g class="clusters"><g data-look="classic" id="subGraph1" class="cluster"><rect height="153" width="1570" y="8" x="8" style=""/><g transform="translate(743.90625, 8)" class="cluster-label"><foreignObject height="24" width="98.1875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>File Structure</p></span></div></foreignObject></g></g></g><g class="edgePaths"/><g class="edgeLabels"/><g class="nodes"><g transform="translate(173, 84.5)" id="flowchart-F1-38" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>mermaid/light/name-light.md</p></span></div></foreignObject></g></g><g transform="translate(483, 84.5)" id="flowchart-F2-39" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>mermaid/dark/name-dark.md</p></span></div></foreignObject></g></g><g transform="translate(793, 84.5)" id="flowchart-F3-40" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>plantuml/light/name-light.puml</p></span></div></foreignObject></g></g><g transform="translate(1103, 84.5)" id="flowchart-F4-41" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>plantuml/dark/name-dark.puml</p></span></div></foreignObject></g></g><g transform="translate(1413, 84.5)" id="flowchart-F5-42" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>thumbnails/name-thumb.svg</p></span></div></foreignObject></g></g></g></g><g transform="translate(1675.5078125, 84.5)" id="flowchart-A-0" class="node default"><rect height="54" width="95.015625" y="-27" x="-47.5078125" style="" class="basic label-container"/><g transform="translate(-17.5078125, -12)" style="" class="label"><rect/><foreignObject height="24" width="35.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Start</p></span></div></foreignObject></g></g><g transform="translate(1675.5078125, 238)" id="flowchart-B-1" class="node default"><rect height="54" width="220.484375" y="-27" x="-110.2421875" style="" class="basic label-container"/><g transform="translate(-80.2421875, -12)" style="" class="label"><rect/><foreignObject height="24" width="160.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Identify Diagram Need</p></span></div></foreignObject></g></g><g transform="translate(1675.5078125, 342)" id="flowchart-C-3" class="node default"><rect height="54" width="211.734375" y="-27" x="-105.8671875" style="" class="basic label-container"/><g transform="translate(-75.8671875, -12)" style="" class="label"><rect/><foreignObject height="24" width="151.734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Choose Diagram Type</p></span></div></foreignObject></g></g><g transform="translate(2057.20703125, 471)" id="flowchart-D-5" class="node default"><rect height="54" width="233.921875" y="-27" x="-116.9609375" style="" class="basic label-container"/><g transform="translate(-86.9609375, -12)" style="" class="label"><rect/><foreignObject height="24" width="173.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Create Draft in Mermaid</p></span></div></foreignObject></g></g><g transform="translate(2057.20703125, 600)" id="flowchart-E-7" class="node default"><rect height="54" width="247.5" y="-27" x="-123.75" style="" class="basic label-container"/><g transform="translate(-93.75, -12)" style="" class="label"><rect/><foreignObject height="24" width="187.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Create Light Mode Version</p></span></div></foreignObject></g></g><g transform="translate(2057.20703125, 704)" id="flowchart-F-9" class="node default"><rect height="54" width="244.21875" y="-27" x="-122.109375" style="" class="basic label-container"/><g transform="translate(-92.109375, -12)" style="" class="label"><rect/><foreignObject height="24" width="184.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Create Dark Mode Version</p></span></div></foreignObject></g></g><g transform="translate(2057.20703125, 808)" id="flowchart-G-11" class="node default"><rect height="54" width="207.5625" y="-27" x="-103.78125" style="" class="basic label-container"/><g transform="translate(-73.78125, -12)" style="" class="label"><rect/><foreignObject height="24" width="147.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Convert to PlantUML</p></span></div></foreignObject></g></g><g transform="translate(2057.20703125, 912)" id="flowchart-H-13" class="node default"><rect height="54" width="194.40625" y="-27" x="-97.203125" style="" class="basic label-container"/><g transform="translate(-67.203125, -12)" style="" class="label"><rect/><foreignObject height="24" width="134.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Create Thumbnails</p></span></div></foreignObject></g></g><g transform="translate(2057.20703125, 1016)" id="flowchart-I-15" class="node default"><rect height="54" width="150.6875" y="-27" x="-75.34375" style="" class="basic label-container"/><g transform="translate(-45.34375, -12)" style="" class="label"><rect/><foreignObject height="24" width="90.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Add to Index</p></span></div></foreignObject></g></g><g transform="translate(2057.20703125, 1120)" id="flowchart-J-17" class="node default"><rect height="54" width="241.375" y="-27" x="-120.6875" style="" class="basic label-container"/><g transform="translate(-90.6875, -12)" style="" class="label"><rect/><foreignObject height="24" width="181.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Update Source Document</p></span></div></foreignObject></g></g><g transform="translate(2057.20703125, 1224)" id="flowchart-K-19" class="node default"><rect height="54" width="86.234375" y="-27" x="-43.1171875" style="" class="basic label-container"/><g transform="translate(-13.1171875, -12)" style="" class="label"><rect/><foreignObject height="24" width="26.234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>End</p></span></div></foreignObject></g></g><g transform="translate(1044.88671875, 471)" id="flowchart-C1-20" class="node default"><rect height="54" width="131.25" y="-27" x="-65.625" style="" class="basic label-container"/><g transform="translate(-35.625, -12)" style="" class="label"><rect/><foreignObject height="24" width="71.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Flowchart</p></span></div></foreignObject></g></g><g transform="translate(1204.36328125, 471)" id="flowchart-C2-21" class="node default"><rect height="54" width="87.703125" y="-27" x="-43.8515625" style="" class="basic label-container"/><g transform="translate(-13.8515625, -12)" style="" class="label"><rect/><foreignObject height="24" width="27.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ERD</p></span></div></foreignObject></g></g><g transform="translate(1362.31640625, 471)" id="flowchart-C3-22" class="node default"><rect height="54" width="128.203125" y="-27" x="-64.1015625" style="" class="basic label-container"/><g transform="translate(-34.1015625, -12)" style="" class="label"><rect/><foreignObject height="24" width="68.203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Sequence</p></span></div></foreignObject></g></g><g transform="translate(1524.24609375, 471)" id="flowchart-C4-23" class="node default"><rect height="54" width="95.65625" y="-27" x="-47.828125" style="" class="basic label-container"/><g transform="translate(-17.828125, -12)" style="" class="label"><rect/><foreignObject height="24" width="35.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Class</p></span></div></foreignObject></g></g><g transform="translate(1670.83203125, 471)" id="flowchart-C5-24" class="node default"><rect height="54" width="97.515625" y="-27" x="-48.7578125" style="" class="basic label-container"/><g transform="translate(-18.7578125, -12)" style="" class="label"><rect/><foreignObject height="24" width="37.515625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>State</p></span></div></foreignObject></g></g><g transform="translate(1819.91796875, 471)" id="flowchart-C6-25" class="node default"><rect height="54" width="100.65625" y="-27" x="-50.328125" style="" class="basic label-container"/><g transform="translate(-20.328125, -12)" style="" class="label"><rect/><foreignObject height="24" width="40.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Gantt</p></span></div></foreignObject></g></g></g></g></g></svg>