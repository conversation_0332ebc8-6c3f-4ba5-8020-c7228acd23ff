<svg aria-roledescription="gantt" role="graphics-document document" style="max-width: 284px; background-color: #2d333b;" viewBox="0 0 284 580" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#a44141;}#my-svg .error-text{fill:#ddd;stroke:#ddd;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#ecf0f1;stroke:#ecf0f1;}#my-svg .marker.cross{stroke:#ecf0f1;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg .mermaid-main-font{font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg .exclude-range{fill:hsl(52.9411764706, 28.813559322%, 48.431372549%);}#my-svg .section{stroke:none;opacity:0.2;}#my-svg .section0{fill:hsl(52.9411764706, 28.813559322%, 58.431372549%);}#my-svg .section2{fill:#EAE8D9;}#my-svg .section1,#my-svg .section3{fill:#333;opacity:0.2;}#my-svg .sectionTitle0{fill:#F9FFFE;}#my-svg .sectionTitle1{fill:#F9FFFE;}#my-svg .sectionTitle2{fill:#F9FFFE;}#my-svg .sectionTitle3{fill:#F9FFFE;}#my-svg .sectionTitle{text-anchor:start;font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg .grid .tick{stroke:lightgrey;opacity:0.8;shape-rendering:crispEdges;}#my-svg .grid .tick text{font-family:"trebuchet ms",verdana,arial,sans-serif;fill:#ccc;}#my-svg .grid path{stroke-width:0;}#my-svg .today{fill:none;stroke:#DB5757;stroke-width:2px;}#my-svg .task{stroke-width:2;}#my-svg .taskText{text-anchor:middle;font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg .taskTextOutsideRight{fill:hsl(28.5714285714, 17.3553719008%, 86.2745098039%);text-anchor:start;font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg .taskTextOutsideLeft{fill:hsl(28.5714285714, 17.3553719008%, 86.2745098039%);text-anchor:end;}#my-svg .task.clickable{cursor:pointer;}#my-svg .taskText.clickable{cursor:pointer;fill:#003163!important;font-weight:bold;}#my-svg .taskTextOutsideLeft.clickable{cursor:pointer;fill:#003163!important;font-weight:bold;}#my-svg .taskTextOutsideRight.clickable{cursor:pointer;fill:#003163!important;font-weight:bold;}#my-svg .taskText0,#my-svg .taskText1,#my-svg .taskText2,#my-svg .taskText3{fill:hsl(28.5714285714, 17.3553719008%, 86.2745098039%);}#my-svg .task0,#my-svg .task1,#my-svg .task2,#my-svg .task3{fill:hsl(180, 1.5873015873%, 35.3529411765%);stroke:#ffffff;}#my-svg .taskTextOutside0,#my-svg .taskTextOutside2{fill:lightgrey;}#my-svg .taskTextOutside1,#my-svg .taskTextOutside3{fill:lightgrey;}#my-svg .active0,#my-svg .active1,#my-svg .active2,#my-svg .active3{fill:#81B1DB;stroke:#ffffff;}#my-svg .activeText0,#my-svg .activeText1,#my-svg .activeText2,#my-svg .activeText3{fill:hsl(28.5714285714, 17.3553719008%, 86.2745098039%)!important;}#my-svg .done0,#my-svg .done1,#my-svg .done2,#my-svg .done3{stroke:grey;fill:lightgrey;stroke-width:2;}#my-svg .doneText0,#my-svg .doneText1,#my-svg .doneText2,#my-svg .doneText3{fill:hsl(28.5714285714, 17.3553719008%, 86.2745098039%)!important;}#my-svg .crit0,#my-svg .crit1,#my-svg .crit2,#my-svg .crit3{stroke:#E83737;fill:#E83737;stroke-width:2;}#my-svg .activeCrit0,#my-svg .activeCrit1,#my-svg .activeCrit2,#my-svg .activeCrit3{stroke:#E83737;fill:#81B1DB;stroke-width:2;}#my-svg .doneCrit0,#my-svg .doneCrit1,#my-svg .doneCrit2,#my-svg .doneCrit3{stroke:#E83737;fill:lightgrey;stroke-width:2;cursor:pointer;shape-rendering:crispEdges;}#my-svg .milestone{transform:rotate(45deg) scale(0.8,0.8);}#my-svg .milestoneText{font-style:italic;}#my-svg .doneCritText0,#my-svg .doneCritText1,#my-svg .doneCritText2,#my-svg .doneCritText3{fill:hsl(28.5714285714, 17.3553719008%, 86.2745098039%)!important;}#my-svg .activeCritText0,#my-svg .activeCritText1,#my-svg .activeCritText2,#my-svg .activeCritText3{fill:hsl(28.5714285714, 17.3553719008%, 86.2745098039%)!important;}#my-svg .titleText{text-anchor:middle;font-size:18px;fill:#F9FFFE;font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g/><g text-anchor="middle" font-family="sans-serif" font-size="10" fill="none" transform="translate(75, 530)" class="grid"><path d="M0.5,-495V0.5H134.5V-495" stroke="currentColor" class="domain"/><g transform="translate(0.5,0)" opacity="1" class="tick"><line y2="-495" stroke="currentColor"/><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">2025-01-01</text></g><g transform="translate(26.5,0)" opacity="1" class="tick"><line y2="-495" stroke="currentColor"/><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">2025-02-01</text></g><g transform="translate(49.5,0)" opacity="1" class="tick"><line y2="-495" stroke="currentColor"/><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">2025-03-01</text></g><g transform="translate(75.5,0)" opacity="1" class="tick"><line y2="-495" stroke="currentColor"/><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">2025-04-01</text></g><g transform="translate(100.5,0)" opacity="1" class="tick"><line y2="-495" stroke="currentColor"/><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">2025-05-01</text></g><g transform="translate(126.5,0)" opacity="1" class="tick"><line y2="-495" stroke="currentColor"/><text style="text-anchor: middle;" font-size="10" stroke="none" dy="1em" y="3" fill="#000">2025-06-01</text></g></g><g><rect class="section section0" height="24" width="246.5" y="48" x="0"/><rect class="section section0" height="24" width="246.5" y="72" x="0"/><rect class="section section0" height="24" width="246.5" y="96" x="0"/><rect class="section section1" height="24" width="246.5" y="120" x="0"/><rect class="section section1" height="24" width="246.5" y="144" x="0"/><rect class="section section1" height="24" width="246.5" y="168" x="0"/><rect class="section section1" height="24" width="246.5" y="240" x="0"/><rect class="section section1" height="24" width="246.5" y="192" x="0"/><rect class="section section2" height="24" width="246.5" y="312" x="0"/><rect class="section section1" height="24" width="246.5" y="216" x="0"/><rect class="section section2" height="24" width="246.5" y="264" x="0"/><rect class="section section2" height="24" width="246.5" y="336" x="0"/><rect class="section section2" height="24" width="246.5" y="288" x="0"/><rect class="section section2" height="24" width="246.5" y="360" x="0"/><rect class="section section3" height="24" width="246.5" y="408" x="0"/><rect class="section section3" height="24" width="246.5" y="456" x="0"/><rect class="section section2" height="24" width="246.5" y="384" x="0"/><rect class="section section3" height="24" width="246.5" y="432" x="0"/><rect class="section section0" height="24" width="246.5" y="480" x="0"/><rect class="section section0" height="24" width="246.5" y="504" x="0"/></g><g><rect class="task task0" transform-origin="81px 60px" height="20" width="12" y="50" x="75" ry="3" rx="3" id="a1"/><rect class="task task0" transform-origin="95.5px 84px" height="20" width="17" y="74" x="87" ry="3" rx="3" id="a2"/><rect class="task task0" transform-origin="98.5px 108px" height="20" width="23" y="98" x="87" ry="3" rx="3" id="a3"/><rect class="task task1" transform-origin="92.5px 132px" height="20" width="11" y="122" x="87" ry="3" rx="3" id="b1"/><rect class="task task1" transform-origin="104px 156px" height="20" width="12" y="146" x="98" ry="3" rx="3" id="b2"/><rect class="task task1" transform-origin="118.5px 180px" height="20" width="17" y="170" x="110" ry="3" rx="3" id="b3"/><rect class="task task1" transform-origin="121.5px 252px" height="20" width="23" y="242" x="110" ry="3" rx="3" id="b6"/><rect class="task task1" transform-origin="133px 204px" height="20" width="12" y="194" x="127" ry="3" rx="3" id="b4"/><rect class="task task2" transform-origin="136px 324px" height="20" width="18" y="314" x="127" ry="3" rx="3" id="c3"/><rect class="task task1" transform-origin="145px 228px" height="20" width="12" y="218" x="139" ry="3" rx="3" id="b5"/><rect class="task task2" transform-origin="145px 276px" height="20" width="12" y="266" x="139" ry="3" rx="3" id="c1"/><rect class="task task2" transform-origin="156.5px 348px" height="20" width="23" y="338" x="145" ry="3" rx="3" id="c4"/><rect class="task task2" transform-origin="159.5px 300px" height="20" width="17" y="290" x="151" ry="3" rx="3" id="c2"/><rect class="task task2" transform-origin="177px 372px" height="20" width="18" y="362" x="168" ry="3" rx="3" id="c5"/><rect class="task task3" transform-origin="174px 420px" height="20" width="12" y="410" x="168" ry="3" rx="3" id="d1"/><rect class="task task3" transform-origin="186px 468px" height="20" width="12" y="458" x="180" ry="3" rx="3" id="d3"/><rect class="task task2" transform-origin="194.5px 396px" height="20" width="17" y="386" x="186" ry="3" rx="3" id="c6"/><rect class="task task3" transform-origin="191.5px 444px" height="20" width="11" y="434" x="186" ry="3" rx="3" id="d2"/><rect class="task task0" transform-origin="194.5px 492px" height="20" width="5" y="482" x="192" ry="3" rx="3" id="e1"/><rect class="task task0" transform-origin="203px 516px" height="20" width="12" y="506" x="197" ry="3" rx="3" id="e2"/><text class="taskTextOutsideLeft taskTextOutside0" y="63.5" x="70" font-size="11" id="a1-text">Technical Architecture Document    </text><text class="taskTextOutsideRight taskTextOutside0  width-64.40625" y="87.5" x="109" font-size="11" id="a2-text">UI/UX Design                       </text><text class="taskTextOutsideRight taskTextOutside0  width-79.96875" y="111.5" x="115" font-size="11" id="a3-text">Technical Spikes                   </text><text class="taskTextOutsideLeft taskTextOutside1" y="135.5" x="82" font-size="11" id="b1-text">Database Schema Implementation     </text><text class="taskTextOutsideLeft taskTextOutside1" y="159.5" x="93" font-size="11" id="b2-text">Authentication &amp; Authorization     </text><text class="taskTextOutsideLeft taskTextOutside1" y="183.5" x="105" font-size="11" id="b3-text">User &amp; Team Management             </text><text class="taskTextOutsideLeft taskTextOutside1" y="255.5" x="105" font-size="11" id="b6-text">Admin Portal (Filament)            </text><text class="taskTextOutsideLeft taskTextOutside1" y="207.5" x="122" font-size="11" id="b4-text">Category Management                </text><text class="taskTextOutsideLeft taskTextOutside2" y="327.5" x="122" font-size="11" id="c3-text">Basic Chat Functionality           </text><text class="taskTextOutsideRight taskTextOutside1  width-88.671875" y="231.5" x="156" font-size="11" id="b5-text">Todo Management                    </text><text class="taskTextOutsideLeft taskTextOutside2" y="279.5" x="134" font-size="11" id="c1-text">Advanced Team &amp; Category Management </text><text class="taskTextOutsideLeft taskTextOutside2" y="351.5" x="140" font-size="11" id="c4-text">Advanced Chat Features             </text><text class="taskTextOutsideLeft taskTextOutside2" y="303.5" x="146" font-size="11" id="c2-text">Blogging Feature                   </text><text class="taskTextOutsideRight taskTextOutside2  width-48.5" y="375.5" x="191" font-size="11" id="c5-text">Public API                         </text><text class="taskTextOutsideLeft taskTextOutside3" y="423.5" x="163" font-size="11" id="d1-text">Performance Optimization           </text><text class="taskTextOutsideLeft taskTextOutside3" y="471.5" x="175" font-size="11" id="d3-text">User Acceptance Testing            </text><text class="taskTextOutsideLeft taskTextOutside2" y="399.5" x="181" font-size="11" id="c6-text">Advanced Reporting                 </text><text class="taskTextOutsideLeft taskTextOutside3" y="447.5" x="181" font-size="11" id="d2-text">Security Testing                   </text><text class="taskTextOutsideLeft taskTextOutside0" y="495.5" x="187" font-size="11" id="e1-text">Production Deployment              </text><text class="taskTextOutsideLeft taskTextOutside0" y="519.5" x="192" font-size="11" id="e2-text">User Training                      </text></g><g><text class="sectionTitle sectionTitle0" font-size="11" y="86" x="10" dy="0em"><tspan x="10" alignment-baseline="central">Planning &amp; Architecture</tspan></text><text class="sectionTitle sectionTitle1" font-size="11" y="194" x="10" dy="0em"><tspan x="10" alignment-baseline="central">Core Development</tspan></text><text class="sectionTitle sectionTitle2" font-size="11" y="338" x="10" dy="0em"><tspan x="10" alignment-baseline="central">Advanced Features</tspan></text><text class="sectionTitle sectionTitle3" font-size="11" y="446" x="10" dy="0em"><tspan x="10" alignment-baseline="central">Testing &amp; Refinement</tspan></text><text class="sectionTitle sectionTitle0" font-size="11" y="506" x="10" dy="0em"><tspan x="10" alignment-baseline="central">Deployment &amp; Training</tspan></text></g><g class="today"><line class="today" y2="555" y1="25" x2="188" x1="188"/></g><text class="titleText" y="25" x="142">Resource Allocation Timeline</text></svg>