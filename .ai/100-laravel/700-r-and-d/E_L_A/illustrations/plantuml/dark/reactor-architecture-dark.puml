
@startuml Reactor Architecture Dark (Dark Mode)

' Dark mode theme
!theme cyborg
skinparam backgroundColor #282c34
skinparam ArrowColor #ecf0f1
skinparam shadowing false
skinparam DefaultFontColor #FFFFFF

' Define styles for classes
skinparam class {
    BackgroundColor #2c3e50
    BorderColor #7f8c8d
    FontColor #FFFFFF
}

skinparam interface {
    BackgroundColor #34495e
    BorderColor #7f8c8d
    FontColor #FFFFFF
}


    class EventStore {

        +StoredEvent[] events
        +persist(Event event)
        +retrieveAll()
        +retrieveAllForAggregate(string uuid)
    
}
    
    class Reactor {

        +onUserRegistered(UserRegistered event)
        +onTeamCreated(TeamCreated event)
        +onPostPublished(PostPublished event)
    
}
    
    class QueuedReactor {

        +queue: string
        +connection: string
        +delay: int
    
}
    
    class SideEffect {

        +execute()
        +rollback()
    
}
    
    class EmailNotification {

        +send()
    
}
    
    class PushNotification {

        +send()
    
}
    
    class ExternalAPICall {

        +execute()
    
}
    
    EventStore --> Reactor: events
    Reactor <|-- QueuedReactor
    Reactor --> SideEffect: triggers
    SideEffect <|-- EmailNotification
    SideEffect <|-- PushNotification
    SideEffect <|-- ExternalAPICall
@enduml