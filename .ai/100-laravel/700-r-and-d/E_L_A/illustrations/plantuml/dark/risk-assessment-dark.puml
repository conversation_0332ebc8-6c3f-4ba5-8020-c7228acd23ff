@startuml Risk Assessment (Dark Mode)

' Dark mode theme
!theme cyborg
skinparam backgroundColor #282c34
skinparam ArrowColor #ecf0f1
skinparam shadowing false
skinparam DefaultFontColor #FFFFFF

' Define styles
skinparam rectangle {
    BackgroundColor #2c3e50
    BorderColor #7f8c8d
    FontColor #FFFFFF
}

' Create a 2x2 grid for risk assessment
rectangle "High Impact\nLow Probability" as HL #3498db
rectangle "High Impact\nHigh Probability" as HH #e74c3c
rectangle "Low Impact\nLow Probability" as LL #2ecc71
rectangle "Low Impact\nHigh Probability" as LH #f39c12

' Position the quadrants
HL -[hidden]right- HH
LL -[hidden]right- LH
HL -[hidden]down- LL
HH -[hidden]down- LH

' Add risks to each quadrant
rectangle "Performance bottlenecks" as R1 #3498db
rectangle "Security vulnerabilities" as R2 #3498db

rectangle "Laravel 12 compatibility issues" as R3 #e74c3c
rectangle "User adoption challenges" as R4 #e74c3c

rectangle "Integration issues" as R5 #2ecc71
rectangle "Deployment delays" as R6 #2ecc71

rectangle "Scope creep" as R7 #f39c12
rectangle "Team resource constraints" as R8 #f39c12

' Position risks within quadrants
HL -[hidden]down- R1
R1 -[hidden]down- R2

HH -[hidden]down- R3
R3 -[hidden]down- R4

LL -[hidden]down- R5
R5 -[hidden]down- R6

LH -[hidden]down- R7
R7 -[hidden]down- R8

' Add labels
note top of HL : Critical Risks: Monitor closely
note top of HH : High Priority: Address immediately
note bottom of LL : Low Priority: Monitor occasionally
note bottom of LH : Medium Priority: Plan mitigation

@enduml
