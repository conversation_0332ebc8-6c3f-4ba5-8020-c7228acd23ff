@startuml Event Sourcing Flow (Dark Mode)

' Dark mode theme
!theme cyborg
skinparam backgroundColor #282c34
skinparam ArrowColor #ecf0f1
skinparam shadowing false
skinparam DefaultFontColor #FFFFFF

' Define styles
skinparam rectangle {
    BackgroundColor #2c3e50
    BorderColor #7f8c8d
    FontColor #FFFFFF
}

skinparam database {
    BackgroundColor #1a237e
    BorderColor #3949ab
}

skinparam note {
    BackgroundColor #4a148c
    BorderColor #7b1fa2
    FontColor #FFFFFF
}

' Components
rectangle "Domain Event" as A
database "Event Store" as B
rectangle "Event Stream" as C
rectangle "Projector" as D
rectangle "Read Model" as E
rectangle "Reactor" as F
rectangle "Side Effects" as G
rectangle "Aggregate" as H
rectangle "Command" as I

' Connections
A -down-> B
B -down-> C
C -down-> D
D -down-> E
A -right-> F
F -down-> G
H -up-> A : "Applies"
I -right-> H : "Handled by"

' Add notes
note right of A
  Immutable record of something
  that happened in the domain
end note

note right of B
  Persistent storage for all events
end note

note right of H
  Domain objects that handle
  commands and apply events
end note

note right of D
  Build and maintain read models
  based on events
end note

note right of F
  Execute side effects when
  specific events occur
end note

@enduml
