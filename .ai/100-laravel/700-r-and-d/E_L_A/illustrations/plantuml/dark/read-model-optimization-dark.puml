
@startuml Read Model Optimization Dark (Dark Mode)

' Dark mode theme
!theme cyborg
skinparam backgroundColor #282c34
skinparam ArrowColor #ecf0f1
skinparam shadowing false
skinparam DefaultFontColor #FFFFFF

' Define styles
skinparam rectangle {
    BackgroundColor #2c3e50
    BorderColor #7f8c8d
    FontColor #FFFFFF
}

skinparam database {
    BackgroundColor #1a237e
    BorderColor #3949ab
}


    rectangle "Event Store" as A --> rectangle "Projector" as B
    B --> rectangle "Base Read Model" as C
    C --> rectangle "Indexed Columns" as D
    C --> rectangle "Denormalized Data" as E
    C --> rectangle "Cached Results" as F
    C --> rectangle "Full-text Search" as G
    D --> rectangle "Fast Lookups" as H
    E --> rectangle "Reduced Joins" as I
    F --> rectangle "Reduced Database Load" as J
    G --> rectangle "Efficient Text Search" as K
@enduml