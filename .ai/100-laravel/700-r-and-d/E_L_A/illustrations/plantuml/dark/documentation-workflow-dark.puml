@startuml Documentation Workflow (Dark Mode)

' Dark mode theme
!theme cyborg
skinparam backgroundColor #282c34
skinparam ArrowColor #ecf0f1
skinparam shadowing false
skinparam DefaultFontColor #FFFFFF

' Define styles
skinparam activity {
    BackgroundColor #2c3e50
    BorderColor #7f8c8d
    FontColor #FFFFFF
}

' Start
start

' Planning
:Planning Phase;
:Identify Documentation Needs;
:Create Documentation Plan;
:Define Document Structure;

' Creation
:Creation Phase;
:Draft Document Content;
:Create Initial Diagrams;
:Add Code Examples;
:Format According to Style Guide;

' Review
:Review Phase;
:Technical Review;
:Editorial Review;
:Stakeholder Review;
:Incorporate Feedback;

' Finalization
:Finalization Phase;
:Final Formatting Check;
:Ensure Diagram Consistency;
:Verify Links and References;
:Publish Documentation;

' Maintenance
:Maintenance Phase;
:Monitor for Accuracy;
:Update as Needed;
:Version Control;
:Archive Outdated Versions;

' End
stop

@enduml
