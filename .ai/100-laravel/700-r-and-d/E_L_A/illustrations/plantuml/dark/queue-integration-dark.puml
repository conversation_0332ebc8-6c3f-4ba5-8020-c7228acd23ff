
@startuml Queue Integration Dark (Dark Mode)

' Dark mode theme
!theme cyborg
skinparam backgroundColor #282c34
skinparam ArrowColor #ecf0f1
skinparam shadowing false
skinparam DefaultFontColor #FFFFFF

' Define styles
skinparam participant {
    BackgroundColor #2c3e50
    BorderColor #7f8c8d
    FontColor #FFFFFF
}

skinparam actor {
    BackgroundColor #34495e
    BorderColor #7f8c8d
    FontColor #FFFFFF
}


    participant A as Aggregate
    participant E as Event Store
    participant R as Reactor
    participant Q as Queue
    participant W as Queue Worker
    participant S as Side Effect
    
    A -> E: Store Event
    E -> R: Dispatch Event
    R -> Q: Push Job
    Q -> W: Process Job
    W -> S: Execute Side Effect
    alt Success
        S -> W: Return Success
        W -> Q: Mark Job as Completed
    else Failure
        S -> W: Return Failure
        W -> Q: Retry Job
    end
@enduml