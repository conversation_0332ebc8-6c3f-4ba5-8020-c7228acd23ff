@startuml Technology Stack (Dark Mode)

' Dark mode theme
!theme cyborg
skinparam backgroundColor #282c34
skinparam ArrowColor #ecf0f1
skinparam shadowing false
skinparam DefaultFontColor #FFFFFF

' Define styles
skinparam rectangle {
    BackgroundColor #2c3e50
    BorderColor #7f8c8d
    FontColor #FFFFFF
}

' Main components
rectangle "Enhanced Laravel Application" as A

' Main categories
rectangle "Backend" as B
rectangle "Frontend" as C
rectangle "Infrastructure" as D
rectangle "DevOps" as E

' Backend components
rectangle "Laravel 12.x" as B1
rectangle "PHP 8.4.x" as B2
rectangle "PostgreSQL 16.x" as B3
rectangle "Redis 7.x" as B4
rectangle "FrankenPHP 1.x" as B5
rectangle "Meilisearch 1.x" as B6

' Frontend components
rectangle "Livewire/Volt 3.x" as C1
rectangle "Tailwind CSS 4.x" as C2
rectangle "Filament 3.x" as C3
rectangle "Alpine.js 3.x" as C4

' Infrastructure components
rectangle "S3-compatible Storage" as D1
rectangle "Redis Cloud" as D2
rectangle "PostgreSQL Database" as D3
rectangle "CDN" as D4

' DevOps components
rectangle "GitHub Actions" as E1
rectangle "Docker" as E2
rectangle "Pest PHP" as E3
rectangle "Laravel Herd" as E4

' Connections
A -down-> B
A -down-> C
A -down-> D
A -down-> E

B -down-> B1
B -down-> B2
B -down-> B3
B -down-> B4
B -down-> B5
B -down-> B6

C -down-> C1
C -down-> C2
C -down-> C3
C -down-> C4

D -down-> D1
D -down-> D2
D -down-> D3
D -down-> D4

E -down-> E1
E -down-> E2
E -down-> E3
E -down-> E4

@enduml
