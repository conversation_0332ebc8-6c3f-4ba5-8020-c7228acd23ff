
@startuml Read Model Architecture Dark (Dark Mode)

' Dark mode theme
!theme cyborg
skinparam backgroundColor #282c34
skinparam ArrowColor #ecf0f1
skinparam shadowing false
skinparam DefaultFontColor #FFFFFF

' Define styles for classes
skinparam class {
    BackgroundColor #2c3e50
    BorderColor #7f8c8d
    FontColor #FFFFFF
}

skinparam interface {
    BackgroundColor #34495e
    BorderColor #7f8c8d
    FontColor #FFFFFF
}


    class EventStore {

        +StoredEvent[] events
        +persist(Event event)
        +retrieveAll()
        +retrieveAllForAggregate(string uuid)
    
}
    
    class Projector {

        +onUserCreated(UserCreated event)
        +onUserUpdated(UserUpdated event)
        +onUserDeleted(UserDeleted event)
        +reset()
    
}
    
    class ReadModel {

        +id
        +attributes
        +create()
        +update()
        +delete()
    
}
    
    EventStore --> Projector: events
    Projector --> ReadModel: updates
@enduml