@startuml
!theme light
skinparam backgroundColor #ffffff
skinparam arrowColor #666666
skinparam nodeFontColor #333333
skinparam nodeBorderColor #cccccc
skinparam nodeBorderThickness 1
skinparam nodeBackgroundColor #f5f5f5

node "Documentation Root" as A
node "Executive Summary" as B
node "Product Requirements" as C
node "Project Roadmap" as D
node "Technical Architecture" as E
node "Implementation Plan" as F
node "Questions & Decisions Log" as G
node "Reference Documents" as H

node "Project Overview" as B1
node "Business Value" as B2
node "Success Metrics" as B3

node "Features" as C1
node "Requirements" as C2
node "User Stories" as C3

node "Timeline" as D1
node "Milestones" as D2
node "Resource Allocation" as D3

node "Architecture Overview" as E1
node "Database Schema" as E2
node "API Specifications" as E3
node "Security Architecture" as E4

node "Setup Instructions" as F1
node "Configuration" as F2
node "Implementation Details" as F3
node "Testing" as F4

node "Glossary" as H1
node "Coding Standards" as H2
node "Documentation Style Guide" as H3
node "Illustrations" as H4

node "Diagrams" as H4A
node "Screenshots" as H4B
node "Mockups" as H4C

A --> B
A --> C
A --> D
A --> E
A --> F
A --> G
A --> H

B --> B1
B --> B2
B --> B3

C --> C1
C --> C2
C --> C3

D --> D1
D --> D2
D --> D3

E --> E1
E --> E2
E --> E3
E --> E4

F --> F1
F --> F2
F --> F3
F --> F4

H --> H1
H --> H2
H --> H3
H --> H4

H4 --> H4A
H4 --> H4B
H4 --> H4C

@enduml
