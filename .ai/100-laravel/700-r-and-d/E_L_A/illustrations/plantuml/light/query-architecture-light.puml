
@startuml Query Architecture Light (Light Mode)

' Dark mode theme
!theme cerulean
skinparam backgroundColor #ffffff
skinparam ArrowColor #666666
skinparam shadowing false
skinparam DefaultFontColor #333333

' Define styles for classes
skinparam class {
    BackgroundColor #f5f5f5
    BorderColor #cccccc
    FontColor #333333
}

skinparam interface {
    BackgroundColor #e6f7ff
    BorderColor #cccccc
    FontColor #333333
}


    class Query {

        +validate()
        +rules()
    
}
    
    class QueryHandler {

        +handle(Query query)
    
}
    
    class ReadModel {

        +id
        +attributes
        +find()
        +findAll()
    
}
    
    class QueryResult {

        +data
        +meta
    
}
    
    Query --> QueryHandler: processed by
    QueryHandler --> ReadModel: retrieves from
    ReadModel --> QueryResult: transformed to
@enduml