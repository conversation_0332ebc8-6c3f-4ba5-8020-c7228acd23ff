
@startuml Read Model Architecture Light (Light Mode)

' Dark mode theme
!theme cerulean
skinparam backgroundColor #ffffff
skinparam ArrowColor #666666
skinparam shadowing false
skinparam DefaultFontColor #333333

' Define styles for classes
skinparam class {
    BackgroundColor #f5f5f5
    BorderColor #cccccc
    FontColor #333333
}

skinparam interface {
    BackgroundColor #e6f7ff
    BorderColor #cccccc
    FontColor #333333
}


    class EventStore {

        +StoredEvent[] events
        +persist(Event event)
        +retrieveAll()
        +retrieveAllForAggregate(string uuid)
    
}
    
    class Projector {

        +onUserCreated(UserCreated event)
        +onUserUpdated(UserUpdated event)
        +onUserDeleted(UserDeleted event)
        +reset()
    
}
    
    class ReadModel {

        +id
        +attributes
        +create()
        +update()
        +delete()
    
}
    
    EventStore --> Projector: events
    Projector --> ReadModel: updates
@enduml