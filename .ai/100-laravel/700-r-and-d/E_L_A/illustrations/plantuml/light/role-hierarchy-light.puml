
@startuml Role Hierarchy Light (Light Mode)

' Dark mode theme
!theme cerulean
skinparam backgroundColor #ffffff
skinparam ArrowColor #666666
skinparam shadowing false
skinparam DefaultFontColor #333333

' Define styles
skinparam rectangle {
    BackgroundColor #f5f5f5
    BorderColor #cccccc
    FontColor #333333
}

skinparam database {
    BackgroundColor #e6f7ff
    BorderColor #91d5ff
}


    rectangle "Super Administrator" as SuperAdmin --> rectangle "Administrator" as Admin
    Admin --> rectangle "User" as User
    
    rectangle "Team Owner" as TeamOwner --> rectangle "Team Administrator" as TeamAdmin
    TeamAdmin --> rectangle "Team Member" as TeamMember
    
    SuperAdmin -.-> TeamOwner
    Admin -.-> TeamAdmin
    User -.-> TeamMember
    
    classDef global fill:#f9f9f9,stroke:#333,stroke-width:1px
    classDef team fill:#e6f7ff,stroke:#333,stroke-width:1px
    
    class SuperAd<PERSON>,<PERSON><PERSON>,User global
    class TeamOwner,Team<PERSON>d<PERSON>,TeamMember team
@enduml