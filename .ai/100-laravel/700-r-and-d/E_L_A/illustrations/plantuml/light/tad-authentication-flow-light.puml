@startuml TAD Authentication Flow (Light Mode)

' Light mode theme
!theme cerulean
skinparam backgroundColor #FFFFFF
skinparam ArrowColor #666666
skinparam shadowing false
skinparam DefaultFontColor #333333

' Define styles
skinparam participant {
    BackgroundColor #f5f5f5
    BorderColor #cccccc
    FontColor #333333
}

' Participants
actor User
participant Browser
participant App as "Laravel Application"
participant Auth as "Authentication Service"
participant MFA as "MFA Service"
participant DB as "Database"

' Registration Flow
note over User, DB: Registration Flow
User -> Browser: Access Registration Page
Browser -> App: GET /register
App --> Browser: Return Registration Form
User -> Browser: Fill Registration Form
Browser -> App: POST /register
App -> Auth: Validate Registration Data
Auth -> DB: Create User Account
DB --> Auth: User Created
Auth --> App: Return Success
App --> Browser: Redirect to Email Verification

' Login Flow
note over User, DB: Login Flow
User -> Browser: Access Login Page
Browser -> App: GET /login
App --> Browser: Return Login Form
User -> Browser: Enter Credentials
Browser -> App: POST /login
App -> Auth: Validate Credentials
Auth -> DB: Check Credentials
DB --> Auth: Credentials Valid
Auth -> DB: Check MFA Enabled
DB --> Auth: MFA Status

alt MFA Enabled
    Auth -> App: Request MFA Code
    App --> Browser: Show MFA Input Form
    User -> Browser: Enter MFA Code
    Browser -> App: POST MFA Code
    App -> MFA: Validate MFA Code
    MFA --> App: MFA Valid
end

Auth -> App: Create Session
App --> Browser: Redirect to Dashboard

@enduml
