
@startuml Query Flow Light (Light Mode)

' Dark mode theme
!theme cerulean
skinparam backgroundColor #ffffff
skinparam ArrowColor #666666
skinparam shadowing false
skinparam DefaultFontColor #333333

' Define styles
skinparam rectangle {
    BackgroundColor #f5f5f5
    BorderColor #cccccc
    FontColor #333333
}

skinparam database {
    BackgroundColor #e6f7ff
    BorderColor #91d5ff
}


    rectangle "Client" as A --> rectangle "Query" as B
    B --> rectangle "Query Handler" as C
    C --> rectangle "Read Model" as D
    D --> rectangle "Query Result" as E
    E --> A
@enduml