@startuml TAD Event Flow (Light Mode)

' Light mode theme
!theme cerulean
skinparam backgroundColor #FFFFFF
skinparam ArrowColor #666666
skinparam shadowing false
skinparam DefaultFontColor #333333

' Define styles
skinparam rectangle {
    BackgroundColor #f5f5f5
    BorderColor #cccccc
    FontColor #333333
}

' Main flow
rectangle "User Action / System Trigger" as A
rectangle "Controller / Service" as B
rectangle "Domain Logic" as C
rectangle "Event Dispatch" as D

' Event listeners
rectangle "Synchronous Listeners" as E1
rectangle "Queued Listeners" as E2

' Effects
rectangle "Immediate Side Effects" as F1
rectangle "Background Processing" as F2

' Immediate effects
rectangle "Cache Updates" as G1
rectangle "UI Updates" as G2

' Background effects
rectangle "Email Notifications" as H1
rectangle "Search Indexing" as H2
rectangle "Webhook Calls" as H3
rectangle "Activity Logging" as H4
rectangle "Audit Trail" as H5

' Event types
package "Event Types" {
    rectangle "Model Events" as I1
    rectangle "Domain Events" as I2
    rectangle "System Events" as I3
    rectangle "Notification Events" as I4
}

' Event channels
package "Event Channels" {
    rectangle "Database" as J1
    rectangle "Redis" as J2
    rectangle "WebSockets" as J3
    rectangle "Queue" as J4
}

' Connections
A --> B
B --> C
C --> D

D --> E1
D --> E2

E1 --> F1
E2 --> F2

F1 --> G1
F1 --> G2

F2 --> H1
F2 --> H2
F2 --> H3
F2 --> H4
F2 --> H5

D --> I1
D --> I2
D --> I3
D --> I4

I1 --> J1
I2 --> J2
I3 --> J3
I4 --> J4

@enduml
