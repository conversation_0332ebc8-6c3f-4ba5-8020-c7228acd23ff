@startuml Event Sourcing Components (Light Mode)

' Light mode theme
!theme cerulean
skinparam backgroundColor #FFFFFF
skinparam ArrowColor #666666
skinparam shadowing false
skinparam DefaultFontColor #333333

' Define styles
skinparam rectangle {
    BackgroundColor #f5f5f5
    BorderColor #cccccc
    FontColor #333333
}

' Components
rectangle "Domain Event" as A
rectangle "Event Store" as B
rectangle "Event Stream" as C
rectangle "Projector" as D
rectangle "Read Model" as E
rectangle "Reactor" as F
rectangle "Side Effects" as G
rectangle "Aggregate" as H
rectangle "Command" as I

' Connections
A --> B
B --> C
C --> D
D --> E
A --> F
F --> G
H --> A : Applies
I --> H : Handled by

@enduml
