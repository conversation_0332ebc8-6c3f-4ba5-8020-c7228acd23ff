
@startuml Queue Integration Light (Light Mode)

' Dark mode theme
!theme cerulean
skinparam backgroundColor #ffffff
skinparam ArrowColor #666666
skinparam shadowing false
skinparam DefaultFontColor #333333

' Define styles
skinparam participant {
    BackgroundColor #f5f5f5
    BorderColor #cccccc
    FontColor #333333
}

skinparam actor {
    BackgroundColor #e6f7ff
    BorderColor #cccccc
    FontColor #333333
}


    participant A as Aggregate
    participant E as Event Store
    participant R as Reactor
    participant Q as Queue
    participant W as Queue Worker
    participant S as Side Effect
    
    A -> E: Store Event
    E -> R: Dispatch Event
    R -> Q: Push Job
    Q -> W: Process Job
    W -> S: Execute Side Effect
    alt Success
        S -> W: Return Success
        W -> Q: Mark Job as Completed
    else Failure
        S -> W: Return Failure
        W -> Q: Retry Job
    end
@enduml