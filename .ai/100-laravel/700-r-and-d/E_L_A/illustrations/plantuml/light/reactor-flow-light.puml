
@startuml Reactor Flow Light (Light Mode)

' Dark mode theme
!theme cerulean
skinparam backgroundColor #ffffff
skinparam ArrowColor #666666
skinparam shadowing false
skinparam DefaultFontColor #333333

' Define styles
skinparam rectangle {
    BackgroundColor #f5f5f5
    BorderColor #cccccc
    FontColor #333333
}

skinparam database {
    BackgroundColor #e6f7ff
    BorderColor #91d5ff
}


    rectangle "Event Store" as A --> rectangle "Reactor" as B
    B --> C{Event Type?}
    C --> rectangle : "UserRegistered" "S} Welcome Email" as D
    C --> rectangle : "TeamCreated" "S} Team Notification" as E
    C --> rectangle : "PostPublished" "S} Social Media Update" as F
    D --> rectangle "Email Service" as G
    E --> rectangle "Notification Service" as H
    F --> rectangle "Social Media API" as I
@enduml