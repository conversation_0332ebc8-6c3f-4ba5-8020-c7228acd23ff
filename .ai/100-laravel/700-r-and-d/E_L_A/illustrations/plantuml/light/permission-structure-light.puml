
@startuml Permission Structure Light (Light Mode)

' Dark mode theme
!theme cerulean
skinparam backgroundColor #ffffff
skinparam ArrowColor #666666
skinparam shadowing false
skinparam DefaultFontColor #333333

' Define styles for classes
skinparam class {
    BackgroundColor #f5f5f5
    BorderColor #cccccc
    FontColor #333333
}

skinparam interface {
    BackgroundColor #e6f7ff
    BorderColor #cccccc
    FontColor #333333
}


    class Permission {

        +string name
        +string guard_name
        +int team_id
    
}
    
    class Role {

        +string name
        +string guard_name
        +int team_id
        +givePermissionTo(Permission)
        +revokePermissionTo(Permission)
        +syncPermissions(Permission[])
    
}
    
    class User {

        +assignRole(Role)
        +removeRole(Role)
        +syncRoles(Role[])
        +hasRole(Role)
        +hasPermissionTo(Permission)
        +hasPermissionViaRole(Permission)
    
}
    
    class Team {

        +int id
        +string name
    
}
    
    Role "many" -- "many" Permission : has
    User "many" -- "many" Role : has
    User "many" -- "many" Permission : has
    Team "1" -- "many" Role : has
    Team "1" -- "many" Permission : has
@enduml