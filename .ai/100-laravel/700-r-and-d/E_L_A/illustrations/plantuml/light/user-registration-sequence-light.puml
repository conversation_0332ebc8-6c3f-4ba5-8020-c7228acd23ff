@startuml User Registration Sequence (Light Mode)

' Light mode theme
!theme cerulean
skinparam backgroundColor #FFFFFF
skinparam ArrowColor #666666
skinparam shadowing false
skinparam DefaultFontColor #333333

' Define styles
skinparam participant {
    BackgroundColor #f5f5f5
    BorderColor #cccccc
    FontColor #333333
}

' Participants
actor User as "User"
participant RegistrationForm as "Registration Form"
participant FortifyController as "Fortify Controller"
participant UserRepository as "User Repository"
participant EmailVerification as "Email Verification"
participant Database as "Database"

' Sequence
User -> RegistrationForm: Fill registration form
RegistrationForm -> FortifyController: Submit registration data
FortifyController -> FortifyController: Validate input
FortifyController -> UserRepository: Create user
UserRepository -> UserRepository: Generate snowflake ID
UserRepository -> UserRepository: Generate slug
UserRepository -> UserRepository: Hash password
UserRepository -> Database: Save user
Database --> UserRepository: Confirm save
UserRepository --> FortifyController: Return user
FortifyController -> EmailVerification: Send verification email
EmailVerification --> User: Email with verification link
FortifyController --> RegistrationForm: Registration successful
RegistrationForm --> User: Show success message

@enduml
