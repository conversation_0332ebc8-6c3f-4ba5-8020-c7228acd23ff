@startuml TAD Architecture (Light Mode)

' Light mode theme
!theme cerulean
skinparam backgroundColor #FFFFFF
skinparam ArrowColor #666666
skinparam shadowing false
skinparam DefaultFontColor #333333

' Define styles
skinparam rectangle {
    BackgroundColor #f5f5f5
    BorderColor #cccccc
    FontColor #333333
}

' Client Layer
package "Client Layer" {
    rectangle "Web Browser" as A1
    rectangle "Mobile App" as A2
    rectangle "API Consumers" as A3
}

' Presentation Layer
package "Presentation Layer" {
    rectangle "Livewire Components" as B1
    rectangle "Filament Admin Panel" as B2
    rectangle "API Controllers" as B3
}

' Application Layer
package "Application Layer" {
    rectangle "Command Handlers" as C1
    rectangle "Query Handlers" as C2
    rectangle "Event Listeners" as C3
    rectangle "Jobs & Queues" as C4
}

' Domain Layer
package "Domain Layer" {
    rectangle "Models" as D1
    rectangle "Services" as D2
    rectangle "Events" as D3
    rectangle "Policies" as D4
}

' Infrastructure Layer
package "Infrastructure Layer" {
    rectangle "Database" as E1
    rectangle "Search Engine" as E2
    rectangle "Queue System" as E3
    rectangle "WebSockets" as E4
    rectangle "File Storage" as E5
    rectangle "Cache" as E6
}

' Connections
A1 --> B1
A1 --> B2
A2 --> B3
A3 --> B3

B1 --> C1
B1 --> C2
B1 --> C3
B2 --> C1
B2 --> C2
B2 --> C3
B3 --> C1
B3 --> C2
B3 --> C3

C1 --> D1
C1 --> D2
C1 --> D4
C2 --> D1
C2 --> D2
C2 --> D3
C2 --> D4
C3 --> D1
C4 --> D1
C4 --> D2

D1 --> E1
D1 --> E2
D2 --> E1
D2 --> E3
D2 --> E4
D2 --> E5
D2 --> E6

@enduml
