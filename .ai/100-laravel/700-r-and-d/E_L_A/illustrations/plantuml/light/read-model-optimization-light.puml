
@startuml Read Model Optimization Light (Light Mode)

' Dark mode theme
!theme cerulean
skinparam backgroundColor #ffffff
skinparam ArrowColor #666666
skinparam shadowing false
skinparam DefaultFontColor #333333

' Define styles
skinparam rectangle {
    BackgroundColor #f5f5f5
    BorderColor #cccccc
    FontColor #333333
}

skinparam database {
    BackgroundColor #e6f7ff
    BorderColor #91d5ff
}


    rectangle "Event Store" as A --> rectangle "Projector" as B
    B --> rectangle "Base Read Model" as C
    C --> rectangle "Indexed Columns" as D
    C --> rectangle "Denormalized Data" as E
    C --> rectangle "Cached Results" as F
    C --> rectangle "Full-text Search" as G
    D --> rectangle "Fast Lookups" as H
    E --> rectangle "Reduced Joins" as I
    F --> rectangle "Reduced Database Load" as J
    G --> rectangle "Efficient Text Search" as K
@enduml