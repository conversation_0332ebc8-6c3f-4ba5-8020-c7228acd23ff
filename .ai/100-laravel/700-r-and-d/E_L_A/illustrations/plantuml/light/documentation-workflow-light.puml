@startuml Documentation Workflow (Light Mode)

' Light mode theme
!theme cerulean
skinparam backgroundColor #FFFFFF
skinparam ArrowColor #666666
skinparam shadowing false
skinparam DefaultFontColor #333333

' Define styles
skinparam activity {
    BackgroundColor #f5f5f5
    BorderColor #cccccc
    FontColor #333333
}

' Start
start

' Planning
:Planning Phase;
:Identify Documentation Needs;
:Create Documentation Plan;
:Define Document Structure;

' Creation
:Creation Phase;
:Draft Document Content;
:Create Initial Diagrams;
:Add Code Examples;
:Format According to Style Guide;

' Review
:Review Phase;
:Technical Review;
:Editorial Review;
:Stakeholder Review;
:Incorporate Feedback;

' Finalization
:Finalization Phase;
:Final Formatting Check;
:Ensure Diagram Consistency;
:Verify Links and References;
:Publish Documentation;

' Maintenance
:Maintenance Phase;
:Monitor for Accuracy;
:Update as Needed;
:Version Control;
:Archive Outdated Versions;

' End
stop

@enduml
