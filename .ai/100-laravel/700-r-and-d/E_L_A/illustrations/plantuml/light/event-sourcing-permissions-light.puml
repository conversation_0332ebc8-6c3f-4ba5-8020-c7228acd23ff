
@startuml Event Sourcing Permissions Light (Light Mode)

' Dark mode theme
!theme cerulean
skinparam backgroundColor #ffffff
skinparam ArrowColor #666666
skinparam shadowing false
skinparam DefaultFontColor #333333

' Define styles
skinparam participant {
    BackgroundColor #f5f5f5
    BorderColor #cccccc
    FontColor #333333
}

skinparam actor {
    BackgroundColor #e6f7ff
    BorderColor #cccccc
    FontColor #333333
}


    participant User
    participant Command
    participant CommandHandler
    participant PermissionChecker
    participant Aggregate
    participant EventStore
    
    User -> Command: Dispatch Command
    Command -> CommandHandler: Handle Command
    CommandHandler -> PermissionChecker: Check Permission
    alt Has Permission
        PermissionChecker -> CommandHandler: Permission Granted
        CommandHandler -> Aggregate: Execute Command
        Aggregate -> EventStore: Record Events
    else No Permission
        PermissionChecker -> CommandHandler: Permission Denied
        CommandHandler -> User: Unauthorized Exception
    end
@enduml