@startuml Class Diagram (Light Mode)

' Light mode theme
!theme cerulean
skinparam backgroundColor #FFFFFF
skinparam ArrowColor #666666
skinparam shadowing false
skinparam DefaultFontColor #333333

' Define styles
skinparam class {
    BackgroundColor #f5f5f5
    BorderColor #cccccc
    FontColor #333333
}

' Classes
class User {
    +bigint id
    +bigint snowflake_id
    +string slug
    +string type
    +string email
    +string password
    +timestamp email_verified_at
    +string status
    +timestamps()
    +userstamps()
    +softDeletes()
    +posts()
    +todos()
    +teams()
    +conversations()
    +messages()
    +comments()
}

class Team {
    +bigint id
    +bigint snowflake_id
    +string name
    +string slug
    +bigint parent_id
    +string path
    +int depth
    +string status
    +timestamps()
    +userstamps()
    +softDeletes()
    +parent()
    +children()
    +users()
    +categories()
    +todos()
}

class Post {
    +bigint id
    +bigint snowflake_id
    +string slug
    +string title
    +text content
    +string status
    +timestamp published_at
    +timestamps()
    +userstamps()
    +softDeletes()
    +user()
    +categories()
    +tags()
    +media()
    +comments()
}

class Todo {
    +bigint id
    +bigint snowflake_id
    +string title
    +text description
    +bigint parent_id
    +string path
    +int depth
    +string status
    +timestamp due_at
    +timestamp completed_at
    +timestamps()
    +userstamps()
    +softDeletes()
    +user()
    +team()
    +parent()
    +children()
    +categories()
    +tags()
    +media()
    +comments()
}

class Category {
    +bigint id
    +bigint snowflake_id
    +string name
    +string slug
    +bigint parent_id
    +string path
    +int depth
    +timestamps()
    +userstamps()
    +softDeletes()
    +parent()
    +children()
    +team()
    +posts()
    +todos()
}

' Relationships
User "1" -- "n" Post : authors
User "1" -- "n" Todo : assigned to
User "n" -- "n" Team : member of
Team "1" -- "n" Team : parent of
Team "1" -- "n" Category : has
Team "1" -- "n" Todo : related to
Category "1" -- "n" Category : parent of
Todo "1" -- "n" Todo : parent of

@enduml
