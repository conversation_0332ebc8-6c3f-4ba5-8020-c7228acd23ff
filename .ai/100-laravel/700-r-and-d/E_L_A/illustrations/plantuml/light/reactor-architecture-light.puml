
@startuml Reactor Architecture Light (Light Mode)

' Dark mode theme
!theme cerulean
skinparam backgroundColor #ffffff
skinparam ArrowColor #666666
skinparam shadowing false
skinparam DefaultFontColor #333333

' Define styles for classes
skinparam class {
    BackgroundColor #f5f5f5
    BorderColor #cccccc
    FontColor #333333
}

skinparam interface {
    BackgroundColor #e6f7ff
    BorderColor #cccccc
    FontColor #333333
}


    class EventStore {

        +StoredEvent[] events
        +persist(Event event)
        +retrieveAll()
        +retrieveAllForAggregate(string uuid)
    
}
    
    class Reactor {

        +onUserRegistered(UserRegistered event)
        +onTeamCreated(TeamCreated event)
        +onPostPublished(PostPublished event)
    
}
    
    class QueuedReactor {

        +queue: string
        +connection: string
        +delay: int
    
}
    
    class SideEffect {

        +execute()
        +rollback()
    
}
    
    class EmailNotification {

        +send()
    
}
    
    class PushNotification {

        +send()
    
}
    
    class ExternalAPICall {

        +execute()
    
}
    
    EventStore --> Reactor: events
    Reactor <|-- QueuedReactor
    Reactor --> SideEffect: triggers
    SideEffect <|-- EmailNotification
    SideEffect <|-- PushNotification
    SideEffect <|-- ExternalAPICall
@enduml