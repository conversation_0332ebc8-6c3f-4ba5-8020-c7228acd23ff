
@startuml Comment State Transitions Light (Light Mode)

' Dark mode theme
!theme cerulean
skinparam backgroundColor #ffffff
skinparam ArrowColor #666666
skinparam shadowing false
skinparam DefaultFontColor #333333

' Define styles
skinparam state {
    BackgroundColor #f5f5f5
    BorderColor #cccccc
    FontColor #333333
}

skinparam note {
    BackgroundColor #e6f7ff
    BorderColor #91d5ff
    FontColor #333333
}


    [*] --> Pending: Create Comment

    Pending --> Approved : Approve
    Pending --> Rejected : Reject
    Pending --> Deleted : Delete

    Approved --> Deleted : Delete
    Rejected --> Deleted : Delete

    Deleted --> [*]

    state Pending {
        [*] --> AwaitingModeration
        AwaitingModeration --> [*]
    }

    state Approved {
        [*] --> Visible
        Visible --> Edited : Update
        Edited --> Visible
    }

    state Rejected {
        [*] --> Hidden
        Hidden --> WithReason : Add Rejection Reason
        WithReason --> [*]
    }

    note right of Pending : Comments start in Pending state<br>and await moderation
    note right of Approved : Approved comments are visible<br>and can be edited
    note right of Rejected : Rejected comments are hidden<br>with optional rejection reason
    note right of Deleted : Deleted comments are<br>permanently removed

@enduml