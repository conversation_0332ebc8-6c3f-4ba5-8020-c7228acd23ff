
@startuml Projector Flow Light (Light Mode)

' Dark mode theme
!theme cerulean
skinparam backgroundColor #ffffff
skinparam ArrowColor #666666
skinparam shadowing false
skinparam DefaultFontColor #333333

' Define styles
skinparam rectangle {
    BackgroundColor #f5f5f5
    BorderColor #cccccc
    FontColor #333333
}

skinparam database {
    BackgroundColor #e6f7ff
    BorderColor #91d5ff
}


    rectangle "Event Store" as A --> rectangle "Projector" as B
    B --> C{Event Type?}
    C --> rectangle : "UserCreated" "Handle UserCreated" as D
    C --> rectangle : "UserUpdated" "Handle UserUpdated" as E
    C --> rectangle : "UserDeleted" "Handle UserDeleted" as F
    D --> rectangle "Update User Read Model" as G
    E --> G
    F --> G
@enduml