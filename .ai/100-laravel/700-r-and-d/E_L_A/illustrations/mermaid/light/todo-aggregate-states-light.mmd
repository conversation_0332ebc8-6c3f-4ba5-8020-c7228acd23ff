%%{init: {'theme': 'default', 'themeVariables': { 'primaryColor': '#f5f5f5', 'primaryTextColor': '#333333', 'primaryBorderColor': '#cccccc', 'lineColor': '#666666', 'secondaryColor': '#f0f0f0', 'tertiaryColor': '#ffffff' }}}%%
stateDiagram-v2
    [*] --> Pending: TodoCreatedEvent
    Pending --> InProgress: TodoStartedEvent
    InProgress --> Completed: TodoCompletedEvent
    Completed --> InProgress: TodoReopenedEvent
    Pending --> Cancelled: TodoCancelledEvent
    InProgress --> Cancelled: TodoCancelledEvent
    Pending --> Deleted: TodoDeletedEvent
    InProgress --> Deleted: TodoDeletedEvent
    Completed --> Deleted: TodoDeletedEvent
    Cancelled --> Deleted: TodoDeletedEvent
    
    %% State styling with classes
    classDef pendingState fill:#2980B9,stroke:#333,color:white
    classDef inProgressState fill:#F39C12,stroke:#333,color:white
    classDef completedState fill:#27AE60,stroke:#333,color:white
    classDef cancelledState fill:#7F8C8D,stroke:#333,color:white
    classDef deletedState fill:#C0392B,stroke:#333,color:white
    
    class Pending pendingState
    class InProgress inProgressState
    class Completed completedState
    class Cancelled cancelledState
    class Deleted deletedState
    
    %% Notes
    note right of Pending
        Todo is created but not yet started
        Can be assigned to users
        Can be modified freely
    end note
    
    note right of InProgress
        Todo is actively being worked on
        Progress can be tracked
        Due dates and priorities can be set
    end note
    
    note right of Completed
        Todo has been marked as completed
        Completion date is recorded
        Can be reopened if needed
    end note
    
    note right of Cancelled
        Todo has been cancelled
        Reason for cancellation is recorded
        Cannot be restarted, but can be referenced
    end note
    
    note right of Deleted
        Todo has been permanently deleted
        Cannot be recovered
        All associated data is soft-deleted
    end note