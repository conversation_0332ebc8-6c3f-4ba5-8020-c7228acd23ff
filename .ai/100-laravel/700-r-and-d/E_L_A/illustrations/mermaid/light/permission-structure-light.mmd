%%{init: {'theme': 'default', 'themeVariables': { 'primaryColor': '#f5f5f5', 'primaryTextColor': '#333333', 'primaryBorderColor': '#cccccc', 'lineColor': '#666666', 'secondaryColor': '#f0f0f0', 'tertiaryColor': '#ffffff' }}}%%
classDiagram
    class Permission {
        +string name
        +string guard_name
        +int team_id
    }
    
    class Role {
        +string name
        +string guard_name
        +int team_id
        +givePermissionTo(Permission)
        +revokePermissionTo(Permission)
        +syncPermissions(Permission[])
    }
    
    class User {
        +assignRole(Role)
        +removeRole(Role)
        +syncRoles(Role[])
        +hasRole(Role)
        +hasPermissionTo(Permission)
        +hasPermissionViaRole(Permission)
    }
    
    class Team {
        +int id
        +string name
    }
    
    Role "many" -- "many" Permission : has
    User "many" -- "many" Role : has
    User "many" -- "many" Permission : has
    Team "1" -- "many" Role : has
    Team "1" -- "many" Permission : has