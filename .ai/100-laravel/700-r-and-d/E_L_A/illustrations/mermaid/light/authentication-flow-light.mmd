%%{init: {'theme': 'default', 'themeVariables': { 'primaryColor': '#f5f5f5', 'primaryTextColor': '#333333', 'primaryBorderColor': '#cccccc', 'lineColor': '#666666', 'secondaryColor': '#f0f0f0', 'tertiaryColor': '#ffffff' }}}%%
sequenceDiagram
    participant User
    participant Browser
    participant Fortify
    participant <PERSON><PERSON>
    participant Database
    
    User->>Browser: Enter credentials
    Browser->>Fortify: POST /login
    Fortify->><PERSON><PERSON>: Attempt authentication
    Laravel->>Database: Verify credentials
    
    alt Valid credentials
        Database-->><PERSON><PERSON>: User found
        Lara<PERSON>-->>Fortify: Authentication successful
        
        alt MFA enabled
            Fortify->>Browser: Redirect to MFA challenge
            Browser->>User: Request MFA code
            User->>Browser: Enter MFA code
            Browser->>Fortify: POST /two-factor-challenge
            Fortify->><PERSON><PERSON>: Verify MFA code
            Laravel-->>Fortify: MFA verified
            Fortify-->>Browser: Create session
        else MFA not enabled
            Fortify-->>Browser: Create session
        end
        
        Browser-->>User: Redirect to dashboard
    else Invalid credentials
        Database-->><PERSON><PERSON>: User not found/invalid password
        Laravel-->>Fortify: Authentication failed
        Fortify-->>Browser: Return error
        Browser-->>User: Display error message
    end
    
    note over Browser,Laravel: Session contains user ID, CSRF token, and other security data
    
    alt Remember me selected
        Browser->>Browser: Store remember token
    end