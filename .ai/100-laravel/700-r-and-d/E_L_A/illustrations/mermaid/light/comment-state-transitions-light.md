# Comment State Transitions (Light Mode)

```mermaid
%%{init: {'theme': 'default', 'themeVariables': { 'primaryColor': '#f5f5f5', 'primaryTextColor': '#333333', 'primaryBorderColor': '#cccccc', 'lineColor': '#666666', 'secondaryColor': '#f0f0f0', 'tertiaryColor': '#ffffff' }}}%%
stateDiagram-v2
    [*] --> Pending: Create Comment

    Pending --> Approved: Approve
    Pending --> Rejected: Reject
    Pending --> Deleted: Delete

    Approved --> Deleted: Delete
    Rejected --> Deleted: Delete

    Deleted --> [*]

    state Pending {
        [*] --> AwaitingModeration
        AwaitingModeration --> [*]
    }

    state Approved {
        [*] --> Visible
        Visible --> Edited: Update
        Edited --> Visible
    }

    state Rejected {
        [*] --> Hidden
        Hidden --> WithReason: Add Rejection Reason
        WithReason --> [*]
    }

    note right of Pending: Comments start in Pending state<br>and await moderation
    note right of Approved: Approved comments are visible<br>and can be edited
    note right of Rejected: Rejected comments are hidden<br>with optional rejection reason
    note right of Deleted: Deleted comments are<br>permanently removed

```
