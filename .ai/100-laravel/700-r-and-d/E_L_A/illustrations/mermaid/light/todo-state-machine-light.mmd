%%{init: {'theme': 'default', 'themeVariables': { 'primaryColor': '#f5f5f5', 'primaryTextColor': '#333333', 'primaryBorderColor': '#cccccc', 'lineColor': '#666666', 'secondaryColor': '#f0f0f0', 'tertiaryColor': '#ffffff' }}}%%
stateDiagram-v2
    [*] --> Created: Create Todo
    Created --> InProgress: Start Work
    Created --> Cancelled: Cancel
    InProgress --> OnHold: Pause
    InProgress --> Completed: Complete
    InProgress --> Cancelled: Cancel
    OnHold --> InProgress: Resume
    OnHold --> Cancelled: Cancel
    Completed --> [*]
    Cancelled --> [*]

    state Created {
        [*] --> Draft
        Draft --> ReadyToStart: Finalize
        ReadyToStart --> [*]
    }

    state InProgress {
        [*] --> Started
        Started --> InReview: Submit for Review
        InReview --> Started: Request Changes
        InReview --> [*]: Approve
    }

    state OnHold {
        [*] --> Blocked
        [*] --> Deferred
        Blocked --> [*]: Unblock
        Deferred --> [*]: Prioritize
    }

    state Completed {
        [*] --> Done
        [*] --> PartiallyDone
        Done --> [*]
        PartiallyDone --> [*]
    }