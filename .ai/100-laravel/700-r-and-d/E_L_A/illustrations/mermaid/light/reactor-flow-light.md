# Reactor Flow (Light Mode)

```mermaid
%%{init: {'theme': 'default', 'themeVariables': { 'primaryColor': '#f5f5f5', 'primaryTextColor': '#333333', 'primaryBorderColor': '#cccccc', 'lineColor': '#666666', 'secondaryColor': '#f0f0f0', 'tertiaryColor': '#ffffff' }}}%%
flowchart LR
    A[Event Store] --> B[Reactor]
    B --> C{Event Type?}
    C -->|UserRegistered| D[Send Welcome Email]
    C -->|TeamCreated| E[Send Team Notification]
    C -->|PostPublished| F[Send Social Media Update]
    D --> G[Email Service]
    E --> H[Notification Service]
    F --> I[Social Media API]
```
