# Test Structure (Light Mode)

```mermaid
%%{init: {'theme': 'default', 'themeVariables': { 'primaryColor': '#f5f5f5', 'primaryTextColor': '#333333', 'primaryBorderColor': '#cccccc', 'lineColor': '#666666', 'secondaryColor': '#f0f0f0', 'tertiaryColor': '#ffffff' }}}%%
classDiagram
    class TestCase {
        +setUp()
        +tearDown()
    }
    
    class AggregateTest {
        +it_can_execute_command()
        +it_cannot_execute_invalid_command()
        +it_applies_event_correctly()
    }
    
    class ProjectorTest {
        +it_creates_read_model_from_event()
        +it_updates_read_model_from_event()
        +it_deletes_read_model_from_event()
    }
    
    class ReactorTest {
        +it_triggers_side_effect_from_event()
        +it_handles_external_service_failure()
    }
    
    class QueryTest {
        +it_returns_correct_data()
        +it_validates_input_correctly()
        +it_handles_empty_results()
    }
    
    TestCase <|-- AggregateTest
    TestCase <|-- ProjectorTest
    TestCase <|-- ReactorTest
    TestCase <|-- QueryTest
```
