# Migration Sequence (Dark Mode)

```mermaid
%%{init: {'theme': 'dark', 'themeVariables': { 'primaryColor': '#2c3e50', 'primaryTextColor': '#ecf0f1', 'primaryBorderColor': '#7f8c8d', 'lineColor': '#ecf0f1', 'secondaryColor': '#34495e', 'tertiaryColor': '#282c34' }}}%%
flowchart TD
    A["Start Migration Process"] --> B["Create Base Tables (No Foreign Keys)"]
    B --> C["Add Foreign Key Constraints"]
    C --> D["Add Indexes"]
    D --> E["Run Seeders"]
    E --> F["Migration Complete"]

    subgraph "Base Tables Order"
        B1["1. Users"] --> B2["2. Teams"] --> B3["3. Categories"] --> B4["4. Todos"]
        B4 --> B5["5. Posts"] --> B6["6. Conversations"] --> B7["7. Messages"]
        B7 --> B8["8. Roles & Permissions"] --> B9["9. Media"] --> B10["10. Tags"]
        B10 --> B11["11. Comments"] --> B12["12. Settings"] --> B13["13. Activity Logs"]
    end
```
