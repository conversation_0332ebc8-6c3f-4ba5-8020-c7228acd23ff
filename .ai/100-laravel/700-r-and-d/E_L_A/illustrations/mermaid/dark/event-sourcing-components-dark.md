# Event Sourcing Components (Dark Mode)

```mermaid
%%{init: {'theme': 'dark', 'themeVariables': { 'primaryColor': '#2c3e50', 'primaryTextColor': '#ecf0f1', 'primaryBorderColor': '#7f8c8d', 'lineColor': '#ecf0f1', 'secondaryColor': '#34495e', 'tertiaryColor': '#282c34' }}}%%
flowchart TD
    A[Domain Event] --> B[Event Store]
    B --> C[Event Stream]
    C --> D[Projector]
    D --> E[Read Model]
    A --> F[Reactor]
    F --> G[Side Effects]
    
    H[Aggregate] --> |Applies| A
    I[Command] --> |Handled by| H
```
