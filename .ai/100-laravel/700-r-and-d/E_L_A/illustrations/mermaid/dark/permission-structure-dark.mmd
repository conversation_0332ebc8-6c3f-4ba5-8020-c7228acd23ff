%%{init: {'theme': 'dark', 'themeVariables': { 'primaryColor': '#2a2a2a', 'primaryTextColor': '#ffffff', 'primaryBorderColor': '#555555', 'lineColor': '#999999', 'secondaryColor': '#252525', 'tertiaryColor': '#333333' }}}%%
classDiagram
    class Permission {
        +string name
        +string guard_name
        +int team_id
    }
    
    class Role {
        +string name
        +string guard_name
        +int team_id
        +givePermissionTo(Permission)
        +revokePermissionTo(Permission)
        +syncPermissions(Permission[])
    }
    
    class User {
        +assignRole(Role)
        +removeRole(Role)
        +syncRoles(Role[])
        +hasRole(Role)
        +hasPermissionTo(Permission)
        +hasPermissionViaRole(Permission)
    }
    
    class Team {
        +int id
        +string name
    }
    
    Role "many" -- "many" Permission : has
    User "many" -- "many" Role : has
    User "many" -- "many" Permission : has
    Team "1" -- "many" Role : has
    Team "1" -- "many" Permission : has