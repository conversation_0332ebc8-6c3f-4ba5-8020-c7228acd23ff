%%{init: {'theme': 'dark', 'themeVariables': { 'primaryColor': '#2c3e50', 'primaryTextColor': '#ecf0f1', 'primaryBorderColor': '#7f8c8d', 'lineColor': '#ecf0f1', 'secondaryColor': '#34495e', 'tertiaryColor': '#282c34' }}}%%
flowchart TB
    subgraph Client ["Client Layer"]
        Browser["Web Browser"]
        MobileApp["Mobile App"]
        Echo["Laravel Echo Client"]
    end

    subgraph WebSocket ["WebSocket Layer"]
        Reverb["Laravel Reverb"]
        Channels["Channels"]
        
        subgraph ChannelTypes ["Channel Types"]
            Private["Private Channels"]
            Presence["Presence Channels"]
            Public["Public Channels"]
        end
    end

    subgraph Application ["Application Layer"]
        subgraph EventSourcing ["Event Sourcing"]
            Aggregates["Aggregates"]
            Events["Domain Events"]
            Reactors["Reactors"]
        end
        
        subgraph Broadcasting ["Broadcasting"]
            BroadcastEvents["Broadcast Events"]
            Queue["Broadcast Queue"]
        end
        
        subgraph Models ["Models"]
            Comment["Comment Model"]
            Message["Message Model"]
            Todo["Todo Model"]
        end
    end

    subgraph Infrastructure ["Infrastructure Layer"]
        Redis["Redis PubSub"]
        EventStore["Event Store"]
        Database["Database"]
    end
    
    %% Client connections
    Browser --> Echo
    MobileApp --> Echo
    Echo --> Reverb
    
    %% WebSocket connections
    Reverb --> Channels
    Channels --> ChannelTypes
    
    %% Event flow
    Aggregates --> Events
    Events --> Reactors
    Events --> EventStore
    
    %% Reactor actions
    Reactors --> BroadcastEvents
    Reactors --> Models
    
    %% Broadcasting flow
    BroadcastEvents --> Queue
    Queue --> Redis
    
    %% WebSocket server connections
    Redis --> Reverb
    
    %% Model persistence
    Models --> Database
    
    %% Channel examples
    Private --> |"commentable.Post.{id}"| Comment
    Private --> |"todo.{id}"| Todo
    Presence --> |"conversation.{id}"| Message
    
    %% Add notes
    classDef note fill:#282c34,stroke:#7f8c8d,stroke-width:1px,color:#ecf0f1
    
    class Client,WebSocket,Application,Infrastructure,EventSourcing,Broadcasting,Models,ChannelTypes note
    
    %% Add styling
    classDef clientLayer fill:#1a5276,stroke:#3498db,stroke-width:2px
    classDef wsLayer fill:#196f3d,stroke:#27ae60,stroke-width:2px
    classDef appLayer fill:#7e5109,stroke:#f39c12,stroke-width:2px
    classDef infraLayer fill:#4a235a,stroke:#8e44ad,stroke-width:2px
    
    class Client clientLayer
    class WebSocket,Reverb,Channels,ChannelTypes,Private,Presence,Public wsLayer
    class Application,EventSourcing,Broadcasting,Models,Aggregates,Events,Reactors,BroadcastEvents,Queue,Comment,Message,Todo appLayer
    class Infrastructure,Redis,EventStore,Database infraLayer