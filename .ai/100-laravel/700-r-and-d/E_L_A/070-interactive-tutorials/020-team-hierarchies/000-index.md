<!-- filepath: /Users/<USER>/nc/PhpstormProjects/ela-docs/docs/E_L_A/070-interactive-tutorials/020-team-hierarchies/000-index.md -->
# Team Hierarchies Tutorials

**Version:** 1.0.0
**Date:** 2025-05-24
**Author:** AI Assistant
**Status:** Updated
**Progress:** 90%

---

<details>
<summary>Table of Contents</summary>

- [Team Hierarchies Tutorials](#team-hierarchies-tutorials)
  - [1. Introduction](#1-introduction)
  - [2. Available Tutorials](#2-available-tutorials)
  - [3. Related Documents](#3-related-documents)
  - [4. Version History](#4-version-history)

</details>

## 1. Introduction

This directory contains interactive tutorials focused on team hierarchies and permissions in the Enhanced Laravel Application (ELA). Teams are a cornerstone of collaboration in ELA, providing a structured way to organize users, scope data, and manage permissions.

Team hierarchies in ELA allow for parent-child relationships between teams, creating a tree-like structure that can represent organizational departments, project groups, or any logical collection of users working towards common goals. Understanding how to navigate and manage these hierarchies is essential for effective administration of an ELA instance.

## 2. Available Tutorials

| Tutorial | Description |
| --- | --- |
| [Navigating Team Hierarchies & Permissions](./010-navigating-team-hierarchies-permissions.md) | A comprehensive guide to understanding and managing team hierarchies and permissions in ELA. Learn how to create root teams and sub-teams, understand hierarchical structures, and navigate the permission model. |
| [Navigating Team Hierarchies & Permissions Answers](./020-navigating-team-hierarchies-permissions-answers.md) | Detailed answers to the exercises and assessment questions from the team hierarchies tutorial. |

## 3. Related Documents

- [../000-index.md](../000-index.md) - Index of all interactive tutorials
- [../../040-product-requirements/010-product-requirements.md](../../040-product-requirements/010-product-requirements.md) - Product Requirements Document (see Sections 4.3, 4.21)
- [../../100-implementation-plan/100-340-softdeletes-usertracking-implementation.md](../../100-implementation-plan/100-340-softdeletes-usertracking-implementation.md) - Implementation details for user tracking, which relates to team permissions

## 4. Version History

| Version | Date | Changes | Author |
| --- | --- | --- | --- |
| 1.0.0 | 2025-05-24 | Initial version of the index | AI Assistant |

---
