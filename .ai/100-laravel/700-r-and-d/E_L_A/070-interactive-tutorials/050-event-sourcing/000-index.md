<!-- filepath: /Users/<USER>/nc/PhpstormProjects/ela-docs/docs/E_L_A/070-interactive-tutorials/050-event-sourcing/000-index.md -->
# Event Sourcing Tutorials

**Version:** 0.1.0
**Date:** 2025-05-24
**Author:** AI Assistant
**Status:** Draft
**Progress:** 80%

---

<details>
<summary>Table of Contents</summary>

- [Event Sourcing Tutorials](#event-sourcing-tutorials)
  - [1. Introduction](#1-introduction)
  - [2. Available Tutorials](#2-available-tutorials)
  - [3. Related Documents](#3-related-documents)
  - [4. Version History](#4-version-history)

</details>

## 1. Introduction

This directory contains interactive tutorials focused on Event Sourcing concepts and implementation in the Enhanced Laravel Application (ELA). These tutorials are designed to help you understand the fundamentals of Event Sourcing and how it's applied in the ELA architecture.

Event Sourcing is a powerful pattern that stores all changes to an application state as a sequence of events. This approach provides numerous benefits including complete audit trails, temporal queries, and the ability to reconstruct the state of any entity at any point in time.

## 2. Available Tutorials

| Tutorial | Description |
| --- | --- |
| [Introduction to Event Sourcing in ELA](./010-introduction-event-sourcing.md) | A beginner-friendly introduction to Event Sourcing concepts, components, and benefits in the ELA context |
| [Introduction to Event Sourcing Answers](./020-introduction-event-sourcing-answers.md) | Answer key for the Introduction to Event Sourcing tutorial |

## 3. Related Documents

- [../000-index.md](../000-index.md) - Index of all interactive tutorials
- [../../../event-sourcing-summary.md](../../../event-sourcing-summary.md) - Summary of Event Sourcing concepts
- [../../../event-sourcing-guide.md](../../../event-sourcing-guide.md) - Detailed implementation guide for Event Sourcing

## 4. Version History

| Version | Date | Changes | Author |
| --- | --- | --- | --- |
| 0.1.0 | 2025-05-24 | Initial draft of the index | AI Assistant |

---
