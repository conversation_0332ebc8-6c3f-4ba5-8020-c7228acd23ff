<!-- filepath: /Users/<USER>/nc/PhpstormProjects/ela-docs/docs/E_L_A/070-interactive-tutorials/030-content-management/000-index.md -->
# Content Management Tutorials

**Version:** 1.0.0
**Date:** 2025-05-24
**Author:** AI Assistant
**Status:** Updated
**Progress:** 90%

---

<details>
<summary>Table of Contents</summary>

- [Content Management Tutorials](#content-management-tutorials)
  - [1. Introduction](#1-introduction)
  - [2. Available Tutorials](#2-available-tutorials)
  - [3. Related Documents](#3-related-documents)
  - [4. Version History](#4-version-history)

</details>

## 1. Introduction

This directory contains interactive tutorials focused on content management features in the Enhanced Laravel Application (ELA). Content management is a core functionality of ELA, allowing users to create, organize, and publish various types of content.

These tutorials will guide you through the process of creating and managing posts, working with categories, and understanding the different user roles involved in content management workflows.

## 2. Available Tutorials

| Tutorial | Description |
| --- | --- |
| [Mastering Content Management: Posts and Categories](./010-mastering-content-management.md) | Learn how to create and manage posts, organize content with categories, and understand the content workflow for different user roles. |
| [Mastering Content Management Answers](./020-mastering-content-management-answers.md) | Detailed answers to the exercises and assessment questions from the content management tutorial. |

## 3. Related Documents

- [../000-index.md](../000-index.md) - Index of all interactive tutorials
- [../../040-product-requirements/010-product-requirements.md](../../040-product-requirements/010-product-requirements.md) - Product Requirements Document (see Sections 3.2.1, 3.2.2, and 3.2.6)
- [../../100-implementation-plan/100-340-softdeletes-usertracking-implementation.md](../../100-implementation-plan/100-340-softdeletes-usertracking-implementation.md) - Implementation details for user tracking, which relates to content management

## 4. Version History

| Version | Date | Changes | Author |
| --- | --- | --- | --- |
| 1.0.0 | 2025-05-24 | Initial version of the index | AI Assistant |

---
