# 1. Error Handling Guide

**Version:** 1.0.0
**Date:** 2025-05-21
**Author:** Augment Agent
**Status:** Complete
**Progress:** 100%

---

<details>
<summary>Table of Contents</summary>

- [1.1. Overview](#11-overview)
- [1.2. <PERSON><PERSON> Error Handling Fundamentals](#12-laravel-error-handling-fundamentals)
- [1.3. Common Error Types and Solutions](#13-common-error-types-and-solutions)
- [1.4. Error Logging and Monitoring](#14-error-logging-and-monitoring)
- [1.5. Best Practices](#15-best-practices)
- [1.6. Troubleshooting Guide](#16-troubleshooting-guide)
- [1.7. Related Documents](#17-related-documents)
- [1.8. Version History](#18-version-history)

</details>

## 1.1. Overview

This guide provides comprehensive documentation on error handling strategies for the Enhanced Laravel Application (ELA). It covers <PERSON><PERSON>'s error handling mechanisms, common error types and their solutions, error logging and monitoring, and best practices for implementing robust error handling in your application.

## 1.2. <PERSON><PERSON> Error Handling Fundamentals

Laravel provides a robust error and exception handling system that helps developers manage errors effectively. This section covers the core components of <PERSON><PERSON>'s error handling system and how to use them in your Enhanced Laravel Application.

### 1.2.1. Exception Handler

The central component of Laravel's error handling system is the exception handler, located at `app/Exceptions/Handler.php`. This class is responsible for logging exceptions and rendering them to the user.

<div style="padding: 15px; border-radius: 5px; border: 1px solid #b0c4de; margin-bottom: 20px;">
<h4 style="margin-top: 0; ">Default Exception Handler Structure</h4>

```php
namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }
}
```
</div>

### 1.2.2. Exception Reporting

Laravel's exception handler provides methods for reporting exceptions, which can be customized to log exceptions to various services or perform additional actions when exceptions occur.

<div style="padding: 15px; border-radius: 5px; border: 1px solid #b0c4de; margin-bottom: 20px;">
<h4 style="margin-top: 0; ">Custom Exception Reporting</h4>

```php
/**
 * Register the exception handling callbacks for the application.
 */
public function register(): void
{
    $this->reportable(function (CustomException $e) {
        // Report the exception to an external service
        ExternalService::report($e);
    });

    // Prevent specific exceptions from being reported
    $this->reportable(function (ValidationException $e) {
        return false;
    });
}
```
</div>

### 1.2.3. Exception Rendering

The exception handler also determines how exceptions are rendered to the user. By default, Laravel renders exceptions differently based on whether the application is in debug mode and whether the request expects JSON.

<div style="padding: 15px; border-radius: 5px; border: 1px solid #b0c4de; margin-bottom: 20px;">
<h4 style="margin-top: 0; ">Custom Exception Rendering</h4>

```php
/**
 * Register the exception handling callbacks for the application.
 */
public function register(): void
{
    $this->renderable(function (CustomException $e, $request) {
        return response()->view('errors.custom', ['exception' => $e], 500);
    });

    // Render API exceptions differently
    $this->renderable(function (Exception $e, $request) {
        if ($request->is('api/*')) {
            return response()->json([
                'message' => 'An error occurred',
                'error' => $e->getMessage(),
            ], 500);
        }
    });
}
```
</div>

### 1.2.4. HTTP Exceptions

Laravel includes a set of HTTP exceptions that can be used to generate HTTP responses with specific status codes.

<div style="padding: 15px; border-radius: 5px; border: 1px solid #b0c4de; margin-bottom: 20px;">
<h4 style="margin-top: 0; ">Common HTTP Exceptions</h4>

```php
// 404 Not Found
throw new \Symfony\Component\HttpKernel\Exception\NotFoundHttpException('Resource not found');

// 403 Forbidden
throw new \Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException('Access denied');

// 401 Unauthorized
throw new \Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException('Bearer', 'Unauthorized');

// 400 Bad Request
throw new \Symfony\Component\HttpKernel\Exception\BadRequestHttpException('Invalid request');

// 500 Internal Server Error
throw new \Symfony\Component\HttpKernel\Exception\HttpException(500, 'Server error');
```
</div>

### 1.2.5. Custom Exceptions

Creating custom exceptions allows you to handle specific error scenarios in your application with dedicated exception classes.

<div style="padding: 15px; border-radius: 5px; border: 1px solid #b0c4de; margin-bottom: 20px;">
<h4 style="margin-top: 0; ">Creating Custom Exceptions</h4>

```php
namespace App\Exceptions;

use Exception;

class InsufficientFundsException extends Exception
{
    protected $account;

    public function __construct($account, $message = 'Insufficient funds', $code = 0, Exception $previous = null)
    {
        $this->account = $account;
        parent::__construct($message, $code, $previous);
    }

    public function getAccount()
    {
        return $this->account;
    }

    public function context()
    {
        return ['account_id' => $this->account->id];
    }
}
```

Usage:

```php
public function withdraw($amount)
{
    if ($this->balance < $amount) {
        throw new InsufficientFundsException($this);
    }

    $this->balance -= $amount;
    return $this->balance;
}
```
</div>

## 1.3. Common Error Types and Solutions

This section covers common error types encountered in Laravel applications and provides solutions for handling them effectively. Understanding these error types will help you implement robust error handling strategies in your Enhanced Laravel Application.

### 1.3.1. Validation Errors

Validation errors occur when user input fails to meet the expected format or requirements.

<div style="padding: 15px; border-radius: 5px; border: 1px solid #b0c4de; margin-bottom: 20px;">
<h4 style="margin-top: 0; ">Handling Validation Errors</h4>

```php
// Controller method with validation
public function store(Request $request)
{
    try {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users',
            'password' => 'required|min:8|confirmed',
        ]);

        // Process validated data
        User::create($validated);

        return redirect()->route('users.index')
            ->with('success', 'User created successfully.');
    } catch (ValidationException $e) {
        // Laravel automatically redirects back with errors
        // This catch block is for custom handling if needed
        return redirect()->back()
            ->withErrors($e->validator)
            ->withInput();
    }
}
```

**API Validation Handling:**

```php
// API controller method with validation
public function store(Request $request)
{
    try {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users',
            'password' => 'required|min:8',
        ]);

        $user = User::create($validated);

        return response()->json([
            'message' => 'User created successfully',
            'user' => $user
        ], 201);
    } catch (ValidationException $e) {
        return response()->json([
            'message' => 'Validation failed',
            'errors' => $e->errors(),
        ], 422);
    }
}
```
</div>

### 1.3.2. Database Errors

Database errors occur when there are issues with database connections, queries, or constraints.

<div style="padding: 15px; border-radius: 5px; border: 1px solid #b0c4de; margin-bottom: 20px;">
<h4 style="margin-top: 0; ">Handling Database Errors</h4>

```php
// Handling database connection errors
try {
    $users = DB::table('users')->get();
} catch (\Illuminate\Database\QueryException $e) {
    // Log the error
    Log::error('Database query error', [
        'message' => $e->getMessage(),
        'code' => $e->getCode(),
        'sql' => $e->getSql(),
        'bindings' => $e->getBindings(),
    ]);

    // Handle the error
    return response()->json([
        'message' => 'Database error occurred',
        'error' => config('app.debug') ? $e->getMessage() : 'An error occurred while processing your request',
    ], 500);
}
```

**Handling Unique Constraint Violations:**

```php
try {
    $user = User::create([
        'email' => $request->email,
        'name' => $request->name,
        // other fields
    ]);
} catch (\Illuminate\Database\QueryException $e) {
    // Check for unique constraint violation (MySQL error code 23000)
    if ($e->getCode() === '23000') {
        return response()->json([
            'message' => 'A user with this email already exists',
        ], 409); // Conflict status code
    }

    // Handle other database errors
    throw $e;
}
```
</div>

### 1.3.3. Authentication and Authorization Errors

These errors occur when users attempt to access resources they don't have permission to access.

<div style="padding: 15px; border-radius: 5px; border: 1px solid #b0c4de; margin-bottom: 20px;">
<h4 style="margin-top: 0; ">Handling Authentication Errors</h4>

```php
// Authentication exception handling in exception handler
public function register(): void
{
    $this->renderable(function (\Illuminate\Auth\AuthenticationException $e, $request) {
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Unauthenticated',
            ], 401);
        }

        return redirect()->guest(route('login'));
    });
}
```

**Handling Authorization Errors:**

```php
// Authorization exception handling in exception handler
public function register(): void
{
    $this->renderable(function (\Illuminate\Auth\Access\AuthorizationException $e, $request) {
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Forbidden: ' . $e->getMessage(),
            ], 403);
        }

        return response()->view('errors.403', [
            'exception' => $e,
        ], 403);
    });
}
```

**Using Gates and Policies:**

```php
public function update(Request $request, Post $post)
{
    try {
        $this->authorize('update', $post);

        // Update post logic

        return response()->json([
            'message' => 'Post updated successfully',
            'post' => $post,
        ]);
    } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
        return response()->json([
            'message' => 'You are not authorized to update this post',
        ], 403);
    }
}
```
</div>

### 1.3.4. File System Errors

File system errors occur when there are issues with file operations such as reading, writing, or deleting files.

<div style="padding: 15px; border-radius: 5px; border: 1px solid #b0c4de; margin-bottom: 20px;">
<h4 style="margin-top: 0; ">Handling File System Errors</h4>

```php
// Handling file upload errors
try {
    $path = $request->file('avatar')->store('avatars');

    $user->update([
        'avatar' => $path,
    ]);

    return response()->json([
        'message' => 'Avatar uploaded successfully',
        'path' => $path,
    ]);
} catch (\Illuminate\Contracts\Filesystem\FileNotFoundException $e) {
    return response()->json([
        'message' => 'File not found',
    ], 404);
} catch (\Exception $e) {
    Log::error('File upload error', [
        'message' => $e->getMessage(),
        'user_id' => $user->id,
    ]);

    return response()->json([
        'message' => 'Failed to upload avatar',
    ], 500);
}
```

**Handling File Read/Write Errors:**

```php
try {
    $content = Storage::get('file.txt');
} catch (\Illuminate\Contracts\Filesystem\FileNotFoundException $e) {
    Log::error('File not found', [
        'path' => 'file.txt',
        'error' => $e->getMessage(),
    ]);

    return response()->json([
        'message' => 'The requested file could not be found',
    ], 404);
}
```
</div>

### 1.3.5. External Service Errors

External service errors occur when there are issues with third-party APIs or services that your application depends on.

<div style="padding: 15px; border-radius: 5px; border: 1px solid #b0c4de; margin-bottom: 20px;">
<h4 style="margin-top: 0; ">Handling External Service Errors</h4>

```php
// Handling HTTP client errors
try {
    $response = Http::timeout(5)->get('https://api.example.com/users');

    if ($response->successful()) {
        return response()->json([
            'data' => $response->json(),
        ]);
    }

    // Handle API error responses
    return response()->json([
        'message' => 'External API error',
        'status' => $response->status(),
        'error' => $response->body(),
    ], 500);
} catch (\Illuminate\Http\Client\ConnectionException $e) {
    Log::error('API connection error', [
        'url' => 'https://api.example.com/users',
        'error' => $e->getMessage(),
    ]);

    return response()->json([
        'message' => 'Failed to connect to external service',
    ], 503); // Service Unavailable
} catch (\Illuminate\Http\Client\RequestException $e) {
    Log::error('API request error', [
        'url' => 'https://api.example.com/users',
        'error' => $e->getMessage(),
    ]);

    return response()->json([
        'message' => 'Error in external service request',
    ], 500);
}
```

**Using Retry Logic:**

```php
// Implementing retry logic for external service calls
try {
    $response = Http::retry(3, 100)->get('https://api.example.com/users');

    return response()->json([
        'data' => $response->json(),
    ]);
} catch (\Exception $e) {
    Log::error('API error after retries', [
        'url' => 'https://api.example.com/users',
        'error' => $e->getMessage(),
    ]);

    return response()->json([
        'message' => 'External service unavailable',
    ], 503);
}
```
</div>

### 1.3.6. Model Not Found Errors

Model not found errors occur when attempting to retrieve a model that doesn't exist.

<div style="padding: 15px; border-radius: 5px; border: 1px solid #b0c4de; margin-bottom: 20px;">
<h4 style="margin-top: 0; ">Handling Model Not Found Errors</h4>

```php
// Handling ModelNotFoundException in controller
public function show($id)
{
    try {
        $user = User::findOrFail($id);

        return response()->json([
            'user' => $user,
        ]);
    } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
        return response()->json([
            'message' => 'User not found',
        ], 404);
    }
}
```

**Global Handling in Exception Handler:**

```php
// Handling ModelNotFoundException in exception handler
public function register(): void
{
    $this->renderable(function (\Illuminate\Database\Eloquent\ModelNotFoundException $e, $request) {
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Resource not found',
            ], 404);
        }

        return response()->view('errors.404', [], 404);
    });
}
```
</div>

## 1.4. Error Logging and Monitoring

Effective error logging and monitoring are essential for identifying, diagnosing, and resolving issues in your Laravel application. This section covers strategies for logging errors and monitoring your application's health.

### 1.4.1. Laravel Logging Configuration

Laravel provides a powerful logging system that allows you to log messages to various outputs, including files, the system log, and external services.

<div style="padding: 15px; border-radius: 5px; border: 1px solid #b0c4de; margin-bottom: 20px;">
<h4 style="margin-top: 0; ">Logging Configuration</h4>

The logging configuration is stored in `config/logging.php`. Laravel supports multiple logging channels, each with its own configuration:

```php
// config/logging.php
return [
    'default' => env('LOG_CHANNEL', 'stack'),

    'channels' => [
        'stack' => [
            'driver' => 'stack',
            'channels' => ['single', 'slack'],
            'ignore_exceptions' => false,
        ],

        'single' => [
            'driver' => 'single',
            'path' => storage_path('logs/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
        ],

        'daily' => [
            'driver' => 'daily',
            'path' => storage_path('logs/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'days' => 14,
        ],

        'slack' => [
            'driver' => 'slack',
            'url' => env('LOG_SLACK_WEBHOOK_URL'),
            'username' => 'Laravel Log',
            'emoji' => ':boom:',
            'level' => env('LOG_LEVEL', 'critical'),
        ],

        // Other channels...
    ],
];
```
</div>

### 1.4.2. Logging Errors

Laravel provides several methods for logging errors at different severity levels.

<div style="padding: 15px; border-radius: 5px; border: 1px solid #b0c4de; margin-bottom: 20px;">
<h4 style="margin-top: 0; ">Logging Methods</h4>

```php
// Basic logging
Log::emergency($message, $context = []);
Log::alert($message, $context = []);
Log::critical($message, $context = []);
Log::error($message, $context = []);
Log::warning($message, $context = []);
Log::notice($message, $context = []);
Log::info($message, $context = []);
Log::debug($message, $context = []);

// Example: Logging an error with context
Log::error('User payment failed', [
    'user_id' => $user->id,
    'amount' => $amount,
    'error' => $exception->getMessage(),
]);

// Logging to a specific channel
Log::channel('slack')->critical('Application is down!', [
    'exception' => $exception,
]);

// Logging to multiple channels
Log::stack(['single', 'slack'])->error('Something went wrong', [
    'user_id' => $user->id,
]);
```
</div>

### 1.4.3. Contextual Information

Adding contextual information to your logs makes them more useful for debugging.

<div style="padding: 15px; border-radius: 5px; border: 1px solid #b0c4de; margin-bottom: 20px;">
<h4 style="margin-top: 0; ">Adding Context to Logs</h4>

```php
// Adding context to all logs
Log::withContext([
    'request_id' => Str::uuid(),
    'user_id' => Auth::id(),
]);

// Adding context in middleware
class AddLogContext
{
    public function handle($request, Closure $next)
    {
        Log::withContext([
            'request_id' => Str::uuid(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'user_id' => Auth::id(),
        ]);

        return $next($request);
    }
}
```

**Logging Exception Context:**

```php
try {
    // Code that might throw an exception
} catch (Exception $e) {
    Log::error('An error occurred', [
        'exception' => $e,
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString(),
        // Additional context
        'user_id' => Auth::id(),
        'request_data' => $request->all(),
    ]);
}
```
</div>

### 1.4.4. Error Monitoring

Monitoring your application's errors helps you identify issues before they affect users.

<div style="padding: 15px; border-radius: 5px; border: 1px solid #b0c4de; margin-bottom: 20px;">
<h4 style="margin-top: 0; ">Error Monitoring Strategies</h4>

**Laravel Telescope:**

Laravel Telescope is a debugging assistant for Laravel applications that provides insights into requests, exceptions, logs, database queries, and more.

```php
// Install Laravel Telescope
// composer require laravel/telescope

// Register Telescope in your AppServiceProvider
public function register()
{
    if ($this->app->environment('local')) {
        $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
        $this->app->register(TelescopeServiceProvider::class);
    }
}
```

**Laravel Horizon:**

Laravel Horizon provides a beautiful dashboard and code-driven configuration for your Laravel Redis queues.

```php
// Install Laravel Horizon
// composer require laravel/horizon

// Configure Horizon in config/horizon.php
'environments' => [
    'production' => [
        'supervisor-1' => [
            'connection' => 'redis',
            'queue' => ['default'],
            'balance' => 'simple',
            'processes' => 10,
            'tries' => 3,
        ],
    ],
],
```
</div>

## 1.5. Best Practices

Implementing effective error handling requires following best practices that ensure errors are properly caught, logged, and handled. This section outlines key best practices for error handling in Laravel applications.

### 1.5.1. Use Try-Catch Blocks Strategically

Use try-catch blocks to handle exceptions in critical parts of your application.

<div style="padding: 15px; border-radius: 5px; border: 1px solid #b0c4de; margin-bottom: 20px;">
<h4 style="margin-top: 0; ">Strategic Exception Handling</h4>

```php
// Good: Specific exception handling
try {
    // Code that might throw an exception
    $user = User::findOrFail($id);
    $user->update($request->validated());
} catch (ModelNotFoundException $e) {
    // Handle the specific exception
    Log::error('User not found', ['id' => $id]);
    return redirect()->route('users.index')
        ->with('error', 'User not found.');
} catch (QueryException $e) {
    // Handle database exceptions
    Log::error('Database error', [
        'message' => $e->getMessage(),
        'code' => $e->getCode(),
    ]);
    return redirect()->back()
        ->with('error', 'A database error occurred.');
} catch (Exception $e) {
    // Catch-all for other exceptions
    Log::error('Unexpected error', [
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
    ]);
    return redirect()->back()
        ->with('error', 'An unexpected error occurred.');
}
```
</div>

### 1.5.2. Create Custom Exceptions

Create custom exceptions for specific error scenarios in your application.

<div style="padding: 15px; border-radius: 5px; border: 1px solid #b0c4de; margin-bottom: 20px;">
<h4 style="margin-top: 0; ">Custom Exception Classes</h4>

```php
// Create a custom exception for insufficient funds
class InsufficientFundsException extends Exception
{
    protected $account;
    protected $amount;

    public function __construct($account, $amount, $message = null, $code = 0, Exception $previous = null)
    {
        $this->account = $account;
        $this->amount = $amount;
        $message = $message ?: "Insufficient funds: Attempted to withdraw {$amount} from account {$account->id} with balance {$account->balance}";

        parent::__construct($message, $code, $previous);
    }

    public function getAccount()
    {
        return $this->account;
    }

    public function getAmount()
    {
        return $this->amount;
    }

    public function context()
    {
        return [
            'account_id' => $this->account->id,
            'balance' => $this->account->balance,
            'amount' => $this->amount,
        ];
    }
}
```
</div>

### 1.5.3. Centralize Exception Handling

Use Laravel's exception handler to centralize exception handling logic.

<div style="padding: 15px; border-radius: 5px; border: 1px solid #b0c4de; margin-bottom: 20px;">
<h4 style="margin-top: 0; ">Centralized Exception Handling</h4>

```php
// app/Exceptions/Handler.php
public function register(): void
{
    // Handle ModelNotFoundException
    $this->renderable(function (ModelNotFoundException $e, $request) {
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Resource not found',
            ], 404);
        }

        return redirect()->back()->with('error', 'Resource not found.');
    });

    // Handle ValidationException
    $this->renderable(function (ValidationException $e, $request) {
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);
        }
    });

    // Handle AuthenticationException
    $this->renderable(function (AuthenticationException $e, $request) {
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Unauthenticated',
            ], 401);
        }

        return redirect()->guest(route('login'));
    });
}
```
</div>

## 1.6. Troubleshooting Guide

This section provides solutions for common error scenarios you might encounter in your Laravel application. Use this guide to quickly identify and resolve issues.

### 1.6.1. Common Laravel Errors

<div style="padding: 15px; border-radius: 5px; border: 1px solid #b0c4de; margin-bottom: 20px;">
<h4 style="margin-top: 0; ">Class Not Found Errors</h4>

**Problem:** `Class 'App\Models\User' not found` or similar class not found errors.

**Potential Causes:**
- Incorrect namespace
- Missing class import
- Composer autoloader issues
- Class file in wrong location

**Solutions:**

1. **Verify Namespace:**
   ```php
   // Check that the namespace matches the directory structure
   namespace App\Models;

   class User extends Authenticatable
   {
       // ...
   }
   ```

2. **Import the Class:**
   ```php
   // Add the import at the top of the file
   use App\Models\User;
   ```

3. **Regenerate Composer Autoloader:**
   ```bash
   composer dump-autoload
   ```
</div>

<div style="padding: 15px; border-radius: 5px; border: 1px solid #b0c4de; margin-bottom: 20px;">
<h4 style="margin-top: 0; ">Database Connection Errors</h4>

**Problem:** `SQLSTATE[HY000] [2002] Connection refused` or similar database connection errors.

**Potential Causes:**
- Incorrect database credentials
- Database server not running
- Firewall blocking connection
- Wrong database host or port

**Solutions:**

1. **Verify Database Credentials:**
   Check your `.env` file for correct database credentials:
   ```
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=laravel
   DB_USERNAME=root
   DB_PASSWORD=
   ```

2. **Check Database Server:**
   Ensure your database server is running:
   ```bash
   # For MySQL
   sudo service mysql status

   # For PostgreSQL
   sudo service postgresql status
   ```
</div>

## 1.7. Related Documents

- [../000-index.md](../000-index.md) - Main documentation index
- [./000-index.md](000-index.md) - Technical guides index
- [../100-implementation-plan/100-000-implementation-plan-overview.md](../100-implementation-plan/100-000-implementation-plan-overview.md) - Implementation plan overview
- [../100-implementation-plan/100-710-troubleshooting-guide.md](../100-implementation-plan/100-710-troubleshooting-guide.md) - General troubleshooting guide

## 1.8. Version History

| Version | Date | Changes | Author |
|---------|------|---------|--------|
| 1.0.0 | 2025-05-21 | Initial version | Augment Agent |
