<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\CategoryType;
use App\Enums\SecondaryKeyType;
use App\Services\HierarchyStrategyService;
use App\Traits\HasSecondaryUniqueKey;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Permission\Traits\HasPermissions;
use Spatie\Permission\Traits\HasRoles;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Spatie\Tags\HasTags;
use Staudenmeir\LaravelAdjacencyList\Eloquent\HasRecursiveRelationships;
use Wildside\Userstamps\Userstamps;

class Category extends Model
{
    use HasFactory;
    use HasPermissions;
    use HasRecursiveRelationships;
    use HasRoles;
    use HasSecondaryUniqueKey;
    use HasSlug;
    use HasTags;
    use SoftDeletes;
    use Userstamps;

    // Adjacency list support

    /**
     * The table associated with the model.
     */
    protected $table = 'categories';

    /**
     * Adjacency list configuration for HasRecursiveRelationships trait
     */
    protected $parentKey = 'parent_id';
    protected $childrenKey = 'parent_id';
    protected $depthKey = 'depth';
    protected $pathKey = 'path';
    protected $pathSeparator = '/';

    /**
     * Get the secondary key type for this model.
     * Using UUID for categories - standards compliance for reference data.
     */
    public function getSecondaryKeyType(): SecondaryKeyType
    {
        return SecondaryKeyType::UUID;
    }

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'description',
        'type',
        'parent_id',        // Adjacency list parent
        'sort_order',
        'is_active',
        'color',
        'icon',
        'metadata',
        'public_id',
        'slug',
    ];

    /**
     * Get the attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'type' => CategoryType::class,
            'is_active' => 'boolean',
            'metadata' => 'array',
            'sort_order' => 'integer',
            'depth' => 'integer',      // Adjacency list depth
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('public_id')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate()
            ->preventOverwrite()
            ->startSlugSuffixFrom(2);
    }

    /**
     * Get the route key name for model binding.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    // ===== HYBRID HIERARCHY METHODS =====

    /**
     * Intelligent strategy selection for hierarchy operations
     */
    protected function getOptimalStrategy(string $operation, array $context = []): string
    {
        return app(HierarchyStrategyService::class)->selectStrategy($operation, $context);
    }

    /**
     * Get ancestors using optimal strategy
     */
    public function getAncestors(): Collection
    {
        $strategy = $this->getOptimalStrategy('get_ancestors');

        return match ($strategy) {
            'adjacency' => $this->ancestors()->get(),
            'closure' => $this->ancestorsViaClosure(),
            'hybrid' => $this->depth <= 3 ? $this->ancestors()->get() : $this->ancestorsViaClosure(),
        };
    }

    /**
     * Get descendants using optimal strategy
     */
    public function getDescendants(): Collection
    {
        $strategy = $this->getOptimalStrategy('get_descendants');

        return match ($strategy) {
            'adjacency' => $this->descendants()->get(),
            'closure' => $this->descendantsViaClosure(),
            'hybrid' => $this->descendants()->count() > 100 ? $this->descendantsViaClosure() : $this->descendants()->get(),
        };
    }

    /**
     * Get children using adjacency list (always fast for direct children)
     */
    public function getChildren(): Collection
    {
        return $this->children()->orderBy('sort_order')->get();
    }

    // ===== CLOSURE TABLE RELATIONSHIPS =====

    /**
     * Get ancestors through closure table (for complex queries)
     */
    public function ancestorsViaClosure(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'category_closure', 'descendant_id', 'ancestor_id')
            ->withPivot('depth')
            ->orderBy('category_closure.depth');
    }

    /**
     * Get descendants through closure table (for complex queries)
     */
    public function descendantsViaClosure(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'category_closure', 'ancestor_id', 'descendant_id')
            ->withPivot('depth')
            ->orderBy('category_closure.depth');
    }

    /**
     * Get direct parent via closure table
     */
    public function parentViaClosure(): BelongsToMany
    {
        return $this->ancestorsViaClosure()->wherePivot('depth', 1);
    }

    /**
     * Get direct children via closure table
     */
    public function childrenViaClosure(): BelongsToMany
    {
        return $this->descendantsViaClosure()->wherePivot('depth', 1);
    }

    /**
     * Get all descendants through closure table.
     */
    public function descendants(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'category_closure', 'ancestor_id', 'descendant_id')
            ->wherePivot('depth', '>', 0)
            ->orderBy('category_closure.depth')
            ->orderBy('sort_order');
    }

    /**
     * Get all ancestors through closure table.
     */
    public function ancestors(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'category_closure', 'descendant_id', 'ancestor_id')
            ->wherePivot('depth', '>', 0)
            ->orderBy('category_closure.depth', 'desc');
    }

    /**
     * Get siblings (categories with the same parent).
     */
    public function siblings(): Collection
    {
        $parent = $this->parent();
        if (!$parent) {
            return Category::whereDoesntHave('parents')->where('id', '!=', $this->id)->get();
        }

        return $parent->children()->where('id', '!=', $this->id)->get();
    }

    /**
     * Get the root category of this tree.
     */
    public function root(): ?Category
    {
        $ancestors = $this->ancestors()->get();
        return $ancestors->isEmpty() ? $this : $ancestors->last();
    }

    /**
     * Get leaf categories (no children).
     */
    public function scopeLeaves(Builder $query): Builder
    {
        return $query->whereDoesntHave('children');
    }

    /**
     * Get root categories (no parents).
     */
    public function scopeRoots(Builder $query): Builder
    {
        return $query->whereDoesntHave('parents');
    }

    /**
     * Scope by category type.
     */
    public function scopeOfType(Builder $query, CategoryType $type): Builder
    {
        return $query->where('type', $type);
    }

    /**
     * Scope by active status.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope by depth level in the hierarchy.
     */
    public function scopeAtDepth(Builder $query, int $depth): Builder
    {
        return $query->whereHas('ancestors', function ($q) use ($depth) {
            $q->havingRaw('COUNT(*) = ?', [$depth]);
        });
    }

    /**
     * Polymorphic relationship to artists.
     */
    public function artists(): MorphToMany
    {
        return $this->morphedByMany(Artist::class, 'categorizable');
    }

    /**
     * Polymorphic relationship to albums.
     */
    public function albums(): MorphToMany
    {
        return $this->morphedByMany(Album::class, 'categorizable');
    }

    /**
     * Polymorphic relationship to tracks.
     */
    public function tracks(): MorphToMany
    {
        return $this->morphedByMany(Track::class, 'categorizable');
    }

    /**
     * Polymorphic relationship to playlists.
     */
    public function playlists(): MorphToMany
    {
        return $this->morphedByMany(Playlist::class, 'categorizable');
    }

    /**
     * Polymorphic relationship to customers.
     */
    public function customers(): MorphToMany
    {
        return $this->morphedByMany(Customer::class, 'categorizable');
    }

    /**
     * Make this category a child of another category using closure table.
     */
    public function makeChildOf(Category $parent): bool
    {
        if ($this->wouldCreateCircularReference($parent)) {
            return false;
        }

        // Remove existing parent relationships
        $this->removeFromHierarchy();

        // Add direct parent relationship
        $this->parents()->attach($parent->id, ['depth' => 1]);

        // Add all ancestor relationships
        $parentAncestors = $parent->ancestors()->get();
        foreach ($parentAncestors as $ancestor) {
            $depth = $ancestor->pivot->depth + 1;
            $this->parents()->attach($ancestor->id, ['depth' => $depth]);
        }

        // Update all descendants to include new ancestors
        $this->updateDescendantAncestors();

        return true;
    }

    /**
     * Make this category a root category.
     */
    public function makeRoot(): bool
    {
        $this->removeFromHierarchy();
        return true;
    }

    /**
     * Remove this category from the hierarchy (closure table).
     */
    protected function removeFromHierarchy(): void
    {
        // Remove all ancestor relationships for this category
        $this->parents()->detach();

        // Remove all descendant relationships where this is an ancestor
        $this->descendants()->detach();
    }

    /**
     * Update descendant ancestors after hierarchy change.
     */
    protected function updateDescendantAncestors(): void
    {
        $descendants = $this->descendants()->get();
        $ancestors = $this->ancestors()->get();

        foreach ($descendants as $descendant) {
            foreach ($ancestors as $ancestor) {
                $newDepth = $ancestor->pivot->depth + $descendant->pivot->depth;
                $descendant->parents()->syncWithoutDetaching([
                    $ancestor->id => ['depth' => $newDepth],
                ]);
            }
        }
    }

    /**
     * Check if making this category a child would create a circular reference.
     */
    protected function wouldCreateCircularReference(Category $potentialParent): bool
    {
        if ($potentialParent->id === $this->id) {
            return true;
        }

        return $this->descendants()->pluck('id')->contains($potentialParent->id);
    }

    /**
     * Get the full hierarchical name.
     */
    public function getFullNameAttribute(): string
    {
        $ancestors = $this->ancestors()->get();
        $names = $ancestors->pluck('name')->push($this->name);

        return $names->implode(' > ');
    }

    /**
     * Get the category type label.
     */
    public function getTypeLabelAttribute(): string
    {
        return $this->type->label();
    }

    /**
     * Get the category type color.
     */
    public function getTypeColorAttribute(): string
    {
        return $this->type->color();
    }

    /**
     * Get the category type icon.
     */
    public function getTypeIconAttribute(): string
    {
        return $this->type->icon();
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::created(function ($category) {
            // Add self-reference with depth 0 for closure table
            $category->parents()->attach($category->id, ['depth' => 0]);
        });

        static::deleting(function ($category) {
            // Clean up closure table relationships
            $category->removeFromHierarchy();
        });
    }
}
