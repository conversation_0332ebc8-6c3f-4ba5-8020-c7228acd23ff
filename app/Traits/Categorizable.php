<?php

declare(strict_types=1);

namespace App\Traits;

use App\Enums\CategoryType;
use App\Models\Category;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

/**
 * Categorizable Trait
 *
 * Provides polymorphic category functionality for Eloquent models.
 * Supports all CategoryType enums and integrates with the closure table Category model.
 */
trait Categorizable
{
    /**
     * Get the polymorphic many-to-many relationship to categories.
     */
    public function categories(): MorphToMany
    {
        return $this->morphToMany(Category::class, 'categorizable')
            ->withTimestamps()
            ->withPivot(['metadata', 'sort_order', 'is_primary'])
            ->orderBy('pivot_sort_order')
            ->orderBy('categories.sort_order');
    }

    /**
     * Get categories filtered by specific type.
     */
    public function categoriesByType(CategoryType $type): MorphToMany
    {
        return $this->categories()->where('categories.type', $type);
    }

    /**
     * Attach a single category to the model.
     */
    public function attachCategory(Category $category, array $attributes = []): void
    {
        $defaultAttributes = [
            'sort_order' => 0,
            'is_primary' => false,
            'metadata' => null,
        ];

        $this->categories()->attach($category->id, array_merge($defaultAttributes, $attributes));
    }

    /**
     * Attach multiple categories to the model.
     */
    public function attachCategories(array $categories, array $attributes = []): void
    {
        $categoryData = [];
        $defaultAttributes = [
            'sort_order' => 0,
            'is_primary' => false,
            'metadata' => null,
        ];

        foreach ($categories as $index => $category) {
            $categoryId = $category instanceof Category ? $category->id : $category;
            $categoryData[$categoryId] = array_merge($defaultAttributes, $attributes, [
                'sort_order' => $attributes['sort_order'] ?? $index,
            ]);
        }

        $this->categories()->attach($categoryData);
    }

    /**
     * Sync categories of a specific type only (preserves other types).
     */
    public function syncCategoriesByType(CategoryType $type, array $categories, array $attributes = []): array
    {
        // Get current categories of other types to preserve them
        $otherTypeCategories = $this->categories()
            ->where('categories.type', '!=', $type)
            ->get()
            ->mapWithKeys(function ($category) {
                return [$category->id => [
                    'sort_order' => $category->pivot->sort_order,
                    'is_primary' => $category->pivot->is_primary,
                    'metadata' => $category->pivot->metadata,
                ]];
            })
            ->toArray();

        // Prepare new categories of the specified type
        $newTypeCategories = [];
        $defaultAttributes = [
            'sort_order' => 0,
            'is_primary' => false,
            'metadata' => null,
        ];

        foreach ($categories as $index => $category) {
            $categoryId = $category instanceof Category ? $category->id : $category;
            $newTypeCategories[$categoryId] = array_merge($defaultAttributes, $attributes, [
                'sort_order' => $attributes['sort_order'] ?? $index,
            ]);
        }

        // Merge and sync all categories
        $allCategories = array_merge($otherTypeCategories, $newTypeCategories);

        return $this->categories()->sync($allCategories);
    }

    /**
     * Query scopes for category filtering.
     */
    public function scopeWithCategories(Builder $query, array $categoryIds): Builder
    {
        return $query->whereHas('categories', function (Builder $q) use ($categoryIds) {
            $q->whereIn('categories.id', $categoryIds);
        });
    }

    public function scopeWithCategoryTypes(Builder $query, array $types): Builder
    {
        return $query->whereHas('categories', function (Builder $q) use ($types) {
            $q->whereIn('categories.type', $types);
        });
    }

    public function scopeWithoutCategories(Builder $query): Builder
    {
        return $query->whereDoesntHave('categories');
    }

    /**
     * Helper methods for category management.
     */
    public function hasCategoryType(CategoryType $type): bool
    {
        return $this->categoriesByType($type)->exists();
    }

    public function getCategoryNames(): Collection
    {
        return $this->categories->pluck('name');
    }

    public function getCategoriesByTypeNames(): array
    {
        return $this->categories
            ->groupBy('type')
            ->map(function (Collection $categories) {
                return $categories->pluck('name')->toArray();
            })
            ->toArray();
    }

    public function getPrimaryCategory(CategoryType $type): ?Category
    {
        return $this->categoriesByType($type)
            ->wherePivot('is_primary', true)
            ->first();
    }

    public function setPrimaryCategory(Category $category): void
    {
        // Remove primary status from all categories of this type
        $this->categoriesByType($category->type)
            ->updateExistingPivot($this->categories()->getRelatedPivotKeyName(), [
                'is_primary' => false,
            ]);

        // Set the specified category as primary
        $this->categories()->updateExistingPivot($category->id, [
            'is_primary' => true,
        ]);
    }
}
